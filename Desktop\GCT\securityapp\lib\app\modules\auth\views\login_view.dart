import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';

class LoginView extends StatelessWidget {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.heroGradient,
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                // Login Form
                Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: <PERSON>um<PERSON>(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        
                        // Title
                        Text(
                          'Connexion',
                          style: GoogleFonts.inter(
                            fontSize: 32,
                            fontWeight: FontWeight.w800,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Text(
                          'Connectez-vous à votre compte GCT Security',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Login Form
                        Form(
                          key: authController.loginFormKey,
                          child: Column(
                            children: [
                              // Email Field
                              TextFormField(
                                controller: authController.emailController,
                                keyboardType: TextInputType.emailAddress,
                                validator: authController.validateEmail,
                                decoration: const InputDecoration(
                                  labelText: 'Email',
                                  hintText: '<EMAIL>',
                                  prefixIcon: Icon(Icons.email_rounded),
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Password Field
                              Obx(() => TextFormField(
                                controller: authController.passwordController,
                                obscureText: !authController.isPasswordVisible.value,
                                validator: authController.validatePassword,
                                decoration: InputDecoration(
                                  labelText: 'Mot de passe',
                                  hintText: 'Votre mot de passe',
                                  prefixIcon: const Icon(Icons.lock_rounded),
                                  suffixIcon: IconButton(
                                    onPressed: authController.togglePasswordVisibility,
                                    icon: Icon(
                                      authController.isPasswordVisible.value
                                          ? Icons.visibility_off_rounded
                                          : Icons.visibility_rounded,
                                    ),
                                  ),
                                ),
                              )),
                              
                              const SizedBox(height: 16),
                              
                              // Forgot Password
                              Align(
                                alignment: Alignment.centerRight,
                                child: TextButton(
                                  onPressed: () => _showForgotPasswordDialog(authController),
                                  child: Text(
                                    'Mot de passe oublié ?',
                                    style: GoogleFonts.inter(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              // Login Button
                              Obx(() => SizedBox(
                                width: double.infinity,
                                height: 56,
                                child: ElevatedButton(
                                  onPressed: authController.isLoading.value
                                      ? null
                                      : authController.login,
                                  child: authController.isLoading.value
                                      ? const CircularProgressIndicator(
                                          color: Colors.white,
                                        )
                                      : Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Icon(Icons.login_rounded),
                                            const SizedBox(width: 12),
                                            Text(
                                              'Se Connecter',
                                              style: GoogleFonts.inter(
                                                fontSize: 18,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                              )),



                              const SizedBox(height: 24),

                              // Register Link for Employees
                              Center(
                                child: TextButton(
                                  onPressed: () => Get.toNamed('/register'),
                                  child: RichText(
                                    text: TextSpan(
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                      children: [
                                        const TextSpan(text: 'Nouvel employé ? '),
                                        TextSpan(
                                          text: 'Créer un compte',
                                          style: GoogleFonts.inter(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: AppTheme.primaryColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              // Back to Home
                              TextButton(
                                onPressed: () => Get.offAllNamed('/home'),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.arrow_back_rounded, size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Retour à l\'accueil',
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          // Logo GCT depuis les assets
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(23),
              child: Image.asset(
                'assets/a.jpg',
                width: 96,
                height: 96,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.security_rounded,
                    size: 50,
                    color: Colors.white,
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 16),

          Text(
            'GCT Security',
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  void _showForgotPasswordDialog(AuthController authController) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          'Réinitialiser le mot de passe',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Entrez votre email pour recevoir un lien de réinitialisation',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: authController.emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'Email',
                prefixIcon: Icon(Icons.email_rounded),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Annuler'),
          ),
          Obx(() => ElevatedButton(
            onPressed: authController.isLoading.value
                ? null
                : authController.resetPassword,
            child: authController.isLoading.value
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Envoyer'),
          )),
        ],
      ),
    );
  }
}
