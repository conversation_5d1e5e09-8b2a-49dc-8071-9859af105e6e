class FactoryModel {
  final String id;
  final String name;
  final String code;
  final String address;
  final String city;
  final String region;
  final String? phoneNumber;
  final String? email;
  final String? managerName;
  final String? managerId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const FactoryModel({
    required this.id,
    required this.name,
    required this.code,
    required this.address,
    required this.city,
    required this.region,
    this.phoneNumber,
    this.email,
    this.managerName,
    this.managerId,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  String get displayName => '$name ($code)';
  String get fullAddress => '$address, $city, $region';

  FactoryModel copyWith({
    String? id,
    String? name,
    String? code,
    String? address,
    String? city,
    String? region,
    String? phoneNumber,
    String? email,
    String? managerName,
    String? managerId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return FactoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      address: address ?? this.address,
      city: city ?? this.city,
      region: region ?? this.region,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      managerName: managerName ?? this.managerName,
      managerId: managerId ?? this.managerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'address': address,
      'city': city,
      'region': region,
      'phoneNumber': phoneNumber,
      'email': email,
      'managerName': managerName,
      'managerId': managerId,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory FactoryModel.fromJson(Map<String, dynamic> json) {
    return FactoryModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      region: json['region'] ?? '',
      phoneNumber: json['phoneNumber'],
      email: json['email'],
      managerName: json['managerName'],
      managerId: json['managerId'],
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : DateTime.now(),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }

  // Données d'exemple pour les usines GCT
  static List<FactoryModel> getSampleFactories() {
    final now = DateTime.now();
    return [
      FactoryModel(
        id: 'factory_sfax',
        name: 'Usine Sfax',
        code: 'GCT-SFX',
        address: 'Zone Industrielle Sfax',
        city: 'Sfax',
        region: 'Sfax',
        phoneNumber: '+216 74 123 456',
        email: '<EMAIL>',
        managerName: 'Ahmed Ben Salem',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 365)),
      ),
      FactoryModel(
        id: 'factory_tunis',
        name: 'Usine Tunis',
        code: 'GCT-TUN',
        address: 'Zone Industrielle Ben Arous',
        city: 'Ben Arous',
        region: 'Tunis',
        phoneNumber: '+216 71 234 567',
        email: '<EMAIL>',
        managerName: 'Fatma Trabelsi',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 300)),
      ),
      FactoryModel(
        id: 'factory_sousse',
        name: 'Usine Sousse',
        code: 'GCT-SOU',
        address: 'Zone Industrielle Sousse',
        city: 'Sousse',
        region: 'Sousse',
        phoneNumber: '+216 73 345 678',
        email: '<EMAIL>',
        managerName: 'Mohamed Gharbi',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 200)),
      ),
      FactoryModel(
        id: 'factory_gabes',
        name: 'Usine Gabès',
        code: 'GCT-GAB',
        address: 'Zone Industrielle Gabès',
        city: 'Gabès',
        region: 'Gabès',
        phoneNumber: '+216 75 456 789',
        email: '<EMAIL>',
        managerName: 'Leila Mansouri',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 150)),
      ),
    ];
  }
}
