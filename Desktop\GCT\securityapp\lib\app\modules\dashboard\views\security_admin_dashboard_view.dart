import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/dashboard_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/stat_card.dart';

class SecurityAdminDashboardView extends StatelessWidget {
  const SecurityAdminDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final DashboardController dashboardController = Get.find();
    final AuthController authController = Get.find();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.heroGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              DashboardHeader(
                title: '<PERSON><PERSON>',
                subtitle: 'Gestion sécurité GCT',
                user: authController.currentUser!,
                onLogout: authController.logout,
              ),
              
              // Content
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: RefreshIndicator(
                    onRefresh: dashboardController.refreshData,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          
                          // Stats
                          Obx(() => SizedBox(
                              height: 80,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: StatCard(
                                      title: 'Réclamations',
                                      value: dashboardController.totalClaims.toString(),
                                      icon: Icons.report_problem_rounded,
                                      color: AppTheme.errorColor,
                                      subtitle: '${dashboardController.pendingClaimsCount} en attente',
                                      onTap: () => Get.toNamed('/claims'),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: StatCard(
                                      title: 'Formations',
                                      value: dashboardController.totalTrainings.toString(),
                                      icon: Icons.school_rounded,
                                      color: AppTheme.successColor,
                                      subtitle: '${dashboardController.pendingTrainingsCount} à approuver',
                                      onTap: () => Get.toNamed('/trainings'),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Quick Actions
                          Text(
                            'Actions Rapides',
                            style: GoogleFonts.inter(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          
                          const SizedBox(height: 20),
                          
                          GridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 1.3,
                            children: [
                              _buildActionCard(
                                'Réclamations',
                                Icons.report_problem_rounded,
                                AppTheme.errorColor,
                                () => Get.toNamed('/claims'),
                              ),
                              _buildActionCard(
                                'Formations',
                                Icons.school_rounded,
                                AppTheme.successColor,
                                () => Get.toNamed('/trainings'),
                              ),
                              _buildActionCard(
                                'Rapports',
                                Icons.analytics_rounded,
                                AppTheme.infoColor,
                                () => Get.toNamed('/reports'),
                              ),
                              _buildActionCard(
                                'Usines',
                                Icons.factory_rounded,
                                AppTheme.accentColor,
                                () => _showComingSoon('Gestion des usines'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),

            const SizedBox(height: 8),

            Flexible(
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    Get.snackbar(
      'Bientôt disponible',
      '$feature - Fonctionnalité en développement',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppTheme.infoColor,
      colorText: Colors.white,
    );
  }
}
