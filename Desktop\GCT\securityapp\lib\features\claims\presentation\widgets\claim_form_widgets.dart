import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../domain/constants/claim_constants.dart';

/// Widget de section de formulaire avec titre et icône
class ClaimFormSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget child;

  const ClaimFormSection({
    super.key,
    required this.title,
    required this.icon,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }
}

/// Sélecteur de type de réclamation
class ClaimTypeSelector extends StatelessWidget {
  final String selectedType;
  final ValueChanged<String> onChanged;

  const ClaimTypeSelector({
    super.key,
    required this.selectedType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: ClaimConstants.ALL_TYPES.map((type) {
        final isSelected = type == selectedType;
        return FilterChip(
          label: Text(ClaimConstants.getTypeDisplayName(type)),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) onChanged(type);
          },
          selectedColor: AppTheme.primaryColor.withOpacity(0.2),
          checkmarkColor: AppTheme.primaryColor,
        );
      }).toList(),
    );
  }
}

/// Sélecteur de priorité
class ClaimPrioritySelector extends StatelessWidget {
  final String selectedPriority;
  final ValueChanged<String> onChanged;

  const ClaimPrioritySelector({
    super.key,
    required this.selectedPriority,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedPriority,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
      ),
      items: ClaimConstants.ALL_PRIORITIES.map((priority) {
        return DropdownMenuItem(
          value: priority,
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getPriorityColor(priority),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(ClaimConstants.getPriorityDisplayName(priority)),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) onChanged(value);
      },
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case ClaimConstants.PRIORITY_LOW:
        return Colors.green;
      case ClaimConstants.PRIORITY_MEDIUM:
        return Colors.orange;
      case ClaimConstants.PRIORITY_HIGH:
        return Colors.red;
      case ClaimConstants.PRIORITY_CRITICAL:
        return Colors.purple;
      case ClaimConstants.PRIORITY_EMERGENCY:
        return Colors.black;
      default:
        return Colors.grey;
    }
  }
}

/// Sélecteur de sévérité
class ClaimSeveritySelector extends StatelessWidget {
  final String selectedSeverity;
  final ValueChanged<String> onChanged;

  const ClaimSeveritySelector({
    super.key,
    required this.selectedSeverity,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedSeverity,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
      ),
      items: ClaimConstants.ALL_SEVERITIES.map((severity) {
        return DropdownMenuItem(
          value: severity,
          child: Text(ClaimConstants.getSeverityDisplayName(severity)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) onChanged(value);
      },
    );
  }
}

/// Sélecteur de département
class ClaimDepartmentSelector extends StatelessWidget {
  final String selectedDepartment;
  final ValueChanged<String> onChanged;

  const ClaimDepartmentSelector({
    super.key,
    required this.selectedDepartment,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedDepartment,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
      ),
      items: ClaimConstants.ALL_DEPARTMENTS.map((department) {
        return DropdownMenuItem(
          value: department,
          child: Text(ClaimConstants.getDepartmentDisplayName(department)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) onChanged(value);
      },
    );
  }
}

/// Sélecteur de date et heure
class ClaimDateTimePicker extends StatelessWidget {
  final DateTime selectedDate;
  final ValueChanged<DateTime> onChanged;

  const ClaimDateTimePicker({
    super.key,
    required this.selectedDate,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _selectDateTime(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _formatDate(selectedDate),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    _formatTime(selectedDate),
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateTime(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(selectedDate),
      );

      if (time != null) {
        final newDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );
        onChanged(newDateTime);
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:'
        '${date.minute.toString().padLeft(2, '0')}';
  }
}

/// Sélecteur de personnes impliquées
class ClaimPersonsSelector extends StatefulWidget {
  final List<String> persons;
  final ValueChanged<List<String>> onChanged;

  const ClaimPersonsSelector({
    super.key,
    required this.persons,
    required this.onChanged,
  });

  @override
  State<ClaimPersonsSelector> createState() => _ClaimPersonsSelectorState();
}

class _ClaimPersonsSelectorState extends State<ClaimPersonsSelector> {
  final _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _controller,
                decoration: const InputDecoration(
                  hintText: 'Nom de la personne...',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addPerson,
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        if (widget.persons.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: widget.persons.map((person) {
              return Chip(
                label: Text(person),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removePerson(person),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  void _addPerson() {
    final person = _controller.text.trim();
    if (person.isNotEmpty && !widget.persons.contains(person)) {
      final newPersons = [...widget.persons, person];
      widget.onChanged(newPersons);
      _controller.clear();
    }
  }

  void _removePerson(String person) {
    final newPersons = widget.persons.where((p) => p != person).toList();
    widget.onChanged(newPersons);
  }
}

/// Sélecteur d'équipements
class ClaimEquipmentSelector extends StatefulWidget {
  final List<String> equipment;
  final ValueChanged<List<String>> onChanged;

  const ClaimEquipmentSelector({
    super.key,
    required this.equipment,
    required this.onChanged,
  });

  @override
  State<ClaimEquipmentSelector> createState() => _ClaimEquipmentSelectorState();
}

class _ClaimEquipmentSelectorState extends State<ClaimEquipmentSelector> {
  final _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _controller,
                decoration: const InputDecoration(
                  hintText: 'Nom ou référence de l\'équipement...',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addEquipment,
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        if (widget.equipment.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: widget.equipment.map((equipment) {
              return Chip(
                label: Text(equipment),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeEquipment(equipment),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  void _addEquipment() {
    final equipment = _controller.text.trim();
    if (equipment.isNotEmpty && !widget.equipment.contains(equipment)) {
      final newEquipment = [...widget.equipment, equipment];
      widget.onChanged(newEquipment);
      _controller.clear();
    }
  }

  void _removeEquipment(String equipment) {
    final newEquipment = widget.equipment.where((e) => e != equipment).toList();
    widget.onChanged(newEquipment);
  }
}

/// Uploader de pièces jointes
class ClaimAttachmentUploader extends StatelessWidget {
  final List<String> attachments;
  final ValueChanged<List<String>> onChanged;

  const ClaimAttachmentUploader({
    super.key,
    required this.attachments,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Zone de drop/upload
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppTheme.primaryColor,
              style: BorderStyle.solid,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            color: AppTheme.primaryColor.withOpacity(0.05),
          ),
          child: InkWell(
            onTap: _pickFiles,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_upload,
                  size: 40,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(height: 8),
                Text(
                  'Cliquez pour ajouter des fichiers',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Photos, vidéos, documents (max 10MB)',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Liste des fichiers
        if (attachments.isNotEmpty) ...[
          const SizedBox(height: 16),
          ...attachments.map((attachment) => _buildAttachmentItem(attachment)),
        ],
      ],
    );
  }

  Widget _buildAttachmentItem(String attachment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(attachment),
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              attachment.split('/').last,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          IconButton(
            onPressed: () => _removeAttachment(attachment),
            icon: const Icon(Icons.delete, color: Colors.red),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String filename) {
    final extension = filename.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.videocam;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      default:
        return Icons.attach_file;
    }
  }

  void _pickFiles() {
    // TODO: Implémenter la sélection de fichiers
    // Pour l'instant, on simule l'ajout d'un fichier
    final newAttachments = [...attachments, 'photo_incident_${DateTime.now().millisecondsSinceEpoch}.jpg'];
    onChanged(newAttachments);
  }

  void _removeAttachment(String attachment) {
    final newAttachments = attachments.where((a) => a != attachment).toList();
    onChanged(newAttachments);
  }
}
