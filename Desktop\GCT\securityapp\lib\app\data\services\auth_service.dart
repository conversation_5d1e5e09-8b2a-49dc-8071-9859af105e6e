import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/auth_user_model.dart';
import '../models/user_session_model.dart';
import 'firestore_service.dart';
import '../../routes/app_routes.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find();
  
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = Get.find();
  
  // Observable pour l'utilisateur connecté
  Rx<User?> firebaseUser = Rx<User?>(null);
  Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  
  @override
  void onInit() {
    super.onInit();
    // Écouter les changements d'authentification
    firebaseUser.bindStream(_auth.authStateChanges());
    ever(firebaseUser, _setInitialScreen);
  }

  // Définir l'écran initial selon l'état d'authentification
  void _setInitialScreen(User? user) async {
    if (user == null) {
      // Utilisateur non connecté
      currentUser.value = null;
      Get.offAllNamed(Routes.home);
    } else {
      // Utilisateur connecté, récupérer ses données
      await _loadUserData(user.uid);
    }
  }

  // Charger les données utilisateur depuis Firestore
  Future<void> _loadUserData(String uid) async {
    try {
      final userData = await _firestoreService.getUserById(uid);
      if (userData != null) {
        currentUser.value = userData;
        _redirectToDashboard(userData.role, userData.email);
      } else {
        // Utilisateur non trouvé dans Firestore
        await signOut();
      }
    } catch (e) {
      print('Erreur lors du chargement des données utilisateur: $e');
      await signOut();
    }
  }

  // Rediriger vers le bon dashboard selon le rôle et l'email
  void _redirectToDashboard(String role, [String? email]) {
    print('=== REDIRECTION DEBUG ===');
    print('Email: $email');
    print('Role: $role');
    print('========================');

    // Mapping spécifique des emails vers leurs dashboards
    if (email != null) {
      switch (email.toLowerCase()) {
        case '<EMAIL>':
          print('Redirection vers Super Admin Dashboard');
          Get.offAllNamed(Routes.superAdminDashboard);
          return;
        case '<EMAIL>':
          print('Redirection vers Security Admin Dashboard');
          Get.offAllNamed(Routes.securityAdminDashboard);
          return;
        case '<EMAIL>':
        case '<EMAIL>':
          print('Redirection vers Factory Admin Dashboard (par email)');
          Get.offAllNamed(Routes.factoryAdminDashboard);
          return;
        case '<EMAIL>':
        case '<EMAIL>':
          print('=== REDIRECTION EMPLOYEE (PAR EMAIL) ===');
          print('Email: $email');
          print('Role: $role');
          print('Route: ${Routes.employeeDashboard}');
          print('======================================');
          Get.offAllNamed(Routes.employeeDashboard);
          return;
      }
    }

    // Fallback sur le rôle si l'email n'est pas reconnu
    print('Fallback sur le rôle: ${role.toLowerCase()}');
    switch (role.toLowerCase()) {
      case 'super_admin':
      case 'superadmin':
        print('Redirection vers Super Admin Dashboard (par rôle)');
        Get.offAllNamed(Routes.superAdminDashboard);
        break;
      case 'admin_securite_gct':
      case 'security_admin':
      case 'securityadmin':
        print('Redirection vers Security Admin Dashboard (par rôle)');
        Get.offAllNamed(Routes.securityAdminDashboard);
        break;
      case 'admin_usine':
      case 'factory_admin':
      case 'factoryadmin':
        print('Redirection vers Factory Admin Dashboard (par rôle)');
        Get.offAllNamed(Routes.factoryAdminDashboard);
        break;
      case 'employe':
      case 'employee':
        print('=== REDIRECTION EMPLOYEE ===');
        print('Role: $role');
        print('Email: $email');
        print('Route: ${Routes.employeeDashboard}');
        print('===========================');
        Get.offAllNamed(Routes.employeeDashboard);
        break;
      default:
        print('=== REDIRECTION DÉFAUT ===');
        print('Role non reconnu: $role');
        print('Email: $email');
        print('Redirection vers Home');
        print('======================');
        Get.offAllNamed(Routes.home);
    }
  }

  // Connexion avec email et mot de passe
  Future<UserModel?> signInWithEmailAndPassword(String email, String password) async {
    try {
      // Essayer d'abord l'authentification locale pour les tests
      final localUser = await _tryLocalAuthentication(email, password);
      if (localUser != null) {
        currentUser.value = localUser;
        _redirectToDashboard(localUser.role, localUser.email);
        return localUser;
      }

      // Si l'authentification locale échoue, essayer Firebase
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        await _loadUserData(credential.user!.uid);
        return currentUser.value;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Erreur de connexion: $e';
    }
  }

  // Authentification locale pour les tests
  Future<UserModel?> _tryLocalAuthentication(String email, String password) async {
    // Utilisateurs de test locaux
    final testUsers = {
      '<EMAIL>': {
        'password': 'admin123',
        'user': UserModel(
          id: 'admin_001',
          email: '<EMAIL>',
          firstName: 'Super',
          lastName: 'Admin',
          role: 'super_admin',
          factory: null,
          createdAt: DateTime.now(),
        ),
      },
      '<EMAIL>': {
        'password': 'security123',
        'user': UserModel(
          id: 'security_001',
          email: '<EMAIL>',
          firstName: 'Security',
          lastName: 'Admin',
          role: 'admin_securite_gct',
          factory: null,
          createdAt: DateTime.now(),
        ),
      },
      '<EMAIL>': {
        'password': 'admin123',
        'user': UserModel(
          id: 'factory_admin_sfax',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'Sfax',
          role: 'admin_usine',
          factory: 'Sfax',
          createdAt: DateTime.now(),
        ),
      },
      '<EMAIL>': {
        'password': 'admin123',
        'user': UserModel(
          id: 'factory_admin_gabes',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'Gabès',
          role: 'admin_usine',
          factory: 'Gabès',
          createdAt: DateTime.now(),
        ),
      },
      '<EMAIL>': {
        'password': 'employee123',
        'user': UserModel(
          id: 'employee_sfax',
          email: '<EMAIL>',
          firstName: 'Employé',
          lastName: 'Sfax',
          role: 'employe',
          factory: 'Sfax',
          createdAt: DateTime.now(),
        ),
      },
      '<EMAIL>': {
        'password': 'employee123',
        'user': UserModel(
          id: 'employee_gabes',
          email: '<EMAIL>',
          firstName: 'Employé',
          lastName: 'Gabès',
          role: 'employe',
          factory: 'Gabès',
          createdAt: DateTime.now(),
        ),
      },
    };

    final userEmail = email.toLowerCase().trim();
    final userData = testUsers[userEmail];

    if (userData != null && userData['password'] == password) {
      // Simuler un délai de connexion
      await Future.delayed(const Duration(milliseconds: 500));
      return userData['user'] as UserModel;
    }

    return null;
  }

  // Inscription (uniquement pour les employés)
  Future<UserModel?> registerEmployee({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String factory,
  }) async {
    try {
      // Créer le compte Firebase Auth
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // Créer le profil utilisateur dans Firestore
        final user = UserModel(
          id: credential.user!.uid,
          email: email.trim(),
          firstName: firstName.trim(),
          lastName: lastName.trim(),
          role: 'employe',
          factory: factory,
          createdAt: DateTime.now(),
        );

        await _firestoreService.createUser(user);
        currentUser.value = user;
        
        return user;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Erreur lors de l\'inscription: $e';
    }
  }

  // Déconnexion
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      currentUser.value = null;
      Get.offAllNamed('/home');
    } catch (e) {
      throw 'Erreur lors de la déconnexion: $e';
    }
  }

  // Réinitialisation du mot de passe
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Erreur lors de la réinitialisation: $e';
    }
  }

  // Gestion des erreurs Firebase Auth
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'Aucun utilisateur trouvé avec cet email.';
      case 'wrong-password':
        return 'Mot de passe incorrect.';
      case 'email-already-in-use':
        return 'Cet email est déjà utilisé.';
      case 'weak-password':
        return 'Le mot de passe est trop faible.';
      case 'invalid-email':
        return 'Format d\'email invalide.';
      case 'user-disabled':
        return 'Ce compte a été désactivé.';
      case 'too-many-requests':
        return 'Trop de tentatives. Réessayez plus tard.';
      case 'operation-not-allowed':
        return 'Opération non autorisée.';
      default:
        return 'Erreur d\'authentification: ${e.message}';
    }
  }

  // Vérifier si l'utilisateur est connecté
  bool get isLoggedIn => firebaseUser.value != null;
  
  // Obtenir l'utilisateur actuel
  UserModel? get user => currentUser.value;
  
  // Obtenir l'ID de l'utilisateur actuel
  String? get userId => firebaseUser.value?.uid;
  
  // Vérifier les permissions selon le rôle
  bool hasPermission(String permission) {
    final user = currentUser.value;
    if (user == null) return false;
    
    switch (user.role) {
      case 'super_admin':
        return true; // Accès total
      case 'admin_securite_gct':
        return ['view_all_claims', 'manage_trainings', 'view_reports'].contains(permission);
      case 'admin_usine':
        return ['view_factory_claims', 'manage_factory_trainings'].contains(permission);
      case 'employe':
        return ['create_claim', 'view_own_claims', 'request_training'].contains(permission);
      default:
        return false;
    }
  }

  // Nouvelles méthodes pour la gestion des utilisateurs et sessions
  Future<List<AuthUserModel>> getAllAuthUsers() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('auth_users')
          .orderBy('displayName')
          .get();

      return snapshot.docs
          .map((doc) => AuthUserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Erreur lors de la récupération des utilisateurs: $e');
      return [];
    }
  }

  Future<List<UserSessionModel>> getActiveSessions() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('user_sessions')
          .where('isActive', isEqualTo: true)
          .orderBy('loginTime', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => UserSessionModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Erreur lors de la récupération des sessions: $e');
      return [];
    }
  }

  Future<AuthUserModel?> getAuthUserByEmail(String email) async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('auth_users')
          .where('email', isEqualTo: email.toLowerCase())
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return AuthUserModel.fromDocument(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur: $e');
      return null;
    }
  }

  // Méthode utilitaire pour afficher tous les utilisateurs avec leurs mots de passe
  Future<void> printAllUsersWithPasswords() async {
    final users = await getAllAuthUsers();
    print('\n=== LISTE DES UTILISATEURS AVEC MOTS DE PASSE ===');
    print('Total: ${users.length} utilisateurs\n');

    // Grouper par rôle
    final adminUsers = users.where((u) => u.role == 'super_admin' || u.role == 'admin_securite_gct').toList();
    final factoryAdmins = users.where((u) => u.role == 'admin_usine').toList();
    final employees = users.where((u) => u.role == 'employe').toList();

    if (adminUsers.isNotEmpty) {
      print('--- ADMINISTRATEURS ---');
      for (final user in adminUsers) {
        print('Email: ${user.email}');
        print('Mot de passe: ${user.password}');
        print('Rôle: ${user.roleDisplayName}');
        print('Actif: ${user.isActive ? 'Oui' : 'Non'}');
        print('');
      }
    }

    if (factoryAdmins.isNotEmpty) {
      print('--- ADMINISTRATEURS D\'USINE ---');
      for (final user in factoryAdmins) {
        print('Email: ${user.email}');
        print('Mot de passe: ${user.password}');
        print('Rôle: ${user.roleDisplayName}');
        print('Usine: ${user.factory ?? 'N/A'}');
        print('Actif: ${user.isActive ? 'Oui' : 'Non'}');
        print('');
      }
    }

    if (employees.isNotEmpty) {
      print('--- EMPLOYÉS ---');
      // Grouper par usine
      final employeesByFactory = <String, List<AuthUserModel>>{};
      for (final emp in employees) {
        final factory = emp.factory ?? 'Sans usine';
        employeesByFactory.putIfAbsent(factory, () => []).add(emp);
      }

      for (final entry in employeesByFactory.entries) {
        print('** ${entry.key} **');
        for (final user in entry.value) {
          print('Email: ${user.email}');
          print('Mot de passe: ${user.password}');
          print('Nom: ${user.displayName}');
          print('Département: ${user.department ?? 'N/A'}');
          print('Poste: ${user.position ?? 'N/A'}');
          print('Actif: ${user.isActive ? 'Oui' : 'Non'}');
          print('---');
        }
        print('');
      }
    }

    print('=== FIN DE LA LISTE ===\n');
  }
}
