// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCLvB4w4_00A49MhulVfQIYZSmzGhcUYTI',
    appId: '1:325296538989:web:abcdef123456789012345678',
    messagingSenderId: '325296538989',
    projectId: 'securityapp-8b742',
    authDomain: 'securityapp-8b742.firebaseapp.com',
    storageBucket: 'securityapp-8b742.appspot.com',
    measurementId: 'G-ABCDEFGHIJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCLvB4w4_00A49MhulVfQIYZSmzGhcUYTI',
    appId: '1:325296538989:android:abcdef123456789012345678',
    messagingSenderId: '325296538989',
    projectId: 'securityapp-8b742',
    storageBucket: 'securityapp-8b742.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBvOkBwNzEDtDtlEeJpqZ6rIHjMhLaGYQM',
    appId: '1:123456789012:ios:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'securityapp-8b742',
    storageBucket: 'securityapp-8b742.appspot.com',
    iosBundleId: 'com.gct.securityapp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBvOkBwNzEDtDtlEeJpqZ6rIHjMhLaGYQM',
    appId: '1:123456789012:macos:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'securityapp-8b742',
    storageBucket: 'securityapp-8b742.appspot.com',
    iosBundleId: 'com.gct.securityapp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCLvB4w4_00A49MhulVfQIYZSmzGhcUYTI',
    appId: '1:325296538989:windows:abcdef123456789012345678',
    messagingSenderId: '325296538989',
    projectId: 'securityapp-8b742',
  );
}
