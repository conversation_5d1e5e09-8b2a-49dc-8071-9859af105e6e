abstract class Routes {
  Routes._();
  
  static const home = '/home';
  static const login = '/login';
  static const register = '/register';
  
  // Dashboards
  static const superAdminDashboard = '/dashboard/super-admin';
  static const securityAdminDashboard = '/dashboard/security-admin';
  static const factoryAdminDashboard = '/dashboard/factory-admin';
  static const employeeDashboard = '/dashboard/employee';
  
  // Claims
  static const claims = '/claims';
  static const createClaim = '/claims/create';
  static const claimDetails = '/claims/details';
  
  // Trainings
  static const trainings = '/trainings';
  static const requestTraining = '/trainings/request';
  static const trainingDetails = '/trainings/details';
  
  // Users Management
  static const users = '/users';
  static const userDetails = '/users/details';
  
  // Reports
  static const reports = '/reports';
  
  // Settings
  static const settings = '/settings';
  static const profile = '/profile';
}
