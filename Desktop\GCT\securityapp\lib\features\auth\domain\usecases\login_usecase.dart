import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class LoginUseCase {
  final AuthRepository repository;

  LoginUseCase(this.repository);

  Future<Either<Failure, User>> call(LoginParams params) async {
    return await repository.login(
      email: params.email,
      password: params.password,
      deviceId: params.deviceId,
      deviceName: params.deviceName,
    );
  }
}

class LoginParams {
  final String email;
  final String password;
  final String? deviceId;
  final String? deviceName;

  LoginParams({
    required this.email,
    required this.password,
    this.deviceId,
    this.deviceName,
  });
}
