import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/claim.dart';
import '../repositories/claims_repository.dart';

class UpdateClaimUseCase {
  final ClaimsRepository repository;

  UpdateClaimUseCase(this.repository);

  Future<Either<Failure, Claim>> call(UpdateClaimParams params) async {
    return await repository.updateClaim(params.claim);
  }
}

class UpdateClaimParams {
  final Claim claim;

  UpdateClaimParams({required this.claim});
}
