import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/factory_model.dart';

class RegisterView extends StatelessWidget {
  const RegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.heroGradient,
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                // Register Form
                Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        
                        // Title
                        Text(
                          'Créer un Compte',
                          style: GoogleFonts.inter(
                            fontSize: 32,
                            fontWeight: FontWeight.w800,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Text(
                          'Rejoignez la plateforme GCT Security en tant qu\'employé',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Register Form
                        Form(
                          key: authController.registerFormKey,
                          child: Column(
                            children: [
                              // First Name Field
                              TextFormField(
                                controller: authController.firstNameController,
                                textCapitalization: TextCapitalization.words,
                                validator: authController.validateName,
                                decoration: const InputDecoration(
                                  labelText: 'Prénom',
                                  hintText: 'Votre prénom',
                                  prefixIcon: Icon(Icons.person_rounded),
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Last Name Field
                              TextFormField(
                                controller: authController.lastNameController,
                                textCapitalization: TextCapitalization.words,
                                validator: authController.validateName,
                                decoration: const InputDecoration(
                                  labelText: 'Nom',
                                  hintText: 'Votre nom de famille',
                                  prefixIcon: Icon(Icons.person_outline_rounded),
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Email Field
                              TextFormField(
                                controller: authController.emailController,
                                keyboardType: TextInputType.emailAddress,
                                validator: authController.validateEmail,
                                decoration: const InputDecoration(
                                  labelText: 'Email',
                                  hintText: '<EMAIL>',
                                  prefixIcon: Icon(Icons.email_rounded),
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Factory Dropdown
                              Obx(() => DropdownButtonFormField<String>(
                                value: authController.selectedFactory.value,
                                decoration: const InputDecoration(
                                  labelText: 'Usine',
                                  hintText: 'Sélectionnez votre usine',
                                  prefixIcon: Icon(Icons.factory_rounded),
                                ),
                                items: FactoryModel.defaultFactories.map((factory) {
                                  return DropdownMenuItem<String>(
                                    value: factory.id,
                                    child: Text(
                                      factory.displayName,
                                      style: GoogleFonts.inter(fontSize: 16),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  authController.selectedFactory.value = value;
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Veuillez sélectionner une usine';
                                  }
                                  return null;
                                },
                              )),
                              
                              const SizedBox(height: 24),
                              
                              // Password Field
                              Obx(() => TextFormField(
                                controller: authController.passwordController,
                                obscureText: !authController.isPasswordVisible.value,
                                validator: authController.validatePassword,
                                decoration: InputDecoration(
                                  labelText: 'Mot de passe',
                                  hintText: 'Minimum 6 caractères',
                                  prefixIcon: const Icon(Icons.lock_rounded),
                                  suffixIcon: IconButton(
                                    onPressed: authController.togglePasswordVisibility,
                                    icon: Icon(
                                      authController.isPasswordVisible.value
                                          ? Icons.visibility_off_rounded
                                          : Icons.visibility_rounded,
                                    ),
                                  ),
                                ),
                              )),
                              
                              const SizedBox(height: 24),
                              
                              // Confirm Password Field
                              Obx(() => TextFormField(
                                controller: authController.confirmPasswordController,
                                obscureText: !authController.isConfirmPasswordVisible.value,
                                validator: authController.validateConfirmPassword,
                                decoration: InputDecoration(
                                  labelText: 'Confirmer le mot de passe',
                                  hintText: 'Répétez votre mot de passe',
                                  prefixIcon: const Icon(Icons.lock_outline_rounded),
                                  suffixIcon: IconButton(
                                    onPressed: authController.toggleConfirmPasswordVisibility,
                                    icon: Icon(
                                      authController.isConfirmPasswordVisible.value
                                          ? Icons.visibility_off_rounded
                                          : Icons.visibility_rounded,
                                    ),
                                  ),
                                ),
                              )),
                              
                              const SizedBox(height: 32),
                              
                              // Register Button
                              Obx(() => SizedBox(
                                width: double.infinity,
                                height: 56,
                                child: ElevatedButton(
                                  onPressed: authController.isLoading.value
                                      ? null
                                      : authController.register,
                                  child: authController.isLoading.value
                                      ? const CircularProgressIndicator(
                                          color: Colors.white,
                                        )
                                      : Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Icon(Icons.person_add_rounded),
                                            const SizedBox(width: 12),
                                            Text(
                                              'Créer mon Compte',
                                              style: GoogleFonts.inter(
                                                fontSize: 18,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                              )),
                              
                              const SizedBox(height: 24),
                              
                              // Divider
                              Row(
                                children: [
                                  Expanded(
                                    child: Divider(
                                      color: AppTheme.textSecondaryColor.withOpacity(0.3),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'ou',
                                      style: GoogleFonts.inter(
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Divider(
                                      color: AppTheme.textSecondaryColor.withOpacity(0.3),
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Login Button
                              SizedBox(
                                width: double.infinity,
                                height: 56,
                                child: OutlinedButton(
                                  onPressed: () => Get.toNamed('/login'),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.login_rounded),
                                      const SizedBox(width: 12),
                                      Text(
                                        'J\'ai déjà un Compte',
                                        style: GoogleFonts.inter(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              // Back to Home
                              TextButton(
                                onPressed: () => Get.offAllNamed('/home'),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.arrow_back_rounded, size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Retour à l\'accueil',
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          // Logo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.person_add_rounded,
              size: 40,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Rejoignez GCT',
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }
}
