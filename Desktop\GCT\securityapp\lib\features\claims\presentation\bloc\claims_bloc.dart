import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_claims_usecase.dart';
import '../../domain/usecases/create_claim_usecase.dart';
import '../../domain/usecases/update_claim_usecase.dart';
import '../../domain/usecases/delete_claim_usecase.dart';
import '../../domain/repositories/claims_repository.dart';
import '../../domain/entities/claim.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../auth/domain/entities/user.dart';
import 'claims_event.dart';
import 'claims_state.dart';

class ClaimsBloc extends Bloc<ClaimsEvent, ClaimsState> {
  final GetClaimsUseCase getClaimsUseCase;
  final CreateClaimUseCase createClaimUseCase;
  final UpdateClaimUseCase updateClaimUseCase;
  final DeleteClaimUseCase deleteClaimUseCase;
  final ClaimsRepository claimsRepository;

  ClaimsBloc({
    required this.getClaimsUseCase,
    required this.createClaimUseCase,
    required this.updateClaimUseCase,
    required this.deleteClaimUseCase,
    required this.claimsRepository,
  }) : super(const ClaimsInitial()) {
    on<ClaimsLoadRequested>(_onClaimsLoadRequested);
    on<ClaimCreateRequested>(_onClaimCreateRequested);
    on<ClaimUpdateRequested>(_onClaimUpdateRequested);
    on<ClaimDeleteRequested>(_onClaimDeleteRequested);
    on<ClaimStatusUpdateRequested>(_onClaimStatusUpdateRequested);
    on<ClaimCommentAddRequested>(_onClaimCommentAddRequested);
    on<ClaimsFilterChanged>(_onClaimsFilterChanged);
    on<ClaimsStatsRequested>(_onClaimsStatsRequested);
  }

  Future<void> _onClaimsLoadRequested(
    ClaimsLoadRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    if (event.refresh || state is! ClaimsLoaded) {
      emit(const ClaimsLoading());
    }

    final params = GetClaimsParams(
      factoryId: event.factoryId,
      status: event.status,
      type: event.type,
      assignedToId: event.assignedToId,
      limit: 20,
    );

    final result = await getClaimsUseCase(params);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (claims) => emit(ClaimsLoaded(
        claims: claims,
        currentFactoryId: event.factoryId,
        currentStatus: event.status,
        currentType: event.type,
        currentAssignedToId: event.assignedToId,
      )),
    );
  }

  Future<void> _onClaimCreateRequested(
    ClaimCreateRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    emit(const ClaimCreating());

    final currentUser = FirebaseService.currentUserId;
    if (currentUser == null) {
      emit(const ClaimsError(message: 'Utilisateur non connecté'));
      return;
    }

    final params = CreateClaimParams(
      title: event.title,
      description: event.description,
      type: event.type,
      priority: event.priority,
      reporterId: currentUser,
      reporterName: 'Current User', // TODO: Get from auth state
      factoryId: event.factoryId,
      factoryName: event.factoryName,
      location: event.location,
      latitude: event.latitude,
      longitude: event.longitude,
      incidentDate: event.incidentDate,
      attachments: event.attachments,
    );

    final result = await createClaimUseCase(params);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (claim) {
        emit(ClaimCreated(claim: claim));
        // Reload claims to show the new one
        add(const ClaimsLoadRequested(refresh: true));
      },
    );
  }

  Future<void> _onClaimUpdateRequested(
    ClaimUpdateRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    emit(ClaimUpdating(claimId: event.claim.id));

    final params = UpdateClaimParams(claim: event.claim);
    final result = await updateClaimUseCase(params);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (claim) {
        emit(ClaimUpdated(claim: claim));
        // Update the claims list
        if (state is ClaimsLoaded) {
          final currentState = state as ClaimsLoaded;
          final updatedClaims = currentState.claims.map((c) {
            return c.id == claim.id ? claim : c;
          }).toList();
          emit(currentState.copyWith(claims: updatedClaims));
        }
      },
    );
  }

  Future<void> _onClaimDeleteRequested(
    ClaimDeleteRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    emit(ClaimDeleting(claimId: event.claimId));

    final params = DeleteClaimParams(claimId: event.claimId);
    final result = await deleteClaimUseCase(params);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (_) {
        emit(ClaimDeleted(claimId: event.claimId));
        // Remove from claims list
        if (state is ClaimsLoaded) {
          final currentState = state as ClaimsLoaded;
          final updatedClaims = currentState.claims
              .where((c) => c.id != event.claimId)
              .toList();
          emit(currentState.copyWith(claims: updatedClaims));
        }
      },
    );
  }

  Future<void> _onClaimStatusUpdateRequested(
    ClaimStatusUpdateRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    if (state is ClaimsLoaded) {
      final currentState = state as ClaimsLoaded;
      final claim = currentState.claims.firstWhere((c) => c.id == event.claimId);
      
      final updatedClaim = claim.copyWith(
        status: event.newStatus,
        assignedToId: event.assignedToId,
        assignedToName: event.assignedToName,
        resolvedAt: event.newStatus == 'RESOLVED' ? DateTime.now() : null,
      );

      add(ClaimUpdateRequested(claim: updatedClaim));
    }
  }

  Future<void> _onClaimCommentAddRequested(
    ClaimCommentAddRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    emit(ClaimCommentAdding(claimId: event.claimId));

    final comment = ClaimComment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      claimId: event.claimId,
      authorId: event.authorId,
      authorName: event.authorName,
      content: event.content,
      createdAt: DateTime.now(),
      attachments: event.attachments ?? [],
    );

    final result = await claimsRepository.addComment(event.claimId, comment);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (claim) {
        emit(ClaimCommentAdded(claim: claim));
        // Update the claims list
        if (state is ClaimsLoaded) {
          final currentState = state as ClaimsLoaded;
          final updatedClaims = currentState.claims.map((c) {
            return c.id == claim.id ? claim : c;
          }).toList();
          emit(currentState.copyWith(claims: updatedClaims));
        }
      },
    );
  }

  Future<void> _onClaimsFilterChanged(
    ClaimsFilterChanged event,
    Emitter<ClaimsState> emit,
  ) async {
    add(ClaimsLoadRequested(
      factoryId: event.factoryId,
      status: event.status,
      type: event.type,
      assignedToId: event.assignedToId,
      refresh: true,
    ));
  }

  Future<void> _onClaimsStatsRequested(
    ClaimsStatsRequested event,
    Emitter<ClaimsState> emit,
  ) async {
    final result = await claimsRepository.getClaimsStats(factoryId: event.factoryId);

    result.fold(
      (failure) => emit(ClaimsError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (stats) {
        if (state is ClaimsLoaded) {
          final currentState = state as ClaimsLoaded;
          emit(currentState.copyWith(stats: stats));
        }
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return failure.message;
      case NetworkFailure:
        return 'Erreur de connexion. Vérifiez votre connexion internet.';
      case AuthorizationFailure:
        return 'Vous n\'avez pas les permissions nécessaires.';
      case ValidationFailure:
        return 'Données invalides.';
      case FileFailure:
        return 'Erreur de fichier.';
      default:
        return 'Une erreur inattendue s\'est produite.';
    }
  }
}
