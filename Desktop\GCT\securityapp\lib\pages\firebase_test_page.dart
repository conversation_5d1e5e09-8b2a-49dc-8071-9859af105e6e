import 'package:flutter/material.dart';
import '../services/firebase_user_service.dart';
import '../theme/app_colors.dart';
import 'firebase_auth_page.dart';

class FirebaseTestPage extends StatefulWidget {
  const FirebaseTestPage({super.key});

  @override
  State<FirebaseTestPage> createState() => _FirebaseTestPageState();
}

class _FirebaseTestPageState extends State<FirebaseTestPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _setupResult;

  Future<void> _createFirebaseUsers() async {
    setState(() {
      _isLoading = true;
      _setupResult = null;
    });

    try {
      final result = await FirebaseUserService.initializePredefinedUsers();
      setState(() {
        _setupResult = result;
      });
    } catch (e) {
      setState(() {
        _setupResult = {
          'success': false,
          'errors': ['Erreur: $e'],
          'created': [],
          'existing': [],
          'total': 0,
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: const Text('Test Firebase'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.cloud_sync,
                    size: 64,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Créer les Comptes Firebase',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.grey900,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Cliquez sur le bouton ci-dessous pour créer automatiquement tous les comptes utilisateurs dans Firebase',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.grey600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Comptes à créer
            const Text(
              'Comptes qui seront créés :',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.grey900,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView(
                children: [
                  _buildUserCard('👑', 'Super Admin', '<EMAIL>', 'admin123'),
                  _buildUserCard('🛡️', 'Security Admin', '<EMAIL>', 'security123'),
                  _buildUserCard('🏭', 'Factory Admin Gabès', '<EMAIL>', 'gabes123'),
                  _buildUserCard('🏭', 'Factory Admin Sfax', '<EMAIL>', 'sfax123'),
                  _buildUserCard('👷', 'Employé Gabès', '<EMAIL>', 'emp123'),
                  _buildUserCard('👷', 'Employé Sfax', '<EMAIL>', 'emp123'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Bouton principal
            SizedBox(
              height: 56,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createFirebaseUsers,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add_circle, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'CRÉER LES COMPTES FIREBASE',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Résultats
            if (_setupResult != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _setupResult!['success'] 
                      ? AppColors.success.withOpacity(0.1)
                      : AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _setupResult!['success'] 
                        ? AppColors.success
                        : AppColors.error,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _setupResult!['success'] ? Icons.check_circle : Icons.error,
                          color: _setupResult!['success'] ? AppColors.success : AppColors.error,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _setupResult!['success'] ? 'Succès !' : 'Erreur',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: _setupResult!['success'] ? AppColors.success : AppColors.error,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Créés: ${_setupResult!['created'].length}'),
                    Text('Existants: ${_setupResult!['existing'].length}'),
                    if (_setupResult!['errors'].isNotEmpty)
                      Text('Erreurs: ${_setupResult!['errors'].length}'),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Bouton pour aller à la connexion
              if (_setupResult!['success'] || _setupResult!['existing'].isNotEmpty)
                SizedBox(
                  height: 56,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FirebaseAuthPage(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.login, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'ALLER À LA CONNEXION',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserCard(String emoji, String role, String email, String password) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  role,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  email,
                  style: TextStyle(
                    color: AppColors.grey600,
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Mot de passe: $password',
                  style: TextStyle(
                    color: AppColors.grey500,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
