import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../auth/data/datasources/auth_demo_datasource.dart';

class DemoCredentialsDialog extends StatelessWidget {
  const DemoCredentialsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final credentials = AuthDemoDataSource.getDemoCredentials();

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: 8),
          const Text('Identifiants de Démonstration'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Utilisez ces identifiants pour tester l\'application :',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            ...credentials.entries.map((entry) => _buildCredentialCard(
              context,
              email: entry.key,
              password: entry.value,
            )),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.warningColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber,
                    color: AppTheme.warningColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Mode démonstration uniquement. En production, utilisez vos vrais identifiants.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Widget _buildCredentialCard(
    BuildContext context, {
    required String email,
    required String password,
  }) {
    String role = '';
    String description = '';
    Color roleColor = AppTheme.textSecondaryColor;

    if (email.contains('admin@')) {
      role = 'Super Admin';
      description = 'Accès complet à toutes les fonctionnalités';
      roleColor = AppTheme.errorColor;
    } else if (email.contains('security@')) {
      role = 'Admin Sécurité GCT';
      description = 'Gestion de la sécurité dans toutes les usines';
      roleColor = AppTheme.warningColor;
    } else if (email.contains('manager')) {
      role = 'Admin d\'Usine';
      description = 'Gestion d\'une usine spécifique';
      roleColor = AppTheme.infoColor;
    } else if (email.contains('employee')) {
      role = 'Employé';
      description = 'Accès de base pour les employés';
      roleColor = AppTheme.successColor;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: roleColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: roleColor.withOpacity(0.3)),
                  ),
                  child: Text(
                    role,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: roleColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildCopyableField(context, 'Email', email),
            const SizedBox(height: 8),
            _buildCopyableField(context, 'Mot de passe', password),
          ],
        ),
      ),
    );
  }

  Widget _buildCopyableField(BuildContext context, String label, String value) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: value));
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('$label copié dans le presse-papiers'),
                duration: const Duration(seconds: 2),
              ),
            );
          },
          icon: const Icon(Icons.copy, size: 16),
          tooltip: 'Copier',
        ),
      ],
    );
  }

  static void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const DemoCredentialsDialog(),
    );
  }
}
