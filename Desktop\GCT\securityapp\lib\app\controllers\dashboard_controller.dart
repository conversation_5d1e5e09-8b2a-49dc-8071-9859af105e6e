import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../data/services/firestore_service.dart';
import '../data/services/auth_service.dart';
import '../data/models/user_model.dart';
import '../data/models/claim_model.dart';
import '../data/models/training_model.dart';
import '../data/models/factory_model.dart';

class DashboardController extends GetxController {
  static DashboardController get instance => Get.find();
  
  final FirestoreService _firestoreService = Get.find();
  final AuthService _authService = Get.find();
  
  // State
  var isLoading = false.obs;
  var globalStats = <String, int>{}.obs;
  var factoryStats = <String, int>{}.obs;

  // Data lists
  var claims = <ClaimModel>[].obs;
  var trainings = <TrainingModel>[].obs;
  var users = <UserModel>[].obs;
  var factories = <FactoryModel>[].obs;
  
  @override
  void onInit() {
    super.onInit();

    // Charger les données immédiatement si l'utilisateur est déjà connecté
    loadDashboardData();

    // Écouter les changements d'état de l'utilisateur
    _authService.currentUser.listen((user) {
      if (user != null) {
        print('=== UTILISATEUR CONNECTÉ - RECHARGEMENT ===');
        print('Email: ${user.email}');
        print('Rôle: ${user.role}');
        print('==========================================');
        loadDashboardData();
      } else {
        print('=== UTILISATEUR DÉCONNECTÉ ===');
        _resetData();
      }
    });
  }

  void _resetData() {
    claims.clear();
    users.clear();
    trainings.clear();
    factories.clear();
    globalStats.clear();
    factoryStats.clear();
    isLoading.value = false;
    update();
  }

  // Getters
  UserModel? get currentUser => _authService.currentUser.value;
  String get userRole => currentUser?.role ?? '';
  String? get userFactory => currentUser?.factory;

  // Load dashboard data based on user role
  Future<void> loadDashboardData() async {
    try {
      isLoading.value = true;
      update();

      // Initialiser les données par défaut (y compris les données de test pour Gabès)
      await _firestoreService.initializeDefaultData();

      // Forcer le chargement direct depuis Firebase
      print('Tentative de chargement direct depuis Firebase...');
      try {
        await _loadDataDirectly();
        print('✅ Données chargées directement depuis Firebase');
      } catch (directError) {
        print('❌ Erreur chargement direct Firebase: $directError');
        print('Utilisation du chargement par rôle...');
      }

      print('=== DEBUG DASHBOARD ===');
      print('Current user: ${currentUser?.email ?? "null"} (${currentUser?.role ?? "null"})');
      print('User role: $userRole');
      print('User factory: $userFactory');
      print('======================');

      switch (userRole) {
        case 'super_admin':
          print('Chargement des données Super Admin...');
          await _loadSuperAdminData();
          break;
        case 'admin_securite_gct':
          print('Chargement des données Admin Sécurité...');
          await _loadSecurityAdminData();
          break;
        case 'admin_usine':
          print('Chargement des données Admin Usine...');
          await _loadFactoryAdminData();
          break;
        case 'employe':
          print('Chargement des données Employé...');
          await _loadEmployeeData();
          break;
        default:
          print('Rôle non reconnu: $userRole');
      }

      await loadStats();

      print('=== DONNÉES CHARGÉES ===');
      print('Claims: ${claims.length}');
      print('Users: ${users.length}');
      print('Trainings: ${trainings.length}');
      print('Factories: ${factories.length}');
      print('Global stats: $globalStats');
      print('========================');

    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Load data for Super Admin
  Future<void> _loadSuperAdminData() async {
    print('Chargement des données Super Admin...');

    try {
      // Charger les données directement depuis Firestore
      await _loadDataDirectly();

      // Puis écouter les changements en temps réel
      _firestoreService.getAllClaims().listen((data) {
        print('Réclamations reçues via stream: ${data.length}');
        claims.value = data;
        update();
      });

      _firestoreService.getAllTrainings().listen((data) {
        print('Formations reçues via stream: ${data.length}');
        trainings.value = data;
        update();
      });

      _firestoreService.getAllUsers().listen((data) {
        print('Utilisateurs reçus via stream: ${data.length}');
        users.value = data;
        update();
      });

      _firestoreService.getAllFactories().listen((data) {
        print('Usines reçues via stream: ${data.length}');
        factories.value = data;
        update();
      });

    } catch (e) {
      print('Erreur lors du chargement des données: $e');
      // En cas d'erreur, ajouter des données de test
      _addTestData();
    }
  }

  // Charger les données directement depuis Firestore
  Future<void> _loadDataDirectly() async {
    print('Chargement direct depuis Firestore...');

    try {
      final firestore = FirebaseFirestore.instance;

      // Charger les utilisateurs
      final usersSnapshot = await firestore.collection('users').get();
      final usersList = usersSnapshot.docs.map((doc) {
        try {
          return UserModel.fromDocument(doc);
        } catch (e) {
          print('Erreur lors du parsing utilisateur ${doc.id}: $e');
          return null;
        }
      }).where((user) => user != null).cast<UserModel>().toList();

      users.value = usersList;
      print('Utilisateurs chargés directement: ${usersList.length}');

      // Charger les réclamations
      final claimsSnapshot = await firestore.collection('claims').get();
      final claimsList = claimsSnapshot.docs.map((doc) {
        try {
          return ClaimModel.fromDocument(doc);
        } catch (e) {
          print('Erreur lors du parsing réclamation ${doc.id}: $e');
          return null;
        }
      }).where((claim) => claim != null).cast<ClaimModel>().toList();

      claims.value = claimsList;
      print('Réclamations chargées directement: ${claimsList.length}');

      // Charger les formations
      final trainingsSnapshot = await firestore.collection('trainings').get();
      final trainingsList = trainingsSnapshot.docs.map((doc) {
        try {
          return TrainingModel.fromDocument(doc);
        } catch (e) {
          print('Erreur lors du parsing formation ${doc.id}: $e');
          return null;
        }
      }).where((training) => training != null).cast<TrainingModel>().toList();

      trainings.value = trainingsList;
      print('Formations chargées directement: ${trainingsList.length}');

      // Charger les usines
      final factoriesSnapshot = await firestore.collection('factories').get();
      final factoriesList = factoriesSnapshot.docs.map((doc) {
        try {
          return FactoryModel.fromDocument(doc);
        } catch (e) {
          print('Erreur lors du parsing usine ${doc.id}: $e');
          return null;
        }
      }).where((factory) => factory != null).cast<FactoryModel>().toList();

      factories.value = factoriesList;
      print('Usines chargées directement: ${factoriesList.length}');

      print('Chargement direct terminé - Total: ${users.length} utilisateurs, ${claims.length} réclamations, ${trainings.length} formations, ${factories.length} usines');

      // Mettre à jour l'interface utilisateur
      update();

    } catch (e) {
      print('Erreur lors du chargement direct: $e');
      throw e;
    }
  }

  // Load data for Security Admin
  Future<void> _loadSecurityAdminData() async {
    // Listen to all claims and trainings
    _firestoreService.getAllClaims().listen((data) {
      claims.value = data;
      update();
    });

    _firestoreService.getAllTrainings().listen((data) {
      trainings.value = data;
      update();
    });

    _firestoreService.getAllFactories().listen((data) {
      factories.value = data;
      update();
    });
  }

  // Load data for Factory Admin
  Future<void> _loadFactoryAdminData() async {
    print('=== FACTORY ADMIN DATA LOADING ===');
    print('User Factory: $userFactory');
    print('Current User: ${currentUser?.email}');
    print('Current User Factory: ${currentUser?.factory}');
    print('================================');

    if (userFactory == null) {
      print('ERREUR: userFactory est null!');
      return;
    }

    print('Chargement des données pour l\'usine: $userFactory');

    try {
      // Listen to factory-specific data
      _firestoreService.getClaimsByFactory(userFactory!).listen((data) {
        print('Réclamations reçues pour $userFactory: ${data.length}');
        claims.value = data;
        update();
      }, onError: (error) {
        print('Erreur lors du chargement des réclamations: $error');
        // Forcer le chargement direct depuis Firebase
        _loadDataDirectly().catchError((e) {
          print('Erreur chargement direct: $e');
          // En dernier recours, utiliser des données locales
          if (userFactory == 'Gabès') {
            _loadLocalGabesData();
          }
        });
      });

      _firestoreService.getTrainingsByFactory(userFactory!).listen((data) {
        print('Formations reçues pour $userFactory: ${data.length}');
        trainings.value = data;
        update();
      }, onError: (error) {
        print('Erreur lors du chargement des formations: $error');
      });

      _firestoreService.getUsersByFactory(userFactory!).listen((data) {
        print('Utilisateurs reçus pour $userFactory: ${data.length}');
        users.value = data;
        update();
      }, onError: (error) {
        print('Erreur lors du chargement des utilisateurs: $error');
      });

      // Load factory stats
      await loadStats();
    } catch (e) {
      print('Erreur générale lors du chargement des données: $e');
      // Forcer le chargement direct depuis Firebase
      try {
        await _loadDataDirectly();
      } catch (directError) {
        print('Erreur chargement direct: $directError');
        // En dernier recours, utiliser des données locales
        if (userFactory == 'Gabès') {
          _loadLocalGabesData();
        }
      }
    }
  }

  // Charger des données locales de test pour l'usine de Gabès
  void _loadLocalGabesData() {
    print('Chargement des données locales pour Gabès...');

    // Données de test locales pour les utilisateurs
    final localUsers = [
      UserModel(
        id: 'employee_gabes_1',
        email: '<EMAIL>',
        firstName: 'Ahmed',
        lastName: 'Ben Ali',
        role: 'employe',
        factory: 'Gabès',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      UserModel(
        id: 'employee_gabes_2',
        email: '<EMAIL>',
        firstName: 'Fatma',
        lastName: 'Trabelsi',
        role: 'employe',
        factory: 'Gabès',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      UserModel(
        id: 'employee_gabes_3',
        email: '<EMAIL>',
        firstName: 'Mohamed',
        lastName: 'Sassi',
        role: 'employe',
        factory: 'Gabès',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      UserModel(
        id: 'employee_gabes_4',
        email: '<EMAIL>',
        firstName: 'Salma',
        lastName: 'Ben Salem',
        role: 'employe',
        factory: 'Gabès',
        isActive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      UserModel(
        id: 'supervisor_gabes_1',
        email: '<EMAIL>',
        firstName: 'Karim',
        lastName: 'Supervisor',
        role: 'superviseur',
        factory: 'Gabès',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];

    // Données de test locales pour les réclamations
    final localClaims = [
      ClaimModel(
        id: 'local_claim_1',
        title: 'Fuite de produit chimique - Zone Production',
        description: 'Fuite détectée dans la zone de production principale',
        type: ClaimType.hazard,
        priority: ClaimPriority.high,
        status: ClaimStatus.pending,
        factory: 'Gabès',
        userId: 'employee_gabes_1',
        userEmail: '<EMAIL>',
        userName: 'Ahmed Ben Ali',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ClaimModel(
        id: 'local_claim_2',
        title: 'Équipement de protection défaillant',
        description: 'Les masques de protection ne fonctionnent plus correctement',
        type: ClaimType.nonCompliance,
        priority: ClaimPriority.medium,
        status: ClaimStatus.inProgress,
        factory: 'Gabès',
        userId: 'employee_gabes_2',
        userEmail: '<EMAIL>',
        userName: 'Fatma Trabelsi',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      ClaimModel(
        id: 'local_claim_3',
        title: 'Amélioration éclairage atelier',
        description: 'L\'éclairage de l\'atelier B est insuffisant',
        type: ClaimType.suggestion,
        priority: ClaimPriority.low,
        status: ClaimStatus.resolved,
        factory: 'Gabès',
        userId: 'employee_gabes_3',
        userEmail: '<EMAIL>',
        userName: 'Mohamed Sassi',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      ClaimModel(
        id: 'local_claim_4',
        title: 'Incident machine - Ligne 2',
        description: 'Arrêt imprévu de la machine sur la ligne 2',
        type: ClaimType.incident,
        priority: ClaimPriority.high,
        status: ClaimStatus.inProgress,
        factory: 'Gabès',
        userId: 'supervisor_gabes_1',
        userEmail: '<EMAIL>',
        userName: 'Karim Supervisor',
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
      ),
      ClaimModel(
        id: 'local_claim_5',
        title: 'Presque accident - Zone stockage',
        description: 'Un employé a failli être blessé dans la zone de stockage',
        type: ClaimType.nearMiss,
        priority: ClaimPriority.critical,
        status: ClaimStatus.pending,
        factory: 'Gabès',
        userId: 'employee_gabes_2',
        userEmail: '<EMAIL>',
        userName: 'Fatma Trabelsi',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
    ];

    // Données de test locales pour les formations
    final localTrainings = [
      TrainingModel(
        id: 'local_training_1',
        title: 'Formation Sécurité Chimique Avancée',
        description: 'Formation sur la manipulation des produits chimiques dangereux',
        type: TrainingType.safety,
        status: TrainingStatus.pending,
        factory: 'Gabès',
        userId: 'employee_gabes_1',
        userEmail: '<EMAIL>',
        userName: 'Ahmed Ben Ali',
        requestedAt: DateTime.now().subtract(const Duration(hours: 6)),
        durationHours: 8,
        isMandatory: true,
      ),
      TrainingModel(
        id: 'local_training_2',
        title: 'Formation Premiers Secours',
        description: 'Formation aux gestes de premiers secours en milieu industriel',
        type: TrainingType.emergency,
        status: TrainingStatus.approved,
        factory: 'Gabès',
        userId: 'employee_gabes_2',
        userEmail: '<EMAIL>',
        userName: 'Fatma Trabelsi',
        requestedAt: DateTime.now().subtract(const Duration(days: 2)),
        durationHours: 4,
        isMandatory: false,
      ),
      TrainingModel(
        id: 'local_training_3',
        title: 'Formation Manipulation Équipements',
        description: 'Formation sur l\'utilisation sécurisée des équipements industriels',
        type: TrainingType.equipment,
        status: TrainingStatus.completed,
        factory: 'Gabès',
        userId: 'employee_gabes_3',
        userEmail: '<EMAIL>',
        userName: 'Mohamed Sassi',
        requestedAt: DateTime.now().subtract(const Duration(days: 10)),
        durationHours: 6,
        isMandatory: true,
      ),
      TrainingModel(
        id: 'local_training_4',
        title: 'Formation Procédures de Sécurité',
        description: 'Formation sur les nouvelles procédures de sécurité',
        type: TrainingType.procedure,
        status: TrainingStatus.pending,
        factory: 'Gabès',
        userId: 'supervisor_gabes_1',
        userEmail: '<EMAIL>',
        userName: 'Karim Supervisor',
        requestedAt: DateTime.now().subtract(const Duration(hours: 12)),
        durationHours: 3,
        isMandatory: false,
      ),
    ];

    // Mettre à jour toutes les données
    users.value = localUsers;
    claims.value = localClaims;
    trainings.value = localTrainings;

    update();
    print('Données locales chargées: ${localUsers.length} utilisateurs, ${localClaims.length} réclamations, ${localTrainings.length} formations');
  }

  // Load data for Employee
  Future<void> _loadEmployeeData() async {
    if (currentUser == null) return;

    print('=== EMPLOYEE DATA LOADING ===');
    print('User ID: ${currentUser!.id}');
    print('User Email: ${currentUser!.email}');
    print('User Factory: ${currentUser!.factory}');
    print('==============================');

    try {
      // Charger les réclamations de l'employé
      print('Chargement des réclamations de l\'employé...');
      _firestoreService.getClaimsByUser(currentUser!.id).listen((data) {
        claims.value = data;
        print('Réclamations reçues pour l\'employé: ${data.length}');
        update();
      }, onError: (error) {
        print('Erreur lors du chargement des réclamations de l\'employé: $error');
        _loadLocalEmployeeData();
      });

      // Charger les formations de l'employé
      print('Chargement des formations de l\'employé...');
      _firestoreService.getTrainingsByUser(currentUser!.id).listen((data) {
        trainings.value = data;
        print('Formations reçues pour l\'employé: ${data.length}');
        update();
      }, onError: (error) {
        print('Erreur lors du chargement des formations de l\'employé: $error');
        _loadLocalEmployeeData();
      });

      // Charger les autres employés de la même usine (pour les voir dans les listes)
      if (currentUser!.factory != null) {
        print('Chargement des collègues de l\'usine: ${currentUser!.factory}');
        _firestoreService.getUsersByFactory(currentUser!.factory!).listen((data) {
          users.value = data;
          print('Collègues reçus pour l\'usine ${currentUser!.factory}: ${data.length}');
          update();
        }, onError: (error) {
          print('Erreur lors du chargement des collègues: $error');
          _loadLocalEmployeeData();
        });
      }

    } catch (e) {
      print('Erreur générale lors du chargement des données employé: $e');
      _loadLocalEmployeeData();
    }
  }

  // Charger des données locales de test pour l'employé
  void _loadLocalEmployeeData() {
    print('Chargement des données locales pour l\'employé...');

    if (currentUser == null) return;

    // Données de test pour l'employé connecté
    final localClaims = [
      ClaimModel(
        id: 'employee_claim_1',
        title: 'Problème d\'éclairage - Mon poste',
        description: 'L\'éclairage de mon poste de travail est insuffisant',
        type: ClaimType.suggestion,
        priority: ClaimPriority.low,
        status: ClaimStatus.pending,
        factory: currentUser!.factory ?? 'Gabès',
        userId: currentUser!.id,
        userEmail: currentUser!.email,
        userName: '${currentUser!.firstName} ${currentUser!.lastName}',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ClaimModel(
        id: 'employee_claim_2',
        title: 'Équipement défaillant',
        description: 'Mon équipement de protection a besoin d\'être remplacé',
        type: ClaimType.nonCompliance,
        priority: ClaimPriority.medium,
        status: ClaimStatus.inProgress,
        factory: currentUser!.factory ?? 'Gabès',
        userId: currentUser!.id,
        userEmail: currentUser!.email,
        userName: '${currentUser!.firstName} ${currentUser!.lastName}',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    final localTrainings = [
      TrainingModel(
        id: 'employee_training_1',
        title: 'Formation Sécurité Obligatoire',
        description: 'Formation annuelle de sécurité au travail',
        type: TrainingType.safety,
        status: TrainingStatus.pending,
        factory: currentUser!.factory ?? 'Gabès',
        userId: currentUser!.id,
        userEmail: currentUser!.email,
        userName: '${currentUser!.firstName} ${currentUser!.lastName}',
        requestedAt: DateTime.now().subtract(const Duration(hours: 6)),
        durationHours: 4,
        isMandatory: true,
      ),
    ];

    // Mettre à jour les données
    claims.value = localClaims;
    trainings.value = localTrainings;

    // Ajouter l'employé actuel à la liste des utilisateurs
    users.value = [currentUser!];

    update();
    print('Données locales employé chargées: ${localClaims.length} réclamations, ${localTrainings.length} formations');
  }

  // Load statistics
  Future<void> loadStats() async {
    try {
      if (userRole == 'super_admin' || userRole == 'admin_securite_gct') {
        final stats = await _firestoreService.getGlobalStats();
        globalStats.value = stats;
        update();
      }

      if (userRole == 'admin_usine' && userFactory != null) {
        final stats = await _firestoreService.getFactoryStats(userFactory!);
        factoryStats.value = stats;
        update();
      }
    } catch (e) {
      print('Erreur lors du chargement des statistiques: $e');
    }
  }

  // Get filtered claims by status
  List<ClaimModel> getClaimsByStatus(ClaimStatus status) {
    return claims.where((claim) => claim.status == status).toList();
  }

  // Get filtered trainings by status
  List<TrainingModel> getTrainingsByStatus(TrainingStatus status) {
    return trainings.where((training) => training.status == status).toList();
  }

  // Get recent claims (last 7 days)
  List<ClaimModel> get recentClaims {
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    return claims.where((claim) => claim.createdAt.isAfter(weekAgo)).toList();
  }

  // Get recent trainings (last 7 days)
  List<TrainingModel> get recentTrainings {
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    return trainings.where((training) => training.requestedAt.isAfter(weekAgo)).toList();
  }

  // Get pending claims count
  int get pendingClaimsCount {
    final count = getClaimsByStatus(ClaimStatus.pending).length;
    print('DEBUG: pendingClaimsCount = $count');
    return count;
  }

  // Get pending trainings count
  int get pendingTrainingsCount {
    final count = getTrainingsByStatus(TrainingStatus.pending).length;
    print('DEBUG: pendingTrainingsCount = $count');
    return count;
  }

  // Get resolved claims count
  int get resolvedClaimsCount => getClaimsByStatus(ClaimStatus.resolved).length;

  // Get completed trainings count
  int get completedTrainingsCount => getTrainingsByStatus(TrainingStatus.completed).length;

  // Get claims by priority
  List<ClaimModel> getClaimsByPriority(ClaimPriority priority) {
    return claims.where((claim) => claim.priority == priority).toList();
  }

  // Get high priority claims
  List<ClaimModel> get highPriorityClaims => 
      getClaimsByPriority(ClaimPriority.high) + getClaimsByPriority(ClaimPriority.critical);

  // Get expired trainings
  List<TrainingModel> get expiredTrainings {
    return trainings.where((training) => training.isExpired).toList();
  }

  // Refresh data
  Future<void> refreshData() async {
    print('Rafraîchissement des données...');
    isLoading.value = true;
    update();

    try {
      // Forcer le rechargement direct des données
      await _loadDataDirectly();

      // Puis recharger via les streams
      await loadDashboardData();

      print('Rafraîchissement terminé - Données actuelles:');
      print('- Utilisateurs: ${users.length}');
      print('- Réclamations: ${claims.length}');
      print('- Formations: ${trainings.length}');
      print('- Usines: ${factories.length}');

    } catch (e) {
      print('Erreur lors du rafraîchissement: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Get stats for current user context
  Map<String, int> get currentStats {
    if (userRole == 'admin_usine') {
      return factoryStats;
    }
    return globalStats;
  }

  // Get total claims count
  int get totalClaims {
    final count = claims.length;
    print('🔢 DEBUG: totalClaims = $count (source: ${claims.isNotEmpty ? "Firebase/Local" : "Empty"})');
    if (claims.isNotEmpty) {
      print('   Premier claim: ${claims.first.title}');
    }
    return count;
  }

  // Get total trainings count
  int get totalTrainings {
    final count = trainings.length;
    print('🔢 DEBUG: totalTrainings = $count (source: ${trainings.isNotEmpty ? "Firebase/Local" : "Empty"})');
    if (trainings.isNotEmpty) {
      print('   Première formation: ${trainings.first.title}');
    }
    return count;
  }

  // Get total users count (for admins)
  int get totalUsers {
    final count = users.length;
    print('🔢 DEBUG: totalUsers = $count (source: ${users.isNotEmpty ? "Firebase/Local" : "Empty"})');
    if (users.isNotEmpty) {
      print('   Premier utilisateur: ${users.first.firstName} ${users.first.lastName}');
    }
    return count;
  }

  // Get total factories count (for super admin)
  int get totalFactories {
    final count = factories.length;
    print('🔢 DEBUG: totalFactories = $count (source: ${factories.isNotEmpty ? "Firebase/Local" : "Empty"})');
    if (factories.isNotEmpty) {
      print('   Première usine: ${factories.first.name}');
    }
    return count;
  }

  // Navigation methods
  void navigateToUsers() {
    Get.toNamed('/users');
  }

  void navigateToClaims() {
    Get.toNamed('/claims');
  }

  void navigateToTrainings() {
    Get.toNamed('/trainings');
  }

  void navigateToReports() {
    Get.toNamed('/reports');
  }

  void navigateToFactories() {
    Get.toNamed('/factories');
  }

  // Navigation with filters
  void navigateToClaimsWithStatus(ClaimStatus status) {
    Get.toNamed('/claims', arguments: {'filter': 'status', 'value': status.name});
  }

  void navigateToTrainingsWithStatus(TrainingStatus status) {
    Get.toNamed('/trainings', arguments: {'filter': 'status', 'value': status.name});
  }

  void navigateToUsersWithStatus(bool isActive) {
    Get.toNamed('/users', arguments: {'filter': 'active', 'value': isActive});
  }

  // Méthode pour ajouter des données de test temporaires
  void _addTestData() {
    print('Ajout de données de test...');

    // Ajouter des utilisateurs de test
    users.value = [
      UserModel(
        id: 'test1',
        email: '<EMAIL>',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin',
        factory: null,
        createdAt: DateTime.now(),
        isActive: true,
      ),
      UserModel(
        id: 'test2',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Employee',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now(),
        isActive: true,
      ),
      UserModel(
        id: 'test3',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'Gabès',
        role: 'admin_usine',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now(),
        isActive: true,
      ),
    ];

    // Ajouter des réclamations de test
    claims.value = [
      ClaimModel(
        id: 'claim1',
        title: 'Fuite de produit chimique',
        description: 'Fuite détectée dans la zone de stockage',
        type: ClaimType.accident,
        status: ClaimStatus.pending,
        priority: ClaimPriority.critical,
        userId: 'test2',
        userEmail: '<EMAIL>',
        userName: 'Test Employee',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now(),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim2',
        title: 'Équipement défaillant',
        description: 'Casques de sécurité fissurés',
        type: ClaimType.hazard,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.high,
        userId: 'test2',
        userEmail: '<EMAIL>',
        userName: 'Test Employee',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now(),
        attachments: [],
      ),
    ];

    // Ajouter des formations de test
    trainings.value = [
      TrainingModel(
        id: 'training1',
        title: 'Formation Sécurité Chimique',
        description: 'Formation obligatoire sur la sécurité',
        type: TrainingType.safety,
        status: TrainingStatus.pending,
        userId: 'test2',
        userEmail: '<EMAIL>',
        userName: 'Test Employee',
        factory: 'Usine de Gabès',
        requestedAt: DateTime.now(),
        durationHours: 8,
        isMandatory: true,
        prerequisites: [],
      ),
    ];

    // Ajouter des usines de test
    factories.value = [
      FactoryModel(
        id: 'factory1',
        name: 'Usine de Gabès',
        code: 'GAB',
        address: 'Zone Industrielle Gabès',
        city: 'Gabès',
        region: 'Gabès',
        createdAt: DateTime.now(),
        isActive: true,
      ),
      FactoryModel(
        id: 'factory2',
        name: 'Usine de Sfax',
        code: 'SFX',
        address: 'Zone Industrielle Sfax',
        city: 'Sfax',
        region: 'Sfax',
        createdAt: DateTime.now(),
        isActive: true,
      ),
    ];

    print('Données de test ajoutées: ${users.length} utilisateurs, ${claims.length} réclamations, ${trainings.length} formations, ${factories.length} usines');
    update();
  }
}
