import 'package:cloud_firestore/cloud_firestore.dart';

class FactoryModel {
  final String id;
  final String name;
  final String code;
  final String address;
  final String city;
  final String region;
  final String country;
  final String? phone;
  final String? email;
  final String? manager;
  final int employeeCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? coordinates;

  FactoryModel({
    required this.id,
    required this.name,
    required this.code,
    required this.address,
    required this.city,
    required this.region,
    this.country = 'Tunisie',
    this.phone,
    this.email,
    this.manager,
    this.employeeCount = 0,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.coordinates,
  });

  String get fullAddress => '$address, $city, $region, $country';
  String get displayName => '$name ($code)';

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'address': address,
      'city': city,
      'region': region,
      'country': country,
      'phone': phone,
      'email': email,
      'manager': manager,
      'employeeCount': employeeCount,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'coordinates': coordinates,
    };
  }

  // Création depuis Map Firestore
  factory FactoryModel.fromMap(Map<String, dynamic> map) {
    return FactoryModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      code: map['code'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      region: map['region'] ?? '',
      country: map['country'] ?? 'Tunisie',
      phone: map['phone'],
      email: map['email'],
      manager: map['manager'],
      employeeCount: map['employeeCount'] ?? 0,
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      coordinates: map['coordinates'],
    );
  }

  // Création depuis DocumentSnapshot
  factory FactoryModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FactoryModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  FactoryModel copyWith({
    String? id,
    String? name,
    String? code,
    String? address,
    String? city,
    String? region,
    String? country,
    String? phone,
    String? email,
    String? manager,
    int? employeeCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? coordinates,
  }) {
    return FactoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      address: address ?? this.address,
      city: city ?? this.city,
      region: region ?? this.region,
      country: country ?? this.country,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      manager: manager ?? this.manager,
      employeeCount: employeeCount ?? this.employeeCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      coordinates: coordinates ?? this.coordinates,
    );
  }

  @override
  String toString() {
    return 'FactoryModel(id: $id, name: $name, code: $code, city: $city)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FactoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Usines prédéfinies pour GCT
  static List<FactoryModel> get defaultFactories => [
    FactoryModel(
      id: 'gabes',
      name: 'Usine de Gabès',
      code: 'GAB',
      address: 'Zone Industrielle de Gabès',
      city: 'Gabès',
      region: 'Gabès',
      country: 'Tunisie',
      phone: '+216 75 270 000',
      email: '<EMAIL>',
      manager: 'Ahmed Ben Ali',
      employeeCount: 450,
      createdAt: DateTime.now(),
    ),
    FactoryModel(
      id: 'sfax',
      name: 'Usine de Sfax',
      code: 'SFX',
      address: 'Zone Industrielle de Sfax',
      city: 'Sfax',
      region: 'Sfax',
      country: 'Tunisie',
      phone: '+216 74 400 000',
      email: '<EMAIL>',
      manager: 'Fatma Trabelsi',
      employeeCount: 320,
      createdAt: DateTime.now(),
    ),
    FactoryModel(
      id: 'tunis',
      name: 'Siège Social Tunis',
      code: 'TUN',
      address: 'Avenue Habib Bourguiba',
      city: 'Tunis',
      region: 'Tunis',
      country: 'Tunisie',
      phone: '+216 71 123 456',
      email: '<EMAIL>',
      manager: 'Mohamed Sassi',
      employeeCount: 150,
      createdAt: DateTime.now(),
    ),
    FactoryModel(
      id: 'sousse',
      name: 'Centre de Distribution Sousse',
      code: 'SOU',
      address: 'Zone Industrielle Sousse',
      city: 'Sousse',
      region: 'Sousse',
      country: 'Tunisie',
      phone: '+216 73 200 000',
      email: '<EMAIL>',
      manager: 'Leila Mansouri',
      employeeCount: 80,
      createdAt: DateTime.now(),
    ),
  ];
}
