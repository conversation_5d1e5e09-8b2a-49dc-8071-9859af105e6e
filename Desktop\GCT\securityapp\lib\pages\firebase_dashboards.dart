import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/firebase_auth_service.dart';
import '../services/firebase_database_service.dart';

// Dashboard Super Admin avec Firebase
class SuperAdminDashboard extends StatefulWidget {
  final UserModel user;
  
  const SuperAdminDashboard({super.key, required this.user});

  @override
  State<SuperAdminDashboard> createState() => _SuperAdminDashboardState();
}

class _SuperAdminDashboardState extends State<SuperAdminDashboard> {
  final FirebaseAuthService _authService = FirebaseAuthService();
  final FirebaseDatabaseService _dbService = FirebaseDatabaseService();
  
  List<UserModel> _users = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final users = await _authService.getAllUsers();
      final stats = await _dbService.getStatistics();
      
      setState(() {
        _users = users;
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Super Admin Dashboard'),
        backgroundColor: const Color(0xFF1565C0),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await _authService.signOut();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Informations utilisateur
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Icon(Icons.admin_panel_settings, color: Colors.white),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Bienvenue, ${widget.user.name}',
                                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                ),
                                Text(widget.user.roleDisplayName),
                                Text('Connecté avec Firebase', style: TextStyle(color: Colors.green)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Statistiques
                  const Text(
                    'Statistiques Firebase',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildStatCard('Utilisateurs', '${_users.length}', Icons.people, Colors.blue),
                      _buildStatCard('Réclamations', '${_statistics['claims']?['total'] ?? 0}', Icons.report_problem, Colors.red),
                      _buildStatCard('Formations', '${_statistics['trainings']?['total'] ?? 0}', Icons.school, Colors.green),
                      _buildStatCard('Usines', '4', Icons.factory, Colors.orange),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Liste des utilisateurs
                  const Text(
                    'Utilisateurs Firebase',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _users.length,
                    itemBuilder: (context, index) {
                      final user = _users[index];
                      return Card(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getRoleColor(user.role),
                            child: Icon(_getRoleIcon(user.role), color: Colors.white),
                          ),
                          title: Text(user.name),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(user.email),
                              Text(user.roleDisplayName),
                              if (user.factoryName != null) Text('Usine: ${user.factoryName}'),
                            ],
                          ),
                          trailing: user.isActive 
                              ? const Icon(Icons.check_circle, color: Colors.green)
                              : const Icon(Icons.cancel, color: Colors.red),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 8),
            Text(value, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            Text(title, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.superAdmin: return Colors.red;
      case UserRole.securityAdmin: return Colors.orange;
      case UserRole.factoryAdmin: return Colors.green;
      case UserRole.employee: return Colors.blue;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.superAdmin: return Icons.admin_panel_settings;
      case UserRole.securityAdmin: return Icons.security;
      case UserRole.factoryAdmin: return Icons.factory;
      case UserRole.employee: return Icons.person;
    }
  }
}

// Dashboard Admin Sécurité avec Firebase
class SecurityAdminDashboard extends StatefulWidget {
  final UserModel user;
  
  const SecurityAdminDashboard({super.key, required this.user});

  @override
  State<SecurityAdminDashboard> createState() => _SecurityAdminDashboardState();
}

class _SecurityAdminDashboardState extends State<SecurityAdminDashboard> {
  final FirebaseAuthService _authService = FirebaseAuthService();
  final FirebaseDatabaseService _dbService = FirebaseDatabaseService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Sécurité Dashboard'),
        backgroundColor: const Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await _authService.signOut();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.orange,
                      child: Icon(Icons.security, color: Colors.white),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bienvenue, ${widget.user.name}',
                            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          Text(widget.user.roleDisplayName),
                          Text('Supervision Firebase temps réel', style: TextStyle(color: Colors.green)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Supervision Multi-Usines Firebase',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Stream des réclamations en temps réel
            StreamBuilder(
              stream: _dbService.watchClaims(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                final claims = snapshot.data ?? [];
                
                return Column(
                  children: [
                    Card(
                      color: Colors.blue[50],
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.cloud_sync, color: Colors.blue),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Synchronisation Temps Réel',
                                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue[800]),
                                  ),
                                  Text('${claims.length} réclamations synchronisées'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: claims.take(5).length,
                      itemBuilder: (context, index) {
                        final claim = claims[index];
                        return Card(
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: _getClaimPriorityColor(claim.priority),
                              child: Icon(Icons.report_problem, color: Colors.white),
                            ),
                            title: Text(claim.title),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('${claim.factoryName} - ${claim.typeDisplayName}'),
                                Text('Priorité: ${claim.priorityDisplayName}'),
                              ],
                            ),
                            trailing: Chip(
                              label: Text(claim.statusDisplayName),
                              backgroundColor: _getClaimStatusColor(claim.status),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Color _getClaimPriorityColor(dynamic priority) {
    // Implémentation simplifiée
    return Colors.orange;
  }

  Color _getClaimStatusColor(dynamic status) {
    // Implémentation simplifiée
    return Colors.blue[100]!;
  }
}

// Dashboard Admin Usine avec Firebase
class FactoryAdminDashboard extends StatelessWidget {
  final UserModel user;
  
  const FactoryAdminDashboard({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Usine Dashboard'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await FirebaseAuthService().signOut();
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.factory, size: 80, color: Colors.green),
            const SizedBox(height: 20),
            Text(
              'Bienvenue, ${user.name}',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(user.roleDisplayName),
            if (user.factoryName != null) Text('Usine: ${user.factoryName}'),
            const SizedBox(height: 20),
            const Text('Dashboard Admin Usine avec Firebase'),
            const Text('Fonctionnalités en développement...'),
          ],
        ),
      ),
    );
  }
}

// Dashboard Employé avec Firebase
class EmployeeDashboard extends StatelessWidget {
  final UserModel user;
  
  const EmployeeDashboard({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Employé'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await FirebaseAuthService().signOut();
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person, size: 80, color: Colors.blue),
            const SizedBox(height: 20),
            Text(
              'Bienvenue, ${user.name}',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(user.roleDisplayName),
            if (user.factoryName != null) Text('Usine: ${user.factoryName}'),
            const SizedBox(height: 20),
            const Text('Dashboard Employé avec Firebase'),
            const Text('Fonctionnalités en développement...'),
          ],
        ),
      ),
    );
  }
}
