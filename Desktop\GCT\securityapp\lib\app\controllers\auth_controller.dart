import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/services/auth_service.dart';
import '../data/models/user_model.dart';
import '../routes/app_routes.dart';

class AuthController extends GetxController {
  static AuthController get instance => Get.find();
  
  final AuthService _authService = Get.find();
  
  // Observables
  final isLoading = false.obs;
  final isPasswordVisible = false.obs;
  final isConfirmPasswordVisible = false.obs;
  
  // Form controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  
  // Selected factory for registration
  final selectedFactory = Rx<String?>(null);
  
  // Form keys
  final loginFormKey = GlobalKey<FormState>();
  final registerFormKey = GlobalKey<FormState>();
  
  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  // Getters
  UserModel? get currentUser => _authService.currentUser.value;
  bool get isLoggedIn => _authService.isLoggedIn;

  // Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  // Login
  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) return;

    try {
      isLoading.value = true;

      final user = await _authService.signInWithEmailAndPassword(
        emailController.text.trim(),
        passwordController.text,
      );

      if (user != null) {
        _clearLoginForm();
        Get.snackbar(
          'Connexion réussie',
          'Bienvenue ${user.fullName}',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Redirection automatique vers le dashboard approprié
        _redirectToDashboard(user);
      }
    } catch (e) {
      Get.snackbar(
        'Erreur de connexion',
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Rediriger vers le bon dashboard selon l'email et le rôle
  void _redirectToDashboard(UserModel user) {
    print('=== CONTROLLER REDIRECTION DEBUG ===');
    print('User Email: ${user.email}');
    print('User Role: ${user.role}');
    print('User Factory: ${user.factory}');
    print('===================================');

    // Mapping spécifique des emails vers leurs dashboards
    switch (user.email.toLowerCase()) {
      case '<EMAIL>':
        print('Controller: Redirection vers Super Admin Dashboard');
        Get.offAllNamed(Routes.superAdminDashboard);
        break;
      case '<EMAIL>':
        print('Controller: Redirection vers Security Admin Dashboard');
        Get.offAllNamed(Routes.securityAdminDashboard);
        break;
      case '<EMAIL>':
      case '<EMAIL>':
        print('Controller: Redirection vers Factory Admin Dashboard (par email)');
        Get.offAllNamed(Routes.factoryAdminDashboard);
        break;
      case '<EMAIL>':
      case '<EMAIL>':
        print('Controller: Redirection vers Employee Dashboard');
        Get.offAllNamed(Routes.employeeDashboard);
        break;
      default:
        print('Controller: Fallback sur le rôle: ${user.role.toLowerCase()}');
        // Fallback sur le rôle si l'email n'est pas reconnu
        switch (user.role.toLowerCase()) {
          case 'super_admin':
          case 'superadmin':
            print('Controller: Redirection vers Super Admin Dashboard (par rôle)');
            Get.offAllNamed(Routes.superAdminDashboard);
            break;
          case 'admin_securite_gct':
          case 'security_admin':
          case 'securityadmin':
            print('Controller: Redirection vers Security Admin Dashboard (par rôle)');
            Get.offAllNamed(Routes.securityAdminDashboard);
            break;
          case 'admin_usine':
          case 'factory_admin':
          case 'factoryadmin':
            print('Controller: Redirection vers Factory Admin Dashboard (par rôle)');
            Get.offAllNamed(Routes.factoryAdminDashboard);
            break;
          case 'employe':
          case 'employee':
            print('Controller: Redirection vers Employee Dashboard (par rôle)');
            Get.offAllNamed(Routes.employeeDashboard);
            break;
          default:
            print('Controller: Redirection vers Home (défaut)');
            Get.offAllNamed(Routes.home);
        }
    }
  }

  // Register (Employee only)
  Future<void> register() async {
    if (!registerFormKey.currentState!.validate()) return;
    
    if (selectedFactory.value == null) {
      Get.snackbar(
        'Erreur',
        'Veuillez sélectionner une usine',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }
    
    try {
      isLoading.value = true;
      
      final user = await _authService.registerEmployee(
        email: emailController.text.trim(),
        password: passwordController.text,
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        factory: selectedFactory.value!,
      );
      
      if (user != null) {
        _clearRegisterForm();
        Get.snackbar(
          'Inscription réussie',
          'Bienvenue ${user.fullName}',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Erreur d\'inscription',
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      await _authService.signOut();
      Get.snackbar(
        'Déconnexion',
        'Vous avez été déconnecté avec succès',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Erreur',
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Reset password
  Future<void> resetPassword() async {
    if (emailController.text.trim().isEmpty) {
      Get.snackbar(
        'Erreur',
        'Veuillez saisir votre email',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }
    
    try {
      isLoading.value = true;
      
      await _authService.resetPassword(emailController.text.trim());
      
      Get.snackbar(
        'Email envoyé',
        'Un email de réinitialisation a été envoyé',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      Get.back(); // Fermer le dialog
    } catch (e) {
      Get.snackbar(
        'Erreur',
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Clear forms
  void _clearLoginForm() {
    emailController.clear();
    passwordController.clear();
    isPasswordVisible.value = false;
  }

  void _clearRegisterForm() {
    emailController.clear();
    passwordController.clear();
    firstNameController.clear();
    lastNameController.clear();
    confirmPasswordController.clear();
    selectedFactory.value = null;
    isPasswordVisible.value = false;
    isConfirmPasswordVisible.value = false;
  }

  // Validators
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email requis';
    }
    if (!GetUtils.isEmail(value)) {
      return 'Format d\'email invalide';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Mot de passe requis';
    }
    if (value.length < 6) {
      return 'Le mot de passe doit contenir au moins 6 caractères';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Confirmation requise';
    }
    if (value != passwordController.text) {
      return 'Les mots de passe ne correspondent pas';
    }
    return null;
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Ce champ est requis';
    }
    if (value.length < 2) {
      return 'Au moins 2 caractères requis';
    }
    return null;
  }

  // Check permissions
  bool hasPermission(String permission) {
    return _authService.hasPermission(permission);
  }
}
