import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  });

  Future<Either<Failure, void>> logout();

  Future<Either<Failure, User>> getCurrentUser();

  Future<Either<Failure, User>> refreshToken();

  Future<Either<Failure, bool>> isLoggedIn();

  Future<Either<Failure, void>> clearUserData();

  Stream<User?> get userStream;
}
