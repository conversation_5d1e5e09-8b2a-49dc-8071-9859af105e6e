import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/claim.dart';
import '../../domain/repositories/claims_repository.dart';
import '../datasources/claims_firebase_datasource.dart';
import '../models/claim_model.dart';

class ClaimsRepositoryImpl implements ClaimsRepository {
  final ClaimsFirebaseDataSource firebaseDataSource;

  ClaimsRepositoryImpl({required this.firebaseDataSource});

  @override
  Future<Either<Failure, List<Claim>>> getClaims({
    String? factoryId,
    String? status,
    String? type,
    String? assignedToId,
    int? page,
    int? limit,
  }) async {
    try {
      final claimModels = await firebaseDataSource.getClaims(
        factoryId: factoryId,
        status: status,
        type: type,
        assignedToId: assignedToId,
        limit: limit,
      );

      final claims = claimModels.map((model) => model.toEntity()).toList();
      return Right(claims);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, Claim>> getClaimById(String id) async {
    try {
      final claimModel = await firebaseDataSource.getClaimById(id);
      return Right(claimModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, Claim>> createClaim(Claim claim) async {
    try {
      final claimModel = ClaimModel.fromEntity(claim);
      final createdModel = await firebaseDataSource.createClaim(claimModel);
      return Right(createdModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, Claim>> updateClaim(Claim claim) async {
    try {
      final claimModel = ClaimModel.fromEntity(claim);
      final updatedModel = await firebaseDataSource.updateClaim(claimModel);
      return Right(updatedModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteClaim(String id) async {
    try {
      await firebaseDataSource.deleteClaim(id);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> uploadAttachment(String claimId, File file) async {
    try {
      final url = await firebaseDataSource.uploadAttachment(claimId, file);
      return Right(url);
    } on FileException catch (e) {
      return Left(FileFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAttachment(String url) async {
    try {
      await firebaseDataSource.deleteAttachment(url);
      return const Right(null);
    } on FileException catch (e) {
      return Left(FileFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, Claim>> addComment(String claimId, ClaimComment comment) async {
    try {
      final commentModel = ClaimCommentModel.fromEntity(comment);
      final updatedClaimModel = await firebaseDataSource.addComment(claimId, commentModel);
      return Right(updatedClaimModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Stream<Either<Failure, Claim>> watchClaim(String id) {
    try {
      return firebaseDataSource.watchClaim(id).map((claimModel) {
        return Right(claimModel.toEntity());
      }).handleError((error) {
        if (error is ServerException) {
          return Left(ServerFailure(message: error.message, code: error.statusCode));
        } else if (error is NetworkException) {
          return Left(NetworkFailure(message: error.message, code: error.code));
        } else if (error is AuthorizationException) {
          return Left(AuthorizationFailure(message: error.message, code: error.statusCode));
        } else {
          return Left(UnknownFailure(message: 'Erreur inattendue: $error'));
        }
      });
    } catch (e) {
      return Stream.value(Left(UnknownFailure(message: 'Erreur inattendue: $e')));
    }
  }

  @override
  Stream<Either<Failure, List<Claim>>> watchClaims({
    String? factoryId,
    String? status,
    String? assignedToId,
  }) {
    try {
      return firebaseDataSource.watchClaims(
        factoryId: factoryId,
        status: status,
        assignedToId: assignedToId,
      ).map((claimModels) {
        final claims = claimModels.map((model) => model.toEntity()).toList();
        return Right(claims);
      }).handleError((error) {
        if (error is ServerException) {
          return Left(ServerFailure(message: error.message, code: error.statusCode));
        } else if (error is NetworkException) {
          return Left(NetworkFailure(message: error.message, code: error.code));
        } else if (error is AuthorizationException) {
          return Left(AuthorizationFailure(message: error.message, code: error.statusCode));
        } else {
          return Left(UnknownFailure(message: 'Erreur inattendue: $error'));
        }
      });
    } catch (e) {
      return Stream.value(Left(UnknownFailure(message: 'Erreur inattendue: $e')));
    }
  }

  @override
  Future<Either<Failure, Map<String, int>>> getClaimsStats({String? factoryId}) async {
    try {
      final stats = await (firebaseDataSource as ClaimsFirebaseDataSourceImpl)
          .getClaimsStats(factoryId: factoryId);
      return Right(stats);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on AuthorizationException catch (e) {
      return Left(AuthorizationFailure(message: e.message, code: e.statusCode));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }
}
