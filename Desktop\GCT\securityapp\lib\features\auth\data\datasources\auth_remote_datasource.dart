import 'package:dio/dio.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<LoginResponse> login({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  });

  Future<void> logout();

  Future<UserModel> getProfile();

  Future<RefreshTokenResponse> refreshToken(String refreshToken);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;

  AuthRemoteDataSourceImpl(this.apiClient);

  @override
  Future<LoginResponse> login({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  }) async {
    try {
      final request = LoginRequest(
        email: email,
        password: password,
        deviceId: deviceId,
        deviceName: deviceName,
      );

      final response = await apiClient.login(request.toJson());
      return LoginResponse.from<PERSON>son(response);
    } catch (e) {
      if (e is DioException) {
        throw ServerException(
          message: e.response?.data?['message'] ?? 'Erreur de connexion',
          statusCode: e.response?.statusCode,
        );
      }
      throw ServerException(message: 'Erreur inattendue: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await apiClient.logout();
    } catch (e) {
      if (e is DioException) {
        throw ServerException(
          message: e.response?.data?['message'] ?? 'Erreur lors de la déconnexion',
          statusCode: e.response?.statusCode,
        );
      }
      throw ServerException(message: 'Erreur inattendue: $e');
    }
  }

  @override
  Future<UserModel> getProfile() async {
    try {
      final response = await apiClient.getProfile();
      return UserModel.fromJson(response['user']);
    } catch (e) {
      if (e is DioException) {
        throw ServerException(
          message: e.response?.data?['message'] ?? 'Erreur lors de la récupération du profil',
          statusCode: e.response?.statusCode,
        );
      }
      throw ServerException(message: 'Erreur inattendue: $e');
    }
  }

  @override
  Future<RefreshTokenResponse> refreshToken(String refreshToken) async {
    try {
      final request = RefreshTokenRequest(refreshToken: refreshToken);
      final response = await apiClient.refreshToken(request.toJson());
      return RefreshTokenResponse.fromJson(response);
    } catch (e) {
      if (e is DioException) {
        throw AuthenticationException(
          message: e.response?.data?['message'] ?? 'Token expiré',
          statusCode: e.response?.statusCode,
        );
      }
      throw AuthenticationException(message: 'Erreur inattendue: $e');
    }
  }
}
