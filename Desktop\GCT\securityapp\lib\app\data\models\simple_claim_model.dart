import 'package:cloud_firestore/cloud_firestore.dart';

class ClaimModel {
  final String id;
  final String title;
  final String description;
  final String location;
  final String priority;
  final String category;
  final String status;
  final String employeeId;
  final String employeeName;
  final String employeeEmail;
  final String factory;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? assignedTo;
  final String? resolution;
  final List<String> attachments;

  ClaimModel({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.priority,
    required this.category,
    required this.status,
    required this.employeeId,
    required this.employeeName,
    required this.employeeEmail,
    required this.factory,
    required this.createdAt,
    required this.updatedAt,
    this.assignedTo,
    this.resolution,
    this.attachments = const [],
  });

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'priority': priority,
      'category': category,
      'status': status,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'employeeEmail': employeeEmail,
      'factory': factory,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'assignedTo': assignedTo,
      'resolution': resolution,
      'attachments': attachments,
    };
  }

  // Create from Firestore document
  factory ClaimModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ClaimModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      location: data['location'] ?? '',
      priority: data['priority'] ?? 'Moyenne',
      category: data['category'] ?? 'Sécurité',
      status: data['status'] ?? 'En attente',
      employeeId: data['employeeId'] ?? '',
      employeeName: data['employeeName'] ?? '',
      employeeEmail: data['employeeEmail'] ?? '',
      factory: data['factory'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      assignedTo: data['assignedTo'],
      resolution: data['resolution'],
      attachments: List<String>.from(data['attachments'] ?? []),
    );
  }

  // Create a copy with updated fields
  ClaimModel copyWith({
    String? id,
    String? title,
    String? description,
    String? location,
    String? priority,
    String? category,
    String? status,
    String? employeeId,
    String? employeeName,
    String? employeeEmail,
    String? factory,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? assignedTo,
    String? resolution,
    List<String>? attachments,
  }) {
    return ClaimModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      status: status ?? this.status,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      employeeEmail: employeeEmail ?? this.employeeEmail,
      factory: factory ?? this.factory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      resolution: resolution ?? this.resolution,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  String toString() {
    return 'ClaimModel(id: $id, title: $title, status: $status, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClaimModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
