import 'package:firebase_database/firebase_database.dart';
import 'package:uuid/uuid.dart';
import '../models/claim_model.dart';
import '../models/training_model.dart';
import '../models/factory_model.dart';

class FirebaseDatabaseService {
  final DatabaseReference _database = FirebaseDatabase.instance.ref();
  final Uuid _uuid = const Uuid();
  
  // ==================== RÉCLAMATIONS ====================
  
  // Créer une nouvelle réclamation
  Future<String> createClaim(ClaimModel claim) async {
    try {
      final claimId = _uuid.v4();
      final claimWithId = claim.copyWith(id: claimId);
      
      await _database.child('claims').child(claimId).set(claimWithId.toJson());
      
      // Mettre à jour les statistiques
      await _updateClaimStatistics(claim.factoryId, claim.type);
      
      return claimId;
    } catch (e) {
      throw Exception('Erreur de création de réclamation: ${e.toString()}');
    }
  }
  
  // Obtenir toutes les réclamations
  Future<List<ClaimModel>> getAllClaims() async {
    try {
      final snapshot = await _database.child('claims').get();
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final claimData = Map<String, dynamic>.from(entry.value as Map);
          return ClaimModel.fromJson(claimData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des réclamations: ${e.toString()}');
    }
  }
  
  // Obtenir les réclamations par usine
  Future<List<ClaimModel>> getClaimsByFactory(String factoryId) async {
    try {
      final snapshot = await _database
          .child('claims')
          .orderByChild('factoryId')
          .equalTo(factoryId)
          .get();
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final claimData = Map<String, dynamic>.from(entry.value as Map);
          return ClaimModel.fromJson(claimData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des réclamations par usine: ${e.toString()}');
    }
  }
  
  // Obtenir les réclamations par utilisateur
  Future<List<ClaimModel>> getClaimsByUser(String userId) async {
    try {
      final snapshot = await _database
          .child('claims')
          .orderByChild('reportedBy')
          .equalTo(userId)
          .get();
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final claimData = Map<String, dynamic>.from(entry.value as Map);
          return ClaimModel.fromJson(claimData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des réclamations par utilisateur: ${e.toString()}');
    }
  }
  
  // Mettre à jour le statut d'une réclamation
  Future<void> updateClaimStatus(String claimId, ClaimStatus status, {String? comment}) async {
    try {
      final updates = {
        'status': status.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      };
      
      if (comment != null) {
        updates['statusComment'] = comment;
      }
      
      await _database.child('claims').child(claimId).update(updates);
    } catch (e) {
      throw Exception('Erreur de mise à jour du statut: ${e.toString()}');
    }
  }
  
  // ==================== FORMATIONS ====================
  
  // Créer une nouvelle formation
  Future<String> createTraining(TrainingModel training) async {
    try {
      final trainingId = _uuid.v4();
      final trainingWithId = training.copyWith(id: trainingId);
      
      await _database.child('trainings').child(trainingId).set(trainingWithId.toJson());
      return trainingId;
    } catch (e) {
      throw Exception('Erreur de création de formation: ${e.toString()}');
    }
  }
  
  // Obtenir toutes les formations
  Future<List<TrainingModel>> getAllTrainings() async {
    try {
      final snapshot = await _database.child('trainings').get();
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final trainingData = Map<String, dynamic>.from(entry.value as Map);
          return TrainingModel.fromJson(trainingData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des formations: ${e.toString()}');
    }
  }
  
  // Obtenir les formations par utilisateur
  Future<List<TrainingModel>> getTrainingsByUser(String userId) async {
    try {
      final snapshot = await _database
          .child('trainings')
          .orderByChild('userId')
          .equalTo(userId)
          .get();
      
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final trainingData = Map<String, dynamic>.from(entry.value as Map);
          return TrainingModel.fromJson(trainingData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des formations par utilisateur: ${e.toString()}');
    }
  }
  
  // Approuver/Rejeter une formation
  Future<void> updateTrainingStatus(String trainingId, TrainingStatus status, {String? comment}) async {
    try {
      final updates = {
        'status': status.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      };
      
      if (comment != null) {
        updates['approvalComment'] = comment;
      }
      
      await _database.child('trainings').child(trainingId).update(updates);
    } catch (e) {
      throw Exception('Erreur de mise à jour de la formation: ${e.toString()}');
    }
  }
  
  // ==================== USINES ====================
  
  // Créer une nouvelle usine
  Future<String> createFactory(FactoryModel factory) async {
    try {
      final factoryId = _uuid.v4();
      final factoryWithId = factory.copyWith(id: factoryId);
      
      await _database.child('factories').child(factoryId).set(factoryWithId.toJson());
      return factoryId;
    } catch (e) {
      throw Exception('Erreur de création d\'usine: ${e.toString()}');
    }
  }
  
  // Obtenir toutes les usines
  Future<List<FactoryModel>> getAllFactories() async {
    try {
      final snapshot = await _database.child('factories').get();
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return data.entries.map((entry) {
          final factoryData = Map<String, dynamic>.from(entry.value as Map);
          return FactoryModel.fromJson(factoryData);
        }).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Erreur de récupération des usines: ${e.toString()}');
    }
  }
  
  // ==================== STATISTIQUES ====================
  
  // Mettre à jour les statistiques des réclamations
  Future<void> _updateClaimStatistics(String factoryId, ClaimType type) async {
    try {
      final statsRef = _database.child('statistics').child('claims');
      
      // Statistiques globales
      await statsRef.child('total').runTransaction((Object? post) {
        return Transaction.success((post as int? ?? 0) + 1);
      });
      
      // Statistiques par usine
      await statsRef.child('byFactory').child(factoryId).runTransaction((Object? post) {
        return Transaction.success((post as int? ?? 0) + 1);
      });
      
      // Statistiques par type
      final typeKey = type.toString().split('.').last;
      await statsRef.child('byType').child(typeKey).runTransaction((Object? post) {
        return Transaction.success((post as int? ?? 0) + 1);
      });
      
    } catch (e) {
      print('Erreur de mise à jour des statistiques: $e');
    }
  }
  
  // Obtenir les statistiques
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final snapshot = await _database.child('statistics').get();
      if (snapshot.exists) {
        return Map<String, dynamic>.from(snapshot.value as Map);
      }
      return {};
    } catch (e) {
      throw Exception('Erreur de récupération des statistiques: ${e.toString()}');
    }
  }
  
  // ==================== TEMPS RÉEL ====================
  
  // Écouter les changements de réclamations
  Stream<List<ClaimModel>> watchClaims() {
    return _database.child('claims').onValue.map((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        return data.entries.map((entry) {
          final claimData = Map<String, dynamic>.from(entry.value as Map);
          return ClaimModel.fromJson(claimData);
        }).toList();
      }
      return <ClaimModel>[];
    });
  }
  
  // Écouter les changements de formations
  Stream<List<TrainingModel>> watchTrainings() {
    return _database.child('trainings').onValue.map((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        return data.entries.map((entry) {
          final trainingData = Map<String, dynamic>.from(entry.value as Map);
          return TrainingModel.fromJson(trainingData);
        }).toList();
      }
      return <TrainingModel>[];
    });
  }
}
