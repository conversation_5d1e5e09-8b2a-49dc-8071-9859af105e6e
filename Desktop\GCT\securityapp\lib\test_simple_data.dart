import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Firebase Data',
      home: TestDataScreen(),
    );
  }
}

class TestDataScreen extends StatefulWidget {
  @override
  _TestDataScreenState createState() => _TestDataScreenState();
}

class _TestDataScreenState extends State<TestDataScreen> {
  String _status = 'Prêt à créer des données';
  bool _isLoading = false;

  Future<void> _createSimpleTestData() async {
    setState(() {
      _isLoading = true;
      _status = 'Création des données en cours...';
    });

    try {
      final firestore = FirebaseFirestore.instance;

      // Créer un utilisateur simple
      await firestore.collection('users').doc('simple_user_1').set({
        'id': 'simple_user_1',
        'email': '<EMAIL>',
        'firstName': 'Test',
        'lastName': 'User',
        'role': 'employe',
        'factory': 'Test Factory',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Créer une réclamation simple
      await firestore.collection('claims').doc('simple_claim_1').set({
        'id': 'simple_claim_1',
        'title': 'Test Claim',
        'description': 'Description de test',
        'type': 'accident',
        'status': 'pending',
        'priority': 'medium',
        'userId': 'simple_user_1',
        'userEmail': '<EMAIL>',
        'userName': 'Test User',
        'factory': 'Test Factory',
        'createdAt': FieldValue.serverTimestamp(),
        'attachments': [],
      });

      // Créer une formation simple
      await firestore.collection('trainings').doc('simple_training_1').set({
        'id': 'simple_training_1',
        'title': 'Test Training',
        'description': 'Description de formation test',
        'type': 'safety',
        'status': 'pending',
        'userId': 'simple_user_1',
        'userEmail': '<EMAIL>',
        'userName': 'Test User',
        'factory': 'Test Factory',
        'requestedAt': FieldValue.serverTimestamp(),
        'durationHours': 4,
        'isMandatory': true,
        'prerequisites': [],
      });

      // Créer une usine simple
      await firestore.collection('factories').doc('simple_factory_1').set({
        'id': 'simple_factory_1',
        'name': 'Test Factory',
        'code': 'TEST',
        'address': 'Test Address',
        'city': 'Test City',
        'region': 'Test Region',
        'country': 'Tunisie',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      });

      setState(() {
        _status = 'Données créées avec succès!';
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _status = 'Erreur: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testDataRetrieval() async {
    setState(() {
      _isLoading = true;
      _status = 'Test de récupération des données...';
    });

    try {
      final firestore = FirebaseFirestore.instance;

      // Tester les utilisateurs
      final usersSnapshot = await firestore.collection('users').get();
      print('Utilisateurs trouvés: ${usersSnapshot.docs.length}');

      // Tester les réclamations
      final claimsSnapshot = await firestore.collection('claims').get();
      print('Réclamations trouvées: ${claimsSnapshot.docs.length}');

      // Tester les formations
      final trainingsSnapshot = await firestore.collection('trainings').get();
      print('Formations trouvées: ${trainingsSnapshot.docs.length}');

      // Tester les usines
      final factoriesSnapshot = await firestore.collection('factories').get();
      print('Usines trouvées: ${factoriesSnapshot.docs.length}');

      setState(() {
        _status = 'Test terminé - Vérifiez la console pour les résultats';
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _status = 'Erreur lors du test: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Firebase Data'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _status,
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            if (_isLoading)
              CircularProgressIndicator()
            else ...[
              ElevatedButton(
                onPressed: _createSimpleTestData,
                child: Text('Créer données de test simples'),
              ),
              SizedBox(height: 10),
              ElevatedButton(
                onPressed: _testDataRetrieval,
                child: Text('Tester récupération des données'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
