/// Constantes pour le module de gestion des réclamations GCT
class ClaimConstants {
  
  /// Types de réclamations
  static const String TYPE_INCIDENT = 'INCIDENT';
  static const String TYPE_ACCIDENT = 'ACCIDENT';
  static const String TYPE_NEAR_MISS = 'NEAR_MISS';
  static const String TYPE_UNSAFE_BEHAVIOR = 'UNSAFE_BEHAVIOR';
  static const String TYPE_SUGGESTION = 'SUGGESTION';
  static const String TYPE_ENVIRONMENTAL = 'ENVIRONMENTAL';
  static const String TYPE_EQUIPMENT_FAILURE = 'EQUIPMENT_FAILURE';
  
  static const List<String> ALL_TYPES = [
    TYPE_INCIDENT,
    TYPE_ACCIDENT,
    TYPE_NEAR_MISS,
    TYPE_UNSAFE_BEHAVIOR,
    TYPE_SUGGESTION,
    TYPE_ENVIRONMENTAL,
    TYPE_EQUIPMENT_FAILURE,
  ];
  
  /// Statuts des réclamations
  static const String STATUS_PENDING = 'PENDING';
  static const String STATUS_ACKNOWLEDGED = 'ACKNOWLEDGED';
  static const String STATUS_IN_PROGRESS = 'IN_PROGRESS';
  static const String STATUS_UNDER_INVESTIGATION = 'UNDER_INVESTIGATION';
  static const String STATUS_RESOLVED = 'RESOLVED';
  static const String STATUS_CLOSED = 'CLOSED';
  static const String STATUS_REJECTED = 'REJECTED';
  static const String STATUS_ESCALATED = 'ESCALATED';
  
  static const List<String> ALL_STATUSES = [
    STATUS_PENDING,
    STATUS_ACKNOWLEDGED,
    STATUS_IN_PROGRESS,
    STATUS_UNDER_INVESTIGATION,
    STATUS_RESOLVED,
    STATUS_CLOSED,
    STATUS_REJECTED,
    STATUS_ESCALATED,
  ];
  
  /// Priorités
  static const String PRIORITY_LOW = 'LOW';
  static const String PRIORITY_MEDIUM = 'MEDIUM';
  static const String PRIORITY_HIGH = 'HIGH';
  static const String PRIORITY_CRITICAL = 'CRITICAL';
  static const String PRIORITY_EMERGENCY = 'EMERGENCY';
  
  static const List<String> ALL_PRIORITIES = [
    PRIORITY_LOW,
    PRIORITY_MEDIUM,
    PRIORITY_HIGH,
    PRIORITY_CRITICAL,
    PRIORITY_EMERGENCY,
  ];
  
  /// Niveaux de sévérité
  static const String SEVERITY_MINOR = 'MINOR';
  static const String SEVERITY_MODERATE = 'MODERATE';
  static const String SEVERITY_MAJOR = 'MAJOR';
  static const String SEVERITY_CATASTROPHIC = 'CATASTROPHIC';
  
  static const List<String> ALL_SEVERITIES = [
    SEVERITY_MINOR,
    SEVERITY_MODERATE,
    SEVERITY_MAJOR,
    SEVERITY_CATASTROPHIC,
  ];
  
  /// Départements/Zones typiques dans les usines GCT
  static const String DEPT_PRODUCTION = 'PRODUCTION';
  static const String DEPT_MAINTENANCE = 'MAINTENANCE';
  static const String DEPT_QUALITY = 'QUALITY';
  static const String DEPT_SECURITY = 'SECURITY';
  static const String DEPT_LABORATORY = 'LABORATORY';
  static const String DEPT_WAREHOUSE = 'WAREHOUSE';
  static const String DEPT_ADMINISTRATION = 'ADMINISTRATION';
  static const String DEPT_LOGISTICS = 'LOGISTICS';
  static const String DEPT_ENVIRONMENT = 'ENVIRONMENT';
  static const String DEPT_RESEARCH = 'RESEARCH';
  
  static const List<String> ALL_DEPARTMENTS = [
    DEPT_PRODUCTION,
    DEPT_MAINTENANCE,
    DEPT_QUALITY,
    DEPT_SECURITY,
    DEPT_LABORATORY,
    DEPT_WAREHOUSE,
    DEPT_ADMINISTRATION,
    DEPT_LOGISTICS,
    DEPT_ENVIRONMENT,
    DEPT_RESEARCH,
  ];
  
  /// Méthodes utilitaires pour obtenir les libellés en français
  static String getTypeDisplayName(String type) {
    switch (type) {
      case TYPE_INCIDENT:
        return 'Incident';
      case TYPE_ACCIDENT:
        return 'Accident';
      case TYPE_NEAR_MISS:
        return 'Presque-accident';
      case TYPE_UNSAFE_BEHAVIOR:
        return 'Comportement à risque';
      case TYPE_SUGGESTION:
        return 'Suggestion d\'amélioration';
      case TYPE_ENVIRONMENTAL:
        return 'Incident environnemental';
      case TYPE_EQUIPMENT_FAILURE:
        return 'Panne d\'équipement';
      default:
        return type;
    }
  }
  
  static String getStatusDisplayName(String status) {
    switch (status) {
      case STATUS_PENDING:
        return 'En attente';
      case STATUS_ACKNOWLEDGED:
        return 'Pris en compte';
      case STATUS_IN_PROGRESS:
        return 'En cours';
      case STATUS_UNDER_INVESTIGATION:
        return 'Sous enquête';
      case STATUS_RESOLVED:
        return 'Résolu';
      case STATUS_CLOSED:
        return 'Fermé';
      case STATUS_REJECTED:
        return 'Rejeté';
      case STATUS_ESCALATED:
        return 'Escaladé';
      default:
        return status;
    }
  }
  
  static String getPriorityDisplayName(String priority) {
    switch (priority) {
      case PRIORITY_LOW:
        return 'Faible';
      case PRIORITY_MEDIUM:
        return 'Moyenne';
      case PRIORITY_HIGH:
        return 'Élevée';
      case PRIORITY_CRITICAL:
        return 'Critique';
      case PRIORITY_EMERGENCY:
        return 'Urgence';
      default:
        return priority;
    }
  }
  
  static String getSeverityDisplayName(String severity) {
    switch (severity) {
      case SEVERITY_MINOR:
        return 'Mineure';
      case SEVERITY_MODERATE:
        return 'Modérée';
      case SEVERITY_MAJOR:
        return 'Majeure';
      case SEVERITY_CATASTROPHIC:
        return 'Catastrophique';
      default:
        return severity;
    }
  }
  
  static String getDepartmentDisplayName(String department) {
    switch (department) {
      case DEPT_PRODUCTION:
        return 'Production';
      case DEPT_MAINTENANCE:
        return 'Maintenance';
      case DEPT_QUALITY:
        return 'Qualité';
      case DEPT_SECURITY:
        return 'Sécurité';
      case DEPT_LABORATORY:
        return 'Laboratoire';
      case DEPT_WAREHOUSE:
        return 'Entrepôt';
      case DEPT_ADMINISTRATION:
        return 'Administration';
      case DEPT_LOGISTICS:
        return 'Logistique';
      case DEPT_ENVIRONMENT:
        return 'Environnement';
      case DEPT_RESEARCH:
        return 'Recherche & Développement';
      default:
        return department;
    }
  }
  
  /// Délais de traitement par priorité (en heures)
  static int getResponseTimeLimit(String priority) {
    switch (priority) {
      case PRIORITY_EMERGENCY:
        return 1; // 1 heure
      case PRIORITY_CRITICAL:
        return 4; // 4 heures
      case PRIORITY_HIGH:
        return 24; // 1 jour
      case PRIORITY_MEDIUM:
        return 72; // 3 jours
      case PRIORITY_LOW:
        return 168; // 1 semaine
      default:
        return 72;
    }
  }
  
  /// Délais de résolution par priorité (en heures)
  static int getResolutionTimeLimit(String priority) {
    switch (priority) {
      case PRIORITY_EMERGENCY:
        return 4; // 4 heures
      case PRIORITY_CRITICAL:
        return 24; // 1 jour
      case PRIORITY_HIGH:
        return 72; // 3 jours
      case PRIORITY_MEDIUM:
        return 168; // 1 semaine
      case PRIORITY_LOW:
        return 336; // 2 semaines
      default:
        return 168;
    }
  }
  
  /// Vérifier si une réclamation nécessite une escalade automatique
  static bool requiresEscalation(String priority, String severity) {
    return priority == PRIORITY_CRITICAL || 
           priority == PRIORITY_EMERGENCY ||
           severity == SEVERITY_CATASTROPHIC ||
           severity == SEVERITY_MAJOR;
  }
  
  /// Obtenir la couleur associée à un statut
  static String getStatusColor(String status) {
    switch (status) {
      case STATUS_PENDING:
        return '#FFA726'; // Orange
      case STATUS_ACKNOWLEDGED:
        return '#42A5F5'; // Bleu
      case STATUS_IN_PROGRESS:
        return '#26C6DA'; // Cyan
      case STATUS_UNDER_INVESTIGATION:
        return '#AB47BC'; // Violet
      case STATUS_RESOLVED:
        return '#66BB6A'; // Vert
      case STATUS_CLOSED:
        return '#78909C'; // Gris
      case STATUS_REJECTED:
        return '#EF5350'; // Rouge
      case STATUS_ESCALATED:
        return '#FF7043'; // Orange foncé
      default:
        return '#9E9E9E'; // Gris par défaut
    }
  }
  
  /// Obtenir la couleur associée à une priorité
  static String getPriorityColor(String priority) {
    switch (priority) {
      case PRIORITY_LOW:
        return '#4CAF50'; // Vert
      case PRIORITY_MEDIUM:
        return '#FF9800'; // Orange
      case PRIORITY_HIGH:
        return '#FF5722'; // Rouge-orange
      case PRIORITY_CRITICAL:
        return '#F44336'; // Rouge
      case PRIORITY_EMERGENCY:
        return '#9C27B0'; // Violet
      default:
        return '#9E9E9E'; // Gris par défaut
    }
  }
}
