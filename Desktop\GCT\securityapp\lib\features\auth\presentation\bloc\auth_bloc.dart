import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/get_current_user_usecase.dart';
import '../../domain/usecases/refresh_token_usecase.dart';
import '../../../../core/errors/failures.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final RefreshTokenUseCase refreshTokenUseCase;

  StreamSubscription? _userSubscription;

  AuthBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.getCurrentUserUseCase,
    required this.refreshTokenUseCase,
  }) : super(const AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthCheckRequested>(_onCheckRequested);
    on<AuthRefreshTokenRequested>(_onRefreshTokenRequested);
    on<AuthUserChanged>(_onUserChanged);

    // Listen to user stream changes
    // _userSubscription = authRepository.userStream.listen((user) {
    //   if (user != null) {
    //     add(const AuthUserChanged());
    //   }
    // });
  }

  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final params = LoginParams(
      email: event.email,
      password: event.password,
      deviceId: event.deviceId,
      deviceName: event.deviceName,
    );

    final result = await loginUseCase(params);

    result.fold(
      (failure) => emit(AuthError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await logoutUseCase();

    result.fold(
      (failure) => emit(AuthError(
        message: _mapFailureToMessage(failure),
        errorCode: failure.code?.toString(),
      )),
      (_) => emit(const AuthUnauthenticated()),
    );
  }

  Future<void> _onCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await getCurrentUserUseCase();

    result.fold(
      (failure) {
        if (failure is AuthenticationFailure) {
          emit(const AuthUnauthenticated());
        } else {
          emit(AuthError(
            message: _mapFailureToMessage(failure),
            errorCode: failure.code?.toString(),
          ));
        }
      },
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  Future<void> _onRefreshTokenRequested(
    AuthRefreshTokenRequested event,
    Emitter<AuthState> emit,
  ) async {
    final result = await refreshTokenUseCase();

    result.fold(
      (failure) {
        if (failure is AuthenticationFailure) {
          emit(const AuthTokenExpired());
        } else {
          emit(AuthError(
            message: _mapFailureToMessage(failure),
            errorCode: failure.code?.toString(),
          ));
        }
      },
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  Future<void> _onUserChanged(
    AuthUserChanged event,
    Emitter<AuthState> emit,
  ) async {
    final result = await getCurrentUserUseCase();

    result.fold(
      (failure) => emit(const AuthUnauthenticated()),
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return failure.message;
      case NetworkFailure:
        return 'Erreur de connexion. Vérifiez votre connexion internet.';
      case AuthenticationFailure:
        return 'Email ou mot de passe incorrect.';
      case AuthorizationFailure:
        return 'Vous n\'avez pas les permissions nécessaires.';
      case CacheFailure:
        return 'Erreur de stockage local.';
      case ValidationFailure:
        return 'Données invalides.';
      default:
        return 'Une erreur inattendue s\'est produite.';
    }
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    return super.close();
  }
}
