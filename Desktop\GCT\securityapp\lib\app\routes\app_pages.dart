import 'package:get/get.dart';
import 'app_routes.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/auth/views/register_view.dart';
import '../modules/dashboard/views/super_admin_dashboard_view.dart';
import '../modules/dashboard/views/security_admin_dashboard_view.dart';
import '../modules/dashboard/views/factory_admin_dashboard_view.dart';
import '../modules/dashboard/views/employee_dashboard_view.dart';
import '../modules/users/views/users_view.dart';
import '../modules/claims/views/claims_view.dart';
import '../modules/claims/views/create_claim_view.dart';
import '../modules/claims/views/my_claims_view.dart';
import '../modules/trainings/views/trainings_view.dart';
import '../modules/trainings/views/request_training_view.dart';
import '../modules/trainings/views/my_trainings_view.dart';
import '../modules/reports/views/reports_view.dart';
import '../modules/test/views/test_forms_view.dart';
import '../modules/profile/views/profile_view.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.home;

  static final routes = [
    // Home
    GetPage(
      name: Routes.home,
      page: () => const HomeView(),
    ),
    
    // Authentication
    GetPage(
      name: Routes.login,
      page: () => const LoginView(),
    ),
    GetPage(
      name: Routes.register,
      page: () => const RegisterView(),
    ),
    
    // Dashboards
    GetPage(
      name: Routes.superAdminDashboard,
      page: () => const SuperAdminDashboardView(),
    ),
    GetPage(
      name: Routes.securityAdminDashboard,
      page: () => const SecurityAdminDashboardView(),
    ),
    GetPage(
      name: Routes.factoryAdminDashboard,
      page: () => const FactoryAdminDashboardView(),
    ),
    GetPage(
      name: Routes.employeeDashboard,
      page: () => const EmployeeDashboardView(),
    ),

    // Management Pages
    GetPage(
      name: Routes.users,
      page: () => const UsersView(),
    ),
    GetPage(
      name: Routes.claims,
      page: () => const ClaimsView(),
    ),
    GetPage(
      name: Routes.createClaim,
      page: () => const CreateClaimView(),
    ),
    GetPage(
      name: Routes.myClaims,
      page: () => const MyClaimsView(),
    ),
    GetPage(
      name: Routes.trainings,
      page: () => const TrainingsView(),
    ),
    GetPage(
      name: Routes.requestTraining,
      page: () => const RequestTrainingView(),
    ),
    GetPage(
      name: Routes.myTrainings,
      page: () => const MyTrainingsView(),
    ),
    GetPage(
      name: Routes.reports,
      page: () => const ReportsView(),
    ),
    GetPage(
      name: Routes.testForms,
      page: () => const TestFormsView(),
    ),
    GetPage(
      name: Routes.profile,
      page: () => const ProfileView(),
    ),
  ];
}
