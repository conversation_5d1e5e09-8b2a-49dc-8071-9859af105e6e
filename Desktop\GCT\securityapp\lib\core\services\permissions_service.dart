import '../constants/app_constants.dart';
import '../../features/auth/domain/entities/user.dart';

/// Service de gestion des permissions basé sur les rôles GCT
class PermissionsService {
  
  /// Vérifie si l'utilisateur a une permission spécifique
  static bool hasPermission(User user, String permission) {
    final userPermissions = getRolePermissions(user.role);
    return userPermissions.contains(permission) || userPermissions.contains(AppPermissions.ALL);
  }
  
  /// Vérifie si l'utilisateur peut accéder à une usine spécifique
  static bool canAccessFactory(User user, String factoryId) {
    switch (user.role) {
      case AppRoles.SUPER_ADMIN:
      case AppRoles.SECURITY_ADMIN_GCT:
        return true; // Accès à toutes les usines
      case AppRoles.FACTORY_ADMIN:
      case AppRoles.EMPLOYEE:
        return user.factoryId == factoryId; // Accès uniquement à leur usine
      default:
        return false;
    }
  }
  
  /// Vérifie si l'utilisateur peut gérer d'autres utilisateurs
  static bool canManageUsers(User user, [User? targetUser]) {
    switch (user.role) {
      case AppRoles.SUPER_ADMIN:
        return true; // Peut gérer tous les utilisateurs
      case AppRoles.SECURITY_ADMIN_GCT:
        // Peut gérer tous sauf les Super Admins
        return targetUser?.role != AppRoles.SUPER_ADMIN;
      case AppRoles.FACTORY_ADMIN:
        // Peut gérer uniquement les employés de son usine
        return targetUser?.role == AppRoles.EMPLOYEE && 
               targetUser?.factoryId == user.factoryId;
      default:
        return false;
    }
  }
  
  /// Obtient toutes les permissions pour un rôle donné
  static Set<String> getRolePermissions(String role) {
    switch (role) {
      case AppRoles.SUPER_ADMIN:
        return _superAdminPermissions;
      case AppRoles.SECURITY_ADMIN_GCT:
        return _securityAdminPermissions;
      case AppRoles.FACTORY_ADMIN:
        return _factoryAdminPermissions;
      case AppRoles.EMPLOYEE:
        return _employeePermissions;
      default:
        return <String>{};
    }
  }
  
  /// Obtient la description d'un rôle
  static String getRoleDescription(String role) {
    switch (role) {
      case AppRoles.SUPER_ADMIN:
        return 'Accès complet à toutes les fonctionnalités et données de l\'application';
      case AppRoles.SECURITY_ADMIN_GCT:
        return 'Gestion de la sécurité dans toutes les usines du groupe GCT';
      case AppRoles.FACTORY_ADMIN:
        return 'Administration d\'une usine spécifique avec gestion des employés locaux';
      case AppRoles.EMPLOYEE:
        return 'Accès de base pour signaler des incidents et suivre les formations';
      default:
        return 'Rôle non défini';
    }
  }
  
  /// Vérifie si l'utilisateur peut effectuer une action sur une réclamation
  static bool canManageClaim(User user, String claimFactoryId, [String? claimReporterId]) {
    switch (user.role) {
      case AppRoles.SUPER_ADMIN:
      case AppRoles.SECURITY_ADMIN_GCT:
        return true; // Accès à toutes les réclamations
      case AppRoles.FACTORY_ADMIN:
        return user.factoryId == claimFactoryId; // Réclamations de son usine
      case AppRoles.EMPLOYEE:
        return user.factoryId == claimFactoryId || user.id == claimReporterId; // Ses réclamations ou celles de son usine
      default:
        return false;
    }
  }
  
  /// Vérifie si l'utilisateur peut gérer les formations
  static bool canManageTrainings(User user, [String? trainingFactoryId]) {
    switch (user.role) {
      case AppRoles.SUPER_ADMIN:
      case AppRoles.SECURITY_ADMIN_GCT:
        return true; // Peut gérer toutes les formations
      case AppRoles.FACTORY_ADMIN:
        return trainingFactoryId == null || user.factoryId == trainingFactoryId;
      default:
        return false;
    }
  }
  
  /// Obtient les usines accessibles par l'utilisateur
  static List<String> getAccessibleFactories(User user) {
    switch (user.role) {
      case AppRoles.SUPER_ADMIN:
      case AppRoles.SECURITY_ADMIN_GCT:
        return ['ALL']; // Toutes les usines
      case AppRoles.FACTORY_ADMIN:
      case AppRoles.EMPLOYEE:
        return user.factoryId != null ? [user.factoryId!] : [];
      default:
        return [];
    }
  }
  
  /// Permissions pour Super Admin
  static const Set<String> _superAdminPermissions = {
    AppPermissions.ALL,
  };
  
  /// Permissions pour Security Admin GCT
  static const Set<String> _securityAdminPermissions = {
    // Gestion des réclamations
    AppPermissions.CLAIMS_VIEW_ALL,
    AppPermissions.CLAIMS_CREATE,
    AppPermissions.CLAIMS_EDIT,
    AppPermissions.CLAIMS_DELETE,
    AppPermissions.CLAIMS_ASSIGN,
    AppPermissions.CLAIMS_CLOSE,
    
    // Gestion des formations
    AppPermissions.TRAININGS_VIEW_ALL,
    AppPermissions.TRAININGS_CREATE,
    AppPermissions.TRAININGS_EDIT,
    AppPermissions.TRAININGS_DELETE,
    AppPermissions.TRAININGS_ASSIGN,
    AppPermissions.TRAININGS_VALIDATE,
    
    // Gestion des utilisateurs (limité)
    AppPermissions.USERS_VIEW_ALL,
    AppPermissions.USERS_EDIT,
    
    // Rapports et analytics
    AppPermissions.REPORTS_VIEW_ALL,
    AppPermissions.REPORTS_EXPORT,
    
    // Usines
    AppPermissions.FACTORIES_VIEW_ALL,
    AppPermissions.FACTORIES_EDIT,
  };
  
  /// Permissions pour Factory Admin
  static const Set<String> _factoryAdminPermissions = {
    // Gestion des réclamations (usine locale)
    AppPermissions.CLAIMS_VIEW_FACTORY,
    AppPermissions.CLAIMS_CREATE,
    AppPermissions.CLAIMS_EDIT,
    AppPermissions.CLAIMS_ASSIGN,
    AppPermissions.CLAIMS_CLOSE,
    
    // Gestion des formations (usine locale)
    AppPermissions.TRAININGS_VIEW_FACTORY,
    AppPermissions.TRAININGS_ASSIGN,
    AppPermissions.TRAININGS_VALIDATE,
    
    // Gestion des employés (usine locale)
    AppPermissions.USERS_VIEW_FACTORY,
    AppPermissions.USERS_EDIT,
    AppPermissions.USERS_CREATE,
    
    // Rapports (usine locale)
    AppPermissions.REPORTS_VIEW_FACTORY,
    AppPermissions.REPORTS_EXPORT,
    
    // Usine
    AppPermissions.FACTORIES_VIEW_OWN,
  };
  
  /// Permissions pour Employee
  static const Set<String> _employeePermissions = {
    // Réclamations de base
    AppPermissions.CLAIMS_VIEW_OWN,
    AppPermissions.CLAIMS_CREATE,
    AppPermissions.CLAIMS_COMMENT,
    
    // Formations
    AppPermissions.TRAININGS_VIEW_OWN,
    AppPermissions.TRAININGS_COMPLETE,
    
    // Profil
    AppPermissions.PROFILE_VIEW,
    AppPermissions.PROFILE_EDIT,
    
    // Usine (lecture seule)
    AppPermissions.FACTORIES_VIEW_OWN,
  };
}

/// Constantes pour les rôles
class AppRoles {
  static const String SUPER_ADMIN = 'SUPER_ADMIN';
  static const String SECURITY_ADMIN_GCT = 'SECURITY_ADMIN_GCT';
  static const String FACTORY_ADMIN = 'FACTORY_ADMIN';
  static const String EMPLOYEE = 'EMPLOYEE';
  
  static const List<String> ALL_ROLES = [
    SUPER_ADMIN,
    SECURITY_ADMIN_GCT,
    FACTORY_ADMIN,
    EMPLOYEE,
  ];
  
  static String getDisplayName(String role) {
    switch (role) {
      case SUPER_ADMIN:
        return 'Super Administrateur';
      case SECURITY_ADMIN_GCT:
        return 'Admin Sécurité GCT';
      case FACTORY_ADMIN:
        return 'Admin d\'Usine';
      case EMPLOYEE:
        return 'Employé';
      default:
        return role;
    }
  }
}

/// Constantes pour les permissions
class AppPermissions {
  // Permission globale
  static const String ALL = 'ALL';
  
  // Réclamations
  static const String CLAIMS_VIEW_ALL = 'CLAIMS_VIEW_ALL';
  static const String CLAIMS_VIEW_FACTORY = 'CLAIMS_VIEW_FACTORY';
  static const String CLAIMS_VIEW_OWN = 'CLAIMS_VIEW_OWN';
  static const String CLAIMS_CREATE = 'CLAIMS_CREATE';
  static const String CLAIMS_EDIT = 'CLAIMS_EDIT';
  static const String CLAIMS_DELETE = 'CLAIMS_DELETE';
  static const String CLAIMS_ASSIGN = 'CLAIMS_ASSIGN';
  static const String CLAIMS_CLOSE = 'CLAIMS_CLOSE';
  static const String CLAIMS_COMMENT = 'CLAIMS_COMMENT';
  
  // Formations
  static const String TRAININGS_VIEW_ALL = 'TRAININGS_VIEW_ALL';
  static const String TRAININGS_VIEW_FACTORY = 'TRAININGS_VIEW_FACTORY';
  static const String TRAININGS_VIEW_OWN = 'TRAININGS_VIEW_OWN';
  static const String TRAININGS_CREATE = 'TRAININGS_CREATE';
  static const String TRAININGS_EDIT = 'TRAININGS_EDIT';
  static const String TRAININGS_DELETE = 'TRAININGS_DELETE';
  static const String TRAININGS_ASSIGN = 'TRAININGS_ASSIGN';
  static const String TRAININGS_VALIDATE = 'TRAININGS_VALIDATE';
  static const String TRAININGS_COMPLETE = 'TRAININGS_COMPLETE';
  
  // Utilisateurs
  static const String USERS_VIEW_ALL = 'USERS_VIEW_ALL';
  static const String USERS_VIEW_FACTORY = 'USERS_VIEW_FACTORY';
  static const String USERS_CREATE = 'USERS_CREATE';
  static const String USERS_EDIT = 'USERS_EDIT';
  static const String USERS_DELETE = 'USERS_DELETE';
  
  // Rapports
  static const String REPORTS_VIEW_ALL = 'REPORTS_VIEW_ALL';
  static const String REPORTS_VIEW_FACTORY = 'REPORTS_VIEW_FACTORY';
  static const String REPORTS_EXPORT = 'REPORTS_EXPORT';
  
  // Usines
  static const String FACTORIES_VIEW_ALL = 'FACTORIES_VIEW_ALL';
  static const String FACTORIES_VIEW_OWN = 'FACTORIES_VIEW_OWN';
  static const String FACTORIES_CREATE = 'FACTORIES_CREATE';
  static const String FACTORIES_EDIT = 'FACTORIES_EDIT';
  static const String FACTORIES_DELETE = 'FACTORIES_DELETE';
  
  // Profil
  static const String PROFILE_VIEW = 'PROFILE_VIEW';
  static const String PROFILE_EDIT = 'PROFILE_EDIT';
}
