<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Firebase - GCT Security</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .success {
            background-color: #27ae60;
        }
        .success:hover {
            background-color: #229954;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔥 Test Firebase - GCT Security App</h1>
    
    <div class="container">
        <h2>📋 Test de Création de Réclamation</h2>
        <form id="testClaimForm">
            <div class="form-group">
                <label for="testTitle">Titre de la réclamation *</label>
                <input type="text" id="testTitle" required value="Test - Problème d'éclairage">
            </div>
            
            <div class="form-group">
                <label for="testCategory">Catégorie *</label>
                <select id="testCategory" required>
                    <option value="Sécurité">Sécurité</option>
                    <option value="Équipement">Équipement</option>
                    <option value="Environnement">Environnement</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testPriority">Priorité *</label>
                <select id="testPriority" required>
                    <option value="Moyenne">Moyenne</option>
                    <option value="Élevée">Élevée</option>
                    <option value="Critique">Critique</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testLocation">Localisation *</label>
                <input type="text" id="testLocation" required value="Atelier 1, Zone A">
            </div>
            
            <div class="form-group">
                <label for="testDescription">Description *</label>
                <textarea id="testDescription" required>L'éclairage de mon poste de travail est insuffisant, ce qui rend le travail difficile et dangereux.</textarea>
            </div>
            
            <button type="submit" class="success">🔥 Tester Firebase</button>
            <button type="button" onclick="testFirebaseConnection()">🔗 Test Connexion</button>
            <button type="button" onclick="loadClaims()">📋 Charger Réclamations</button>
        </form>
        <div id="testStatus" class="status"></div>
    </div>
    
    <div class="container">
        <h2>📊 Console de Debug</h2>
        <div id="debugLog" class="log">Prêt pour les tests Firebase...\n</div>
        <button onclick="clearLog()">🗑️ Effacer Log</button>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, query, where, orderBy, Timestamp, updateDoc, doc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Configuration Firebase (configuration réelle du projet)
        const firebaseConfig = {
            apiKey: "AIzaSyCLvB4w4_00A49MhulVfQIYZSmzGhcUYTI",
            authDomain: "securityapp-8b742.firebaseapp.com",
            projectId: "securityapp-8b742",
            storageBucket: "securityapp-8b742.appspot.com",
            messagingSenderId: "325296538989",
            appId: "1:325296538989:web:abcdef123456789012345678"
        };

        // Initialiser Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        let currentUser = null;

        function log(message) {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
            
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }

        // Test de connexion Firebase
        window.testFirebaseConnection = async function() {
            try {
                log('🔗 Test de connexion Firebase...');
                
                // Test d'authentification
                try {
                    const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'employee123');
                    currentUser = userCredential.user;
                    log('✅ Authentification réussie: ' + currentUser.email);
                } catch (authError) {
                    log('❌ Erreur d\'authentification: ' + authError.message);
                    showStatus('Erreur d\'authentification: ' + authError.message, 'error');
                    return;
                }

                // Test de lecture Firestore
                try {
                    const testCollection = collection(db, 'claims');
                    const snapshot = await getDocs(testCollection);
                    log(`✅ Connexion Firestore OK - ${snapshot.size} documents trouvés`);
                    showStatus('Connexion Firebase réussie !', 'success');
                } catch (firestoreError) {
                    log('❌ Erreur Firestore: ' + firestoreError.message);
                    showStatus('Erreur Firestore: ' + firestoreError.message, 'error');
                }
                
            } catch (error) {
                log('❌ Erreur générale: ' + error.message);
                showStatus('Erreur de connexion: ' + error.message, 'error');
            }
        };

        // Test de création de réclamation
        document.getElementById('testClaimForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!currentUser) {
                showStatus('Veuillez d\'abord tester la connexion Firebase', 'error');
                return;
            }

            try {
                log('📋 Création d\'une réclamation de test...');

                const claimData = {
                    title: document.getElementById('testTitle').value,
                    category: document.getElementById('testCategory').value,
                    priority: document.getElementById('testPriority').value,
                    location: document.getElementById('testLocation').value,
                    description: document.getElementById('testDescription').value,
                    status: 'En attente',
                    employeeId: 'test-employee-id',
                    employeeName: 'Test Employee',
                    employeeEmail: currentUser.email,
                    factory: 'Gabès',
                    createdAt: Timestamp.now(),
                    updatedAt: Timestamp.now()
                };

                log('📤 Données à envoyer: ' + JSON.stringify(claimData, null, 2));

                const docRef = await addDoc(collection(db, 'claims'), claimData);
                
                // Mettre à jour avec l'ID généré
                await updateDoc(doc(db, 'claims', docRef.id), { id: docRef.id });
                
                log('✅ Réclamation créée avec succès! ID: ' + docRef.id);
                showStatus('Réclamation créée avec succès dans Firebase !', 'success');
                
            } catch (error) {
                log('❌ Erreur lors de la création: ' + error.message);
                showStatus('Erreur: ' + error.message, 'error');
            }
        });

        // Charger les réclamations
        window.loadClaims = async function() {
            try {
                log('📋 Chargement des réclamations...');
                
                const claimsCollection = collection(db, 'claims');
                const q = query(claimsCollection, orderBy('createdAt', 'desc'));
                const snapshot = await getDocs(q);
                
                log(`📊 ${snapshot.size} réclamations trouvées:`);
                
                snapshot.forEach((doc) => {
                    const data = doc.data();
                    log(`- ${data.title} (${data.status}) - ${data.employeeEmail}`);
                });
                
                showStatus(`${snapshot.size} réclamations chargées`, 'success');
                
            } catch (error) {
                log('❌ Erreur lors du chargement: ' + error.message);
                showStatus('Erreur de chargement: ' + error.message, 'error');
            }
        };

        window.clearLog = function() {
            document.getElementById('debugLog').textContent = 'Log effacé...\n';
        };

        // Test automatique au chargement
        log('🚀 Page de test Firebase chargée');
        log('📝 Instructions:');
        log('1. Cliquez sur "Test Connexion" pour vérifier Firebase');
        log('2. Cliquez sur "Tester Firebase" pour créer une réclamation');
        log('3. Cliquez sur "Charger Réclamations" pour voir les données');
    </script>
</body>
</html>
