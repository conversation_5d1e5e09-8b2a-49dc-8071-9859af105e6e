import 'package:equatable/equatable.dart';

/// Entité représentant un commentaire sur une réclamation
class ClaimComment extends Equatable {
  final String id;
  final String claimId;
  final String authorId;
  final String authorName;
  final String authorRole;
  final String content;
  final String type; // COMMENT, STATUS_CHANGE, ASSIGNMENT, ESCALATION, RESOLUTION
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isInternal; // Commentaire interne (non visible par le rapporteur)
  final List<String> attachments; // Pièces jointes du commentaire
  final String? previousValue; // Valeur précédente (pour les changements de statut)
  final String? newValue; // Nouvelle valeur (pour les changements de statut)
  final Map<String, dynamic>? metadata;

  const ClaimComment({
    required this.id,
    required this.claimId,
    required this.authorId,
    required this.authorName,
    required this.authorRole,
    required this.content,
    required this.type,
    required this.createdAt,
    this.updatedAt,
    this.isInternal = false,
    this.attachments = const [],
    this.previousValue,
    this.newValue,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        claimId,
        authorId,
        authorName,
        authorRole,
        content,
        type,
        createdAt,
        updatedAt,
        isInternal,
        attachments,
        previousValue,
        newValue,
        metadata,
      ];

  /// Crée une copie du commentaire avec des modifications
  ClaimComment copyWith({
    String? id,
    String? claimId,
    String? authorId,
    String? authorName,
    String? authorRole,
    String? content,
    String? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isInternal,
    List<String>? attachments,
    String? previousValue,
    String? newValue,
    Map<String, dynamic>? metadata,
  }) {
    return ClaimComment(
      id: id ?? this.id,
      claimId: claimId ?? this.claimId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorRole: authorRole ?? this.authorRole,
      content: content ?? this.content,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isInternal: isInternal ?? this.isInternal,
      attachments: attachments ?? this.attachments,
      previousValue: previousValue ?? this.previousValue,
      newValue: newValue ?? this.newValue,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Vérifie si le commentaire est un changement de statut
  bool get isStatusChange => type == ClaimCommentType.STATUS_CHANGE;

  /// Vérifie si le commentaire est une assignation
  bool get isAssignment => type == ClaimCommentType.ASSIGNMENT;

  /// Vérifie si le commentaire est une escalade
  bool get isEscalation => type == ClaimCommentType.ESCALATION;

  /// Vérifie si le commentaire est une résolution
  bool get isResolution => type == ClaimCommentType.RESOLUTION;

  /// Obtient le texte formaté pour l'historique
  String getFormattedHistoryText() {
    switch (type) {
      case ClaimCommentType.STATUS_CHANGE:
        return 'Statut changé de "$previousValue" à "$newValue"';
      case ClaimCommentType.ASSIGNMENT:
        return 'Réclamation assignée à $newValue';
      case ClaimCommentType.ESCALATION:
        return 'Réclamation escaladée vers $newValue';
      case ClaimCommentType.RESOLUTION:
        return 'Réclamation marquée comme résolue';
      case ClaimCommentType.COMMENT:
      default:
        return content;
    }
  }
}

/// Types de commentaires
class ClaimCommentType {
  static const String COMMENT = 'COMMENT';
  static const String STATUS_CHANGE = 'STATUS_CHANGE';
  static const String ASSIGNMENT = 'ASSIGNMENT';
  static const String ESCALATION = 'ESCALATION';
  static const String RESOLUTION = 'RESOLUTION';
  static const String INVESTIGATION_START = 'INVESTIGATION_START';
  static const String INVESTIGATION_UPDATE = 'INVESTIGATION_UPDATE';
  static const String CORRECTIVE_ACTION = 'CORRECTIVE_ACTION';
  static const String PREVENTIVE_ACTION = 'PREVENTIVE_ACTION';
  static const String CLOSURE = 'CLOSURE';

  static const List<String> ALL_TYPES = [
    COMMENT,
    STATUS_CHANGE,
    ASSIGNMENT,
    ESCALATION,
    RESOLUTION,
    INVESTIGATION_START,
    INVESTIGATION_UPDATE,
    CORRECTIVE_ACTION,
    PREVENTIVE_ACTION,
    CLOSURE,
  ];

  static String getDisplayName(String type) {
    switch (type) {
      case COMMENT:
        return 'Commentaire';
      case STATUS_CHANGE:
        return 'Changement de statut';
      case ASSIGNMENT:
        return 'Assignation';
      case ESCALATION:
        return 'Escalade';
      case RESOLUTION:
        return 'Résolution';
      case INVESTIGATION_START:
        return 'Début d\'enquête';
      case INVESTIGATION_UPDATE:
        return 'Mise à jour enquête';
      case CORRECTIVE_ACTION:
        return 'Action corrective';
      case PREVENTIVE_ACTION:
        return 'Action préventive';
      case CLOSURE:
        return 'Clôture';
      default:
        return type;
    }
  }

  static String getIcon(String type) {
    switch (type) {
      case COMMENT:
        return '💬';
      case STATUS_CHANGE:
        return '🔄';
      case ASSIGNMENT:
        return '👤';
      case ESCALATION:
        return '⬆️';
      case RESOLUTION:
        return '✅';
      case INVESTIGATION_START:
        return '🔍';
      case INVESTIGATION_UPDATE:
        return '📝';
      case CORRECTIVE_ACTION:
        return '🔧';
      case PREVENTIVE_ACTION:
        return '🛡️';
      case CLOSURE:
        return '🔒';
      default:
        return '📄';
    }
  }
}
