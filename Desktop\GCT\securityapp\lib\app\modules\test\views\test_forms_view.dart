import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../routes/app_routes.dart';

class TestFormsView extends StatelessWidget {
  const TestFormsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Test des Formulaires',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.science_rounded,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test des Formulaires',
                          style: GoogleFonts.inter(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Testez les formulaires de réclamation et de formation',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Form Buttons
            Text(
              'Formulaires Disponibles',
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 16),

            // Claims Form Button
            _buildFormButton(
              title: 'Nouvelle Réclamation',
              subtitle: 'Signaler un problème de sécurité',
              icon: Icons.report_problem_rounded,
              color: AppTheme.errorColor,
              onTap: () => Get.toNamed(Routes.createClaim),
            ),

            const SizedBox(height: 16),

            // Training Form Button
            _buildFormButton(
              title: 'Demande de Formation',
              subtitle: 'Demander une formation en sécurité',
              icon: Icons.school_rounded,
              color: AppTheme.successColor,
              onTap: () => Get.toNamed(Routes.requestTraining),
            ),

            const SizedBox(height: 32),

            // Navigation Buttons
            Text(
              'Navigation',
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 16),

            // Back to Dashboard Button
            _buildFormButton(
              title: 'Retour au Dashboard',
              subtitle: 'Retourner au tableau de bord',
              icon: Icons.dashboard_rounded,
              color: AppTheme.primaryColor,
              onTap: () => Get.back(),
            ),

            const SizedBox(height: 16),

            // Claims List Button
            _buildFormButton(
              title: 'Liste des Réclamations',
              subtitle: 'Voir toutes les réclamations',
              icon: Icons.list_rounded,
              color: AppTheme.warningColor,
              onTap: () => Get.toNamed(Routes.claims),
            ),

            const SizedBox(height: 16),

            // Trainings List Button
            _buildFormButton(
              title: 'Liste des Formations',
              subtitle: 'Voir toutes les formations',
              icon: Icons.library_books_rounded,
              color: AppTheme.infoColor,
              onTap: () => Get.toNamed(Routes.trainings),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
