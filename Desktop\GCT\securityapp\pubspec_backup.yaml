name: securityapp
description: "Application mobile de gestion de sécurité pour le Groupe Chimique Tunisien (GCT)"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  go_router: ^14.6.2
  flutter_bloc: ^8.1.6

  # State Management
  bloc: ^8.1.4
  equatable: ^2.0.5
  dartz: ^0.10.1

  # HTTP & API
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Local Storage
  shared_preferences: ^2.3.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Firebase & Authentication
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  cloud_firestore: ^5.6.11
  firebase_storage: ^12.4.0
  firebase_analytics: ^11.4.0
  firebase_crashlytics: ^4.2.0
  crypto: ^3.0.5

  # File & Media
  image_picker: ^1.1.2
  file_picker: ^8.1.4
  path_provider: ^2.1.5

  # Location & Maps
  geolocator: ^13.0.2
  google_maps_flutter: ^2.10.0

  # Notifications
  firebase_messaging: ^15.1.6
  flutter_local_notifications: ^18.0.1

  # Utils
  intl: ^0.20.1
  uuid: ^4.5.1
  logger: ^2.4.0
  permission_handler: ^11.3.1
  get_it: ^8.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.13
  retrofit_generator: ^8.2.1
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4

  # Linting
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/logos/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
