import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../services/permissions_service.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/bloc/auth_state.dart';
import '../../features/auth/domain/entities/user.dart';

/// Widget qui affiche son contenu uniquement si l'utilisateur a les permissions requises
class PermissionWidget extends StatelessWidget {
  final String? permission;
  final List<String>? permissions;
  final String? requiredRole;
  final List<String>? requiredRoles;
  final String? factoryId;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnDenied;

  const PermissionWidget({
    super.key,
    this.permission,
    this.permissions,
    this.requiredRole,
    this.requiredRoles,
    this.factoryId,
    required this.child,
    this.fallback,
    this.showFallbackOnDenied = false,
  }) : assert(
          permission != null || 
          permissions != null || 
          requiredRole != null || 
          requiredRoles != null,
          'Au moins une condition de permission doit être spécifiée'
        );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return showFallbackOnDenied && fallback != null 
              ? fallback! 
              : const SizedBox.shrink();
        }

        final user = state.user;
        final hasAccess = _checkAccess(user);

        if (hasAccess) {
          return child;
        } else {
          return showFallbackOnDenied && fallback != null 
              ? fallback! 
              : const SizedBox.shrink();
        }
      },
    );
  }

  bool _checkAccess(User user) {
    // Vérifier les rôles requis
    if (requiredRole != null && user.role != requiredRole) {
      return false;
    }
    
    if (requiredRoles != null && !requiredRoles!.contains(user.role)) {
      return false;
    }

    // Vérifier l'accès à l'usine
    if (factoryId != null && !PermissionsService.canAccessFactory(user, factoryId!)) {
      return false;
    }

    // Vérifier les permissions spécifiques
    if (permission != null && !PermissionsService.hasPermission(user, permission!)) {
      return false;
    }

    if (permissions != null) {
      for (final perm in permissions!) {
        if (!PermissionsService.hasPermission(user, perm)) {
          return false;
        }
      }
    }

    return true;
  }
}

/// Widget pour afficher différent contenu selon le rôle
class RoleBasedWidget extends StatelessWidget {
  final Widget? superAdminWidget;
  final Widget? securityAdminWidget;
  final Widget? factoryAdminWidget;
  final Widget? employeeWidget;
  final Widget? defaultWidget;

  const RoleBasedWidget({
    super.key,
    this.superAdminWidget,
    this.securityAdminWidget,
    this.factoryAdminWidget,
    this.employeeWidget,
    this.defaultWidget,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return defaultWidget ?? const SizedBox.shrink();
        }

        switch (state.user.role) {
          case AppRoles.SUPER_ADMIN:
            return superAdminWidget ?? defaultWidget ?? const SizedBox.shrink();
          case AppRoles.SECURITY_ADMIN_GCT:
            return securityAdminWidget ?? defaultWidget ?? const SizedBox.shrink();
          case AppRoles.FACTORY_ADMIN:
            return factoryAdminWidget ?? defaultWidget ?? const SizedBox.shrink();
          case AppRoles.EMPLOYEE:
            return employeeWidget ?? defaultWidget ?? const SizedBox.shrink();
          default:
            return defaultWidget ?? const SizedBox.shrink();
        }
      },
    );
  }
}

/// Widget pour afficher un badge de rôle
class RoleBadge extends StatelessWidget {
  final String role;
  final bool showIcon;
  final double? fontSize;

  const RoleBadge({
    super.key,
    required this.role,
    this.showIcon = true,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getRoleConfig(role);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: config.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: config.color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              config.icon,
              size: fontSize ?? 12,
              color: config.color,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            AppRoles.getDisplayName(role),
            style: TextStyle(
              fontSize: fontSize ?? 12,
              fontWeight: FontWeight.w600,
              color: config.color,
            ),
          ),
        ],
      ),
    );
  }

  _RoleConfig _getRoleConfig(String role) {
    switch (role) {
      case AppRoles.SUPER_ADMIN:
        return _RoleConfig(
          color: Colors.red,
          icon: Icons.admin_panel_settings,
        );
      case AppRoles.SECURITY_ADMIN_GCT:
        return _RoleConfig(
          color: Colors.orange,
          icon: Icons.security,
        );
      case AppRoles.FACTORY_ADMIN:
        return _RoleConfig(
          color: Colors.blue,
          icon: Icons.business,
        );
      case AppRoles.EMPLOYEE:
        return _RoleConfig(
          color: Colors.green,
          icon: Icons.person,
        );
      default:
        return _RoleConfig(
          color: Colors.grey,
          icon: Icons.help,
        );
    }
  }
}

class _RoleConfig {
  final Color color;
  final IconData icon;

  _RoleConfig({required this.color, required this.icon});
}

/// Mixin pour faciliter l'utilisation des permissions dans les widgets
mixin PermissionMixin {
  bool hasPermission(BuildContext context, String permission) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      return PermissionsService.hasPermission(authState.user, permission);
    }
    return false;
  }

  bool hasRole(BuildContext context, String role) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      return authState.user.role == role;
    }
    return false;
  }

  bool canAccessFactory(BuildContext context, String factoryId) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      return PermissionsService.canAccessFactory(authState.user, factoryId);
    }
    return false;
  }

  User? getCurrentUser(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      return authState.user;
    }
    return null;
  }
}

/// Widget pour afficher un message d'accès refusé
class AccessDeniedWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final VoidCallback? onRetry;

  const AccessDeniedWidget({
    super.key,
    this.message,
    this.icon,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.lock,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message ?? 'Accès refusé',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Vous n\'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Réessayer'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
