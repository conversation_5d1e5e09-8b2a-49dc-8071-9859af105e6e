import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/permissions_service.dart';
import '../../../../core/widgets/permission_widget.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/presentation/bloc/auth_state.dart';

class PermissionsPage extends StatelessWidget {
  const PermissionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      requiredRoles: const [AppRoles.SUPER_ADMIN, AppRoles.SECURITY_ADMIN_GCT],
      fallback: const AccessDeniedWidget(
        message: 'Accès aux permissions refusé',
        icon: Icons.admin_panel_settings,
      ),
      showFallbackOnDenied: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Gestion des Permissions'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const _PermissionsContent(),
      ),
    );
  }
}

class _PermissionsContent extends StatefulWidget {
  const _PermissionsContent();

  @override
  State<_PermissionsContent> createState() => _PermissionsContentState();
}

class _PermissionsContentState extends State<_PermissionsContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tabs
        Container(
          color: Colors.grey[100],
          child: TabBar(
            controller: _tabController,
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppTheme.primaryColor,
            tabs: const [
              Tab(
                icon: Icon(Icons.people),
                text: 'Rôles & Permissions',
              ),
              Tab(
                icon: Icon(Icons.security),
                text: 'Matrice des Droits',
              ),
            ],
          ),
        ),
        
        // Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: const [
              _RolesPermissionsTab(),
              _PermissionsMatrixTab(),
            ],
          ),
        ),
      ],
    );
  }
}

class _RolesPermissionsTab extends StatelessWidget {
  const _RolesPermissionsTab();

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Description
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: AppTheme.infoColor),
                    const SizedBox(width: 8),
                    const Text(
                      'Système de Rôles GCT',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const Text(
                  'L\'application GCT Security utilise un système de rôles hiérarchique pour contrôler l\'accès aux fonctionnalités selon les responsabilités de chaque utilisateur.',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Liste des rôles
        ...AppRoles.ALL_ROLES.map((role) => _buildRoleCard(role)),
      ],
    );
  }

  Widget _buildRoleCard(String role) {
    final permissions = PermissionsService.getRolePermissions(role);
    final description = PermissionsService.getRoleDescription(role);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: RoleBadge(role: role),
        title: Text(
          AppRoles.getDisplayName(role),
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          description,
          style: const TextStyle(fontSize: 12),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Permissions (${permissions.length})',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 12),
                if (permissions.contains(AppPermissions.ALL))
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.withOpacity(0.3)),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.admin_panel_settings, color: Colors.red),
                        SizedBox(width: 8),
                        Text(
                          'ACCÈS COMPLET - Toutes les permissions',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: permissions.map((permission) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppTheme.primaryColor.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          _getPermissionDisplayName(permission),
                          style: TextStyle(
                            fontSize: 11,
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case AppPermissions.CLAIMS_VIEW_ALL:
        return 'Voir toutes les réclamations';
      case AppPermissions.CLAIMS_VIEW_FACTORY:
        return 'Voir réclamations usine';
      case AppPermissions.CLAIMS_VIEW_OWN:
        return 'Voir ses réclamations';
      case AppPermissions.CLAIMS_CREATE:
        return 'Créer réclamations';
      case AppPermissions.CLAIMS_EDIT:
        return 'Modifier réclamations';
      case AppPermissions.CLAIMS_DELETE:
        return 'Supprimer réclamations';
      case AppPermissions.CLAIMS_ASSIGN:
        return 'Assigner réclamations';
      case AppPermissions.CLAIMS_CLOSE:
        return 'Clôturer réclamations';
      case AppPermissions.CLAIMS_COMMENT:
        return 'Commenter réclamations';
      case AppPermissions.TRAININGS_VIEW_ALL:
        return 'Voir toutes les formations';
      case AppPermissions.TRAININGS_VIEW_FACTORY:
        return 'Voir formations usine';
      case AppPermissions.TRAININGS_VIEW_OWN:
        return 'Voir ses formations';
      case AppPermissions.TRAININGS_CREATE:
        return 'Créer formations';
      case AppPermissions.TRAININGS_EDIT:
        return 'Modifier formations';
      case AppPermissions.TRAININGS_DELETE:
        return 'Supprimer formations';
      case AppPermissions.TRAININGS_ASSIGN:
        return 'Assigner formations';
      case AppPermissions.TRAININGS_VALIDATE:
        return 'Valider formations';
      case AppPermissions.TRAININGS_COMPLETE:
        return 'Compléter formations';
      case AppPermissions.USERS_VIEW_ALL:
        return 'Voir tous les utilisateurs';
      case AppPermissions.USERS_VIEW_FACTORY:
        return 'Voir utilisateurs usine';
      case AppPermissions.USERS_CREATE:
        return 'Créer utilisateurs';
      case AppPermissions.USERS_EDIT:
        return 'Modifier utilisateurs';
      case AppPermissions.USERS_DELETE:
        return 'Supprimer utilisateurs';
      case AppPermissions.REPORTS_VIEW_ALL:
        return 'Voir tous les rapports';
      case AppPermissions.REPORTS_VIEW_FACTORY:
        return 'Voir rapports usine';
      case AppPermissions.REPORTS_EXPORT:
        return 'Exporter rapports';
      case AppPermissions.FACTORIES_VIEW_ALL:
        return 'Voir toutes les usines';
      case AppPermissions.FACTORIES_VIEW_OWN:
        return 'Voir son usine';
      case AppPermissions.FACTORIES_CREATE:
        return 'Créer usines';
      case AppPermissions.FACTORIES_EDIT:
        return 'Modifier usines';
      case AppPermissions.FACTORIES_DELETE:
        return 'Supprimer usines';
      case AppPermissions.PROFILE_VIEW:
        return 'Voir profil';
      case AppPermissions.PROFILE_EDIT:
        return 'Modifier profil';
      default:
        return permission;
    }
  }
}

class _PermissionsMatrixTab extends StatelessWidget {
  const _PermissionsMatrixTab();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Informations utilisateur actuel
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Vos Permissions Actuelles',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          RoleBadge(role: state.user.role),
                          const SizedBox(width: 12),
                          Text(
                            state.user.fullName,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        PermissionsService.getRoleDescription(state.user.role),
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Matrice des permissions
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Matrice des Permissions par Module',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildPermissionMatrix(state.user),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPermissionMatrix(user) {
    final modules = [
      'Réclamations',
      'Formations', 
      'Utilisateurs',
      'Rapports',
      'Usines',
      'Configuration',
    ];

    return Column(
      children: modules.map((module) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Text(
                  module,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(12),
                child: _getModulePermissions(module, user),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _getModulePermissions(String module, user) {
    // Simuler les permissions par module
    return const Text(
      'Permissions détaillées à implémenter...',
      style: TextStyle(fontSize: 12, color: Colors.grey),
    );
  }
}
