<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Formulaires GCT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .success {
            background-color: #27ae60;
        }
        .success:hover {
            background-color: #229954;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔒 Test des Formulaires GCT Security App</h1>
    
    <div class="container">
        <h2>📋 Formulaire de Réclamation</h2>
        <form id="claimForm">
            <div class="form-group">
                <label for="claimTitle">Titre de la réclamation *</label>
                <input type="text" id="claimTitle" required placeholder="Ex: Équipement défaillant en zone de production">
            </div>
            
            <div class="form-group">
                <label for="claimCategory">Catégorie *</label>
                <select id="claimCategory" required>
                    <option value="">Sélectionner une catégorie</option>
                    <option value="Sécurité">Sécurité</option>
                    <option value="Équipement">Équipement</option>
                    <option value="Environnement">Environnement</option>
                    <option value="Procédures">Procédures</option>
                    <option value="Formation">Formation</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="claimPriority">Priorité *</label>
                <select id="claimPriority" required>
                    <option value="">Sélectionner une priorité</option>
                    <option value="Faible">Faible</option>
                    <option value="Moyenne">Moyenne</option>
                    <option value="Élevée">Élevée</option>
                    <option value="Critique">Critique</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="claimLocation">Localisation *</label>
                <input type="text" id="claimLocation" required placeholder="Ex: Atelier 2, Zone de production A">
            </div>
            
            <div class="form-group">
                <label for="claimDescription">Description détaillée *</label>
                <textarea id="claimDescription" required placeholder="Décrivez le problème en détail..."></textarea>
            </div>
            
            <button type="submit" class="success">📤 Envoyer Réclamation</button>
            <button type="button" onclick="clearForm('claimForm')">🗑️ Effacer</button>
        </form>
        <div id="claimStatus" class="status"></div>
    </div>
    
    <div class="container">
        <h2>🎓 Formulaire de Demande de Formation</h2>
        <form id="trainingForm">
            <div class="form-group">
                <label for="trainingTitle">Titre de la formation *</label>
                <input type="text" id="trainingTitle" required placeholder="Ex: Formation sécurité incendie">
            </div>
            
            <div class="form-group">
                <label for="trainingCategory">Catégorie *</label>
                <select id="trainingCategory" required>
                    <option value="">Sélectionner une catégorie</option>
                    <option value="Sécurité au travail">Sécurité au travail</option>
                    <option value="Premiers secours">Premiers secours</option>
                    <option value="Manipulation d'équipements">Manipulation d'équipements</option>
                    <option value="Procédures d'urgence">Procédures d'urgence</option>
                    <option value="Environnement">Environnement</option>
                    <option value="Qualité">Qualité</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="trainingUrgency">Urgence *</label>
                <select id="trainingUrgency" required>
                    <option value="">Sélectionner l'urgence</option>
                    <option value="Faible">Faible</option>
                    <option value="Normale">Normale</option>
                    <option value="Élevée">Élevée</option>
                    <option value="Urgente">Urgente</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="trainingDuration">Durée estimée *</label>
                <select id="trainingDuration" required>
                    <option value="">Sélectionner la durée</option>
                    <option value="1 heure">1 heure</option>
                    <option value="2 heures">2 heures</option>
                    <option value="1 jour">1 jour</option>
                    <option value="2 jours">2 jours</option>
                    <option value="1 semaine">1 semaine</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="trainingDescription">Description de la formation *</label>
                <textarea id="trainingDescription" required placeholder="Décrivez la formation souhaitée..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="trainingJustification">Justification de la demande *</label>
                <textarea id="trainingJustification" required placeholder="Expliquez pourquoi cette formation est nécessaire..."></textarea>
            </div>
            
            <button type="submit" class="success">📤 Envoyer Demande</button>
            <button type="button" onclick="clearForm('trainingForm')">🗑️ Effacer</button>
        </form>
        <div id="trainingStatus" class="status"></div>
    </div>

    <script>
        // Simulation de l'envoi des formulaires
        document.getElementById('claimForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('claimTitle').value,
                category: document.getElementById('claimCategory').value,
                priority: document.getElementById('claimPriority').value,
                location: document.getElementById('claimLocation').value,
                description: document.getElementById('claimDescription').value,
                timestamp: new Date().toISOString(),
                status: 'En attente',
                employeeEmail: '<EMAIL>'
            };
            
            // Simulation de sauvegarde
            console.log('Réclamation créée:', formData);
            
            // Afficher le succès
            showStatus('claimStatus', 'Réclamation envoyée avec succès ! Elle apparaîtra dans "Mes Réclamations".', 'success');
            
            // Effacer le formulaire
            clearForm('claimForm');
        });
        
        document.getElementById('trainingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('trainingTitle').value,
                category: document.getElementById('trainingCategory').value,
                urgency: document.getElementById('trainingUrgency').value,
                duration: document.getElementById('trainingDuration').value,
                description: document.getElementById('trainingDescription').value,
                justification: document.getElementById('trainingJustification').value,
                timestamp: new Date().toISOString(),
                status: 'En attente',
                employeeEmail: '<EMAIL>'
            };
            
            // Simulation de sauvegarde
            console.log('Demande de formation créée:', formData);
            
            // Afficher le succès
            showStatus('trainingStatus', 'Demande de formation envoyée avec succès ! Elle apparaîtra dans "Mes Formations".', 'success');
            
            // Effacer le formulaire
            clearForm('trainingForm');
        });
        
        function showStatus(elementId, message, type) {
            const statusElement = document.getElementById(elementId);
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
            
            // Masquer après 5 secondes
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }
        
        function clearForm(formId) {
            document.getElementById(formId).reset();
        }
    </script>
</body>
</html>
