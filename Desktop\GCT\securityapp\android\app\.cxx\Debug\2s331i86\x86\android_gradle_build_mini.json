{"buildFiles": ["C:\\Mobile\\Flutter_SDK\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Mobile\\Android_SDK\\Android_SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\GCT\\securityapp\\android\\app\\.cxx\\Debug\\2s331i86\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Mobile\\Android_SDK\\Android_SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\GCT\\securityapp\\android\\app\\.cxx\\Debug\\2s331i86\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}