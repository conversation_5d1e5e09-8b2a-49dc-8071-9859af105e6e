import 'package:cloud_firestore/cloud_firestore.dart';

enum ReportType {
  daily,
  weekly,
  monthly,
  quarterly,
  annual,
  custom,
}

enum ReportStatus {
  generating,
  completed,
  failed,
  archived,
}

class ReportModel {
  final String id;
  final String title;
  final ReportType type;
  final String period;
  final String? factoryId;
  final String? factoryName;
  final String generatedBy;
  final DateTime generatedAt;
  final ReportStatus status;
  final Map<String, dynamic> data;
  final String? filePath;
  final String? downloadUrl;

  ReportModel({
    required this.id,
    required this.title,
    required this.type,
    required this.period,
    this.factoryId,
    this.factoryName,
    required this.generatedBy,
    required this.generatedAt,
    this.status = ReportStatus.generating,
    this.data = const {},
    this.filePath,
    this.downloadUrl,
  });

  String get typeDisplayName {
    switch (type) {
      case ReportType.daily:
        return 'Quotidien';
      case ReportType.weekly:
        return 'Hebdomadaire';
      case ReportType.monthly:
        return 'Mensuel';
      case ReportType.quarterly:
        return 'Trimestriel';
      case ReportType.annual:
        return 'Annuel';
      case ReportType.custom:
        return 'Personnalisé';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case ReportStatus.generating:
        return 'En cours de génération';
      case ReportStatus.completed:
        return 'Terminé';
      case ReportStatus.failed:
        return 'Échec';
      case ReportStatus.archived:
        return 'Archivé';
    }
  }

  bool get isCompleted => status == ReportStatus.completed;
  bool get isGenerating => status == ReportStatus.generating;
  bool get hasFailed => status == ReportStatus.failed;

  // Getters pour les données du rapport
  int get totalClaims => data['totalClaims'] ?? 0;
  int get resolvedClaims => data['resolvedClaims'] ?? 0;
  int get pendingClaims => data['pendingClaims'] ?? 0;
  int get totalTrainings => data['totalTrainings'] ?? 0;
  int get completedTrainings => data['completedTrainings'] ?? 0;
  int get pendingTrainings => data['pendingTrainings'] ?? 0;
  double get safetyScore => (data['safetyScore'] ?? 0.0).toDouble();
  int get incidents => data['incidents'] ?? 0;
  int get nearMisses => data['nearMisses'] ?? 0;

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'period': period,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'generatedBy': generatedBy,
      'generatedAt': generatedAt,
      'status': status.name,
      'data': data,
      'filePath': filePath,
      'downloadUrl': downloadUrl,
    };
  }

  // Création depuis Map Firestore
  factory ReportModel.fromMap(Map<String, dynamic> map) {
    return ReportModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      type: ReportType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ReportType.custom,
      ),
      period: map['period'] ?? '',
      factoryId: map['factoryId'],
      factoryName: map['factoryName'],
      generatedBy: map['generatedBy'] ?? '',
      generatedAt: (map['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: ReportStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ReportStatus.generating,
      ),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      filePath: map['filePath'],
      downloadUrl: map['downloadUrl'],
    );
  }

  // Création depuis DocumentSnapshot
  factory ReportModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReportModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  ReportModel copyWith({
    String? id,
    String? title,
    ReportType? type,
    String? period,
    String? factoryId,
    String? factoryName,
    String? generatedBy,
    DateTime? generatedAt,
    ReportStatus? status,
    Map<String, dynamic>? data,
    String? filePath,
    String? downloadUrl,
  }) {
    return ReportModel(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      period: period ?? this.period,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      generatedBy: generatedBy ?? this.generatedBy,
      generatedAt: generatedAt ?? this.generatedAt,
      status: status ?? this.status,
      data: data ?? this.data,
      filePath: filePath ?? this.filePath,
      downloadUrl: downloadUrl ?? this.downloadUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReportModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ReportModel(id: $id, title: $title, type: $type, status: $status)';
  }
}
