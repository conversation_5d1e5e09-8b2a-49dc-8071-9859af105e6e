import 'package:flutter/material.dart';
import '../services/firebase_user_service.dart';
import '../theme/app_colors.dart';
import 'firebase_auth_page.dart';

class FirebaseSetupPage extends StatefulWidget {
  const FirebaseSetupPage({super.key});

  @override
  State<FirebaseSetupPage> createState() => _FirebaseSetupPageState();
}

class _FirebaseSetupPageState extends State<FirebaseSetupPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _setupResult;

  Future<void> _initializeUsers() async {
    setState(() {
      _isLoading = true;
      _setupResult = null;
    });

    try {
      final result = await FirebaseUserService.initializePredefinedUsers();
      setState(() {
        _setupResult = result;
      });
    } catch (e) {
      setState(() {
        _setupResult = {
          'success': false,
          'errors': ['Erreur: $e'],
          'created': [],
          'existing': [],
          'total': 0,
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Configuration Firebase',
          style: TextStyle(
            color: AppColors.grey900,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [AppColors.primary, Color(0xFF1976D2)],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.cloud_sync,
                    color: Colors.white,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Initialisation Firebase',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Créer les comptes utilisateurs prédéfinis dans Firebase Authentication et Firestore',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Comptes à créer
            const Text(
              'Comptes à créer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: AppColors.grey900,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildUserCard('👑 Super Admin', '<EMAIL>', 'admin123', 'Accès total'),
            _buildUserCard('🛡️ Security Admin', '<EMAIL>', 'security123', 'Sécurité'),
            _buildUserCard('🏭 Factory Admin Gabès', '<EMAIL>', 'gabes123', 'Usine Gabès'),
            _buildUserCard('🏭 Factory Admin Sfax', '<EMAIL>', 'sfax123', 'Usine Sfax'),
            _buildUserCard('👷 Employé Gabès', '<EMAIL>', 'emp123', 'Production'),
            _buildUserCard('👷 Employé Sfax', '<EMAIL>', 'emp123', 'Production'),
            
            const SizedBox(height: 32),
            
            // Bouton d'initialisation
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _initializeUsers,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Initialiser les comptes Firebase',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Résultats
            if (_setupResult != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: _setupResult!['success'] 
                      ? AppColors.success.withOpacity(0.1)
                      : AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _setupResult!['success'] 
                        ? AppColors.success.withOpacity(0.3)
                        : AppColors.error.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _setupResult!['success'] ? Icons.check_circle : Icons.error,
                          color: _setupResult!['success'] ? AppColors.success : AppColors.error,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _setupResult!['success'] ? 'Initialisation réussie' : 'Erreurs détectées',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: _setupResult!['success'] ? AppColors.success : AppColors.error,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    if (_setupResult!['created'].isNotEmpty) ...[
                      Text(
                        'Comptes créés (${_setupResult!['created'].length}):',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      ...(_setupResult!['created'] as List).map((email) => 
                        Padding(
                          padding: const EdgeInsets.only(left: 16, bottom: 4),
                          child: Row(
                            children: [
                              const Icon(Icons.check, color: AppColors.success, size: 16),
                              const SizedBox(width: 8),
                              Text(email),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                    ],
                    
                    if (_setupResult!['existing'].isNotEmpty) ...[
                      Text(
                        'Comptes existants (${_setupResult!['existing'].length}):',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      ...(_setupResult!['existing'] as List).map((email) => 
                        Padding(
                          padding: const EdgeInsets.only(left: 16, bottom: 4),
                          child: Row(
                            children: [
                              const Icon(Icons.info, color: AppColors.info, size: 16),
                              const SizedBox(width: 8),
                              Text(email),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                    ],
                    
                    if (_setupResult!['errors'].isNotEmpty) ...[
                      Text(
                        'Erreurs (${_setupResult!['errors'].length}):',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      ...(_setupResult!['errors'] as List).map((error) => 
                        Padding(
                          padding: const EdgeInsets.only(left: 16, bottom: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(Icons.error, color: AppColors.error, size: 16),
                              const SizedBox(width: 8),
                              Expanded(child: Text(error)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Bouton pour aller à la connexion
              if (_setupResult!['success'] || _setupResult!['existing'].isNotEmpty)
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FirebaseAuthPage(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Aller à la connexion Firebase',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserCard(String title, String email, String password, String role) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.grey900,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Email: $email', style: TextStyle(color: AppColors.grey600)),
                    Text('Mot de passe: $password', style: TextStyle(color: AppColors.grey600)),
                    Text('Rôle: $role', style: TextStyle(color: AppColors.primary, fontSize: 12)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
