import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

abstract class AuthLocalDataSource {
  Future<void> saveUserData(UserModel user);
  Future<UserModel?> getUserData();
  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
  });
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<void> clearUserData();
  Future<bool> isLoggedIn();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl(this.sharedPreferences);

  @override
  Future<void> saveUserData(UserModel user) async {
    try {
      final userJson = json.encode(user.toJson());
      await sharedPreferences.setString(AppConstants.userDataKey, userJson);
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la sauvegarde des données utilisateur: $e');
    }
  }

  @override
  Future<UserModel?> getUserData() async {
    try {
      final userJson = sharedPreferences.getString(AppConstants.userDataKey);
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      }
      return null;
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la récupération des données utilisateur: $e');
    }
  }

  @override
  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    try {
      await sharedPreferences.setString(AppConstants.userTokenKey, accessToken);
      if (refreshToken != null) {
        await sharedPreferences.setString('refresh_token', refreshToken);
      }
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la sauvegarde des tokens: $e');
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      return sharedPreferences.getString(AppConstants.userTokenKey);
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la récupération du token: $e');
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return sharedPreferences.getString('refresh_token');
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la récupération du refresh token: $e');
    }
  }

  @override
  Future<void> clearUserData() async {
    try {
      await Future.wait([
        sharedPreferences.remove(AppConstants.userDataKey),
        sharedPreferences.remove(AppConstants.userTokenKey),
        sharedPreferences.remove('refresh_token'),
      ]);
    } catch (e) {
      throw CacheException(message: 'Erreur lors de la suppression des données: $e');
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final token = sharedPreferences.getString(AppConstants.userTokenKey);
      final userData = sharedPreferences.getString(AppConstants.userDataKey);
      return token != null && userData != null;
    } catch (e) {
      return false;
    }
  }
}
