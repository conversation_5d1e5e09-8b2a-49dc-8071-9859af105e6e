import 'dart:typed_data';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../../firebase_options.dart';
import '../errors/exceptions.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseMessaging get messaging => FirebaseMessaging.instance;

  // Collections
  static const String usersCollection = 'users';
  static const String claimsCollection = 'claims';
  static const String trainingsCollection = 'trainings';
  static const String factoriesCollection = 'factories';
  static const String userTrainingsCollection = 'user_trainings';
  static const String notificationsCollection = 'notifications';

  // Initialize Firebase
  static Future<void> initialize() async {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Configure Firestore settings
      firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // Enable offline persistence
      await firestore.enableNetwork();

    } catch (e) {
      throw ServerException(message: 'Erreur d\'initialisation Firebase: $e');
    }
  }

  // Get current user ID
  static String? get currentUserId => auth.currentUser?.uid;

  // Check if user is authenticated
  static bool get isAuthenticated => auth.currentUser != null;

  // Get user document reference
  static DocumentReference getUserDoc(String userId) {
    return firestore.collection(usersCollection).doc(userId);
  }

  // Get claims collection reference
  static CollectionReference get claimsRef {
    return firestore.collection(claimsCollection);
  }

  // Get trainings collection reference
  static CollectionReference get trainingsRef {
    return firestore.collection(trainingsCollection);
  }

  // Get factories collection reference
  static CollectionReference get factoriesRef {
    return firestore.collection(factoriesCollection);
  }

  // Get user trainings collection reference
  static CollectionReference get userTrainingsRef {
    return firestore.collection(userTrainingsCollection);
  }

  // Get notifications collection reference
  static CollectionReference get notificationsRef {
    return firestore.collection(notificationsCollection);
  }

  // Batch operations
  static WriteBatch batch() => firestore.batch();

  // Transaction
  static Future<T> runTransaction<T>(
    Future<T> Function(Transaction transaction) updateFunction,
  ) {
    return firestore.runTransaction(updateFunction);
  }

  // Storage references
  static Reference getStorageRef(String path) {
    return storage.ref().child(path);
  }

  // TODO: Upload file to Firebase Storage - Temporarily disabled
  static Future<String> uploadFile({
    required String path,
    required List<int> bytes,
    String? contentType,
  }) async {
    // TODO: Implement file upload when needed
    throw UnimplementedError('File upload not implemented yet');
    /*
    try {
      final ref = storage.ref().child(path);
      final metadata = SettableMetadata(contentType: contentType);

      // Convert List<int> to Uint8List
      final uint8List = Uint8List.fromList(bytes);
      final uploadTask = ref.putData(uint8List, metadata);
      final snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw FileException(message: 'Erreur lors de l\'upload: $e');
    }
    */
  }

  // Delete file from Firebase Storage
  static Future<void> deleteFile(String path) async {
    try {
      await storage.ref().child(path).delete();
    } catch (e) {
      throw FileException(message: 'Erreur lors de la suppression: $e');
    }
  }

  // Get FCM token
  static Future<String?> getFCMToken() async {
    try {
      return await messaging.getToken();
    } catch (e) {
      return null;
    }
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await messaging.subscribeToTopic(topic);
    } catch (e) {
      // Ignore errors for now
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await messaging.unsubscribeFromTopic(topic);
    } catch (e) {
      // Ignore errors for now
    }
  }

  // Error handling helper
  static Exception handleFirebaseError(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return const AuthenticationException(message: 'Utilisateur non trouvé');
        case 'wrong-password':
          return const AuthenticationException(message: 'Mot de passe incorrect');
        case 'email-already-in-use':
          return const AuthenticationException(message: 'Email déjà utilisé');
        case 'weak-password':
          return const AuthenticationException(message: 'Mot de passe trop faible');
        case 'invalid-email':
          return const ValidationException(message: 'Email invalide');
        case 'user-disabled':
          return const AuthenticationException(message: 'Compte désactivé');
        case 'too-many-requests':
          return const AuthenticationException(message: 'Trop de tentatives, réessayez plus tard');
        default:
          return AuthenticationException(message: error.message ?? 'Erreur d\'authentification');
      }
    }
    
    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return const AuthorizationException(message: 'Permissions insuffisantes');
        case 'unavailable':
          return const NetworkException(message: 'Service temporairement indisponible');
        case 'deadline-exceeded':
          return const NetworkException(message: 'Délai d\'attente dépassé');
        default:
          return ServerException(message: error.message ?? 'Erreur serveur');
      }
    }
    
    return ServerException(message: 'Erreur inattendue: $error');
  }

  // Dispose resources
  static Future<void> dispose() async {
    // Clean up any resources if needed
  }
}
