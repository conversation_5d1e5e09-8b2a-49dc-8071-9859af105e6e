import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/training_model.dart';

class TrainingCard extends StatelessWidget {
  final TrainingModel training;
  final VoidCallback? onTap;

  const TrainingCard({
    super.key,
    required this.training,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getStatusColor().withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and type
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatusChip(),
                _buildTypeChip(),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Title
            Text(
              training.title,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 8),
            
            // Description
            Text(
              training.description,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 16),
            
            // Footer with duration and date
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.schedule_rounded,
                      size: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${training.durationHours}h',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (training.isMandatory) ...[
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppTheme.errorColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'OBLIGATOIRE',
                          style: GoogleFonts.inter(
                            fontSize: 10,
                            fontWeight: FontWeight.w700,
                            color: AppTheme.errorColor,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(training.requestedAt),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            
            // Progress or completion info
            if (training.status == TrainingStatus.completed && training.expiresAt != null) ...[
              const SizedBox(height: 12),
              _buildExpirationInfo(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    final color = _getStatusColor();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        training.statusDisplayName,
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildTypeChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.infoColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getTypeIcon(),
            size: 14,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: 4),
          Text(
            training.typeDisplayName,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: AppTheme.infoColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpirationInfo() {
    final remaining = training.remainingValidity;
    if (remaining == null) return const SizedBox.shrink();
    
    final isExpired = remaining == Duration.zero;
    final daysRemaining = remaining.inDays;
    
    Color color;
    String text;
    IconData icon;
    
    if (isExpired) {
      color = AppTheme.errorColor;
      text = 'Expiré';
      icon = Icons.error_rounded;
    } else if (daysRemaining <= 30) {
      color = AppTheme.warningColor;
      text = 'Expire dans $daysRemaining jours';
      icon = Icons.warning_rounded;
    } else {
      color = AppTheme.successColor;
      text = 'Valide $daysRemaining jours';
      icon = Icons.check_circle_rounded;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (training.status) {
      case TrainingStatus.pending:
        return AppTheme.warningColor;
      case TrainingStatus.approved:
        return AppTheme.infoColor;
      case TrainingStatus.rejected:
        return AppTheme.errorColor;
      case TrainingStatus.completed:
        return AppTheme.successColor;
      case TrainingStatus.expired:
        return AppTheme.textSecondaryColor;
    }
  }

  IconData _getTypeIcon() {
    switch (training.type) {
      case TrainingType.safety:
        return Icons.security_rounded;
      case TrainingType.security:
        return Icons.shield_rounded;
      case TrainingType.emergency:
        return Icons.emergency_rounded;
      case TrainingType.equipment:
        return Icons.build_rounded;
      case TrainingType.procedure:
        return Icons.description_rounded;
    }
  }
}
