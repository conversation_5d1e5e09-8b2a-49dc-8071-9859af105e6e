import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/claim_model.dart';
import '../models/training_model.dart';
import '../models/factory_model.dart';

class FirestoreService extends GetxService {
  static FirestoreService get instance => Get.find();
  
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collections
  static const String usersCollection = 'users';
  static const String claimsCollection = 'claims';
  static const String trainingsCollection = 'trainings';
  static const String factoriesCollection = 'factories';

  // ==================== USERS ====================
  
  // Créer un utilisateur
  Future<void> createUser(UserModel user) async {
    await _db.collection(usersCollection).doc(user.id).set(user.toMap());
  }

  // Obtenir un utilisateur par ID
  Future<UserModel?> getUserById(String userId) async {
    final doc = await _db.collection(usersCollection).doc(userId).get();
    if (doc.exists) {
      return UserModel.fromDocument(doc);
    }
    return null;
  }

  // Obtenir tous les utilisateurs
  Stream<List<UserModel>> getAllUsers() {
    return _db.collection(usersCollection)
        .snapshots()
        .map((snapshot) {
          print('FirestoreService: Récupération de ${snapshot.docs.length} documents utilisateurs');
          final users = <UserModel>[];

          for (final doc in snapshot.docs) {
            try {
              final user = UserModel.fromDocument(doc);
              users.add(user);
              print('Utilisateur ajouté: ${user.email} (${user.role})');
            } catch (e) {
              print('Erreur lors du parsing de l\'utilisateur ${doc.id}: $e');
              print('Données du document: ${doc.data()}');
            }
          }

          // Trier par date de création si disponible
          users.sort((a, b) {
            if (a.createdAt != null && b.createdAt != null) {
              return b.createdAt!.compareTo(a.createdAt!);
            }
            return 0;
          });

          print('FirestoreService: ${users.length} utilisateurs traités avec succès');
          return users;
        });
  }

  // Obtenir les utilisateurs par usine
  Stream<List<UserModel>> getUsersByFactory(String factory) {
    return _db.collection(usersCollection)
        .where('factory', isEqualTo: factory)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => UserModel.fromDocument(doc))
            .toList());
  }

  // Mettre à jour un utilisateur
  Future<void> updateUser(UserModel user) async {
    await _db.collection(usersCollection).doc(user.id).update(
      user.copyWith(updatedAt: DateTime.now()).toMap(),
    );
  }

  // ==================== CLAIMS ====================
  
  // Créer une réclamation
  Future<String> createClaim(ClaimModel claim) async {
    final docRef = await _db.collection(claimsCollection).add(claim.toMap());
    return docRef.id;
  }

  // Obtenir toutes les réclamations
  Stream<List<ClaimModel>> getAllClaims() {
    return _db.collection(claimsCollection)
        .snapshots()
        .map((snapshot) {
          print('FirestoreService: Récupération de ${snapshot.docs.length} documents réclamations');
          final claims = <ClaimModel>[];

          for (final doc in snapshot.docs) {
            try {
              final claim = ClaimModel.fromDocument(doc);
              claims.add(claim);
              print('Réclamation ajoutée: ${claim.title} (${claim.status})');
            } catch (e) {
              print('Erreur lors du parsing de la réclamation ${doc.id}: $e');
              print('Données du document: ${doc.data()}');
            }
          }

          // Trier par date de création si disponible
          claims.sort((a, b) {
            if (a.createdAt != null && b.createdAt != null) {
              return b.createdAt!.compareTo(a.createdAt!);
            }
            return 0;
          });

          print('FirestoreService: ${claims.length} réclamations traitées avec succès');
          return claims;
        });
  }

  // Obtenir les réclamations par utilisateur
  Stream<List<ClaimModel>> getClaimsByUser(String userId) {
    return _db.collection(claimsCollection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClaimModel.fromDocument(doc))
            .toList());
  }

  // Obtenir les réclamations par usine
  Stream<List<ClaimModel>> getClaimsByFactory(String factory) {
    return _db.collection(claimsCollection)
        .where('factory', isEqualTo: factory)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClaimModel.fromDocument(doc))
            .toList());
  }

  // Mettre à jour une réclamation
  Future<void> updateClaim(ClaimModel claim) async {
    await _db.collection(claimsCollection).doc(claim.id).update(
      claim.copyWith(updatedAt: DateTime.now()).toMap(),
    );
  }

  // ==================== TRAININGS ====================
  
  // Créer une demande de formation
  Future<String> createTraining(TrainingModel training) async {
    final docRef = await _db.collection(trainingsCollection).add(training.toMap());
    return docRef.id;
  }

  // Obtenir toutes les formations
  Stream<List<TrainingModel>> getAllTrainings() {
    return _db.collection(trainingsCollection)
        .snapshots()
        .map((snapshot) {
          print('FirestoreService: Récupération de ${snapshot.docs.length} documents formations');
          final trainings = <TrainingModel>[];

          for (final doc in snapshot.docs) {
            try {
              final training = TrainingModel.fromDocument(doc);
              trainings.add(training);
              print('Formation ajoutée: ${training.title} (${training.status})');
            } catch (e) {
              print('Erreur lors du parsing de la formation ${doc.id}: $e');
              print('Données du document: ${doc.data()}');
            }
          }

          // Trier par date de demande si disponible
          trainings.sort((a, b) {
            if (a.requestedAt != null && b.requestedAt != null) {
              return b.requestedAt!.compareTo(a.requestedAt!);
            }
            return 0;
          });

          print('FirestoreService: ${trainings.length} formations traitées avec succès');
          return trainings;
        });
  }

  // Obtenir les formations par utilisateur
  Stream<List<TrainingModel>> getTrainingsByUser(String userId) {
    return _db.collection(trainingsCollection)
        .where('userId', isEqualTo: userId)
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TrainingModel.fromDocument(doc))
            .toList());
  }

  // Obtenir les formations par usine
  Stream<List<TrainingModel>> getTrainingsByFactory(String factory) {
    return _db.collection(trainingsCollection)
        .where('factory', isEqualTo: factory)
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TrainingModel.fromDocument(doc))
            .toList());
  }

  // Mettre à jour une formation
  Future<void> updateTraining(TrainingModel training) async {
    await _db.collection(trainingsCollection).doc(training.id).update(
      training.toMap(),
    );
  }

  // ==================== FACTORIES ====================
  
  // Créer une usine
  Future<void> createFactory(FactoryModel factory) async {
    await _db.collection(factoriesCollection).doc(factory.id).set(factory.toMap());
  }

  // Obtenir toutes les usines
  Stream<List<FactoryModel>> getAllFactories() {
    return _db.collection(factoriesCollection)
        .snapshots()
        .map((snapshot) {
          print('FirestoreService: Récupération de ${snapshot.docs.length} documents usines');
          final factories = <FactoryModel>[];

          for (final doc in snapshot.docs) {
            try {
              final factory = FactoryModel.fromDocument(doc);
              // Filtrer seulement les usines actives
              if (factory.isActive) {
                factories.add(factory);
                print('Usine ajoutée: ${factory.name} (${factory.code})');
              }
            } catch (e) {
              print('Erreur lors du parsing de l\'usine ${doc.id}: $e');
              print('Données du document: ${doc.data()}');
            }
          }

          // Trier par nom
          factories.sort((a, b) => a.name.compareTo(b.name));

          print('FirestoreService: ${factories.length} usines traitées avec succès');
          return factories;
        });
  }

  // Obtenir une usine par ID
  Future<FactoryModel?> getFactoryById(String factoryId) async {
    final doc = await _db.collection(factoriesCollection).doc(factoryId).get();
    if (doc.exists) {
      return FactoryModel.fromDocument(doc);
    }
    return null;
  }

  // ==================== STATISTIQUES ====================
  
  // Obtenir les statistiques globales
  Future<Map<String, int>> getGlobalStats() async {
    final usersCount = await _db.collection(usersCollection)
        .where('isActive', isEqualTo: true)
        .count()
        .get();
    
    final claimsCount = await _db.collection(claimsCollection)
        .count()
        .get();
    
    final trainingsCount = await _db.collection(trainingsCollection)
        .count()
        .get();
    
    final factoriesCount = await _db.collection(factoriesCollection)
        .where('isActive', isEqualTo: true)
        .count()
        .get();

    return {
      'users': usersCount.count ?? 0,
      'claims': claimsCount.count ?? 0,
      'trainings': trainingsCount.count ?? 0,
      'factories': factoriesCount.count ?? 0,
    };
  }

  // Obtenir les statistiques par usine
  Future<Map<String, int>> getFactoryStats(String factory) async {
    final usersCount = await _db.collection(usersCollection)
        .where('factory', isEqualTo: factory)
        .where('isActive', isEqualTo: true)
        .count()
        .get();
    
    final claimsCount = await _db.collection(claimsCollection)
        .where('factory', isEqualTo: factory)
        .count()
        .get();
    
    final trainingsCount = await _db.collection(trainingsCollection)
        .where('factory', isEqualTo: factory)
        .count()
        .get();

    return {
      'users': usersCount.count ?? 0,
      'claims': claimsCount.count ?? 0,
      'trainings': trainingsCount.count ?? 0,
    };
  }

  // ==================== INITIALISATION ====================

  // Initialiser les données par défaut
  Future<void> initializeDefaultData() async {
    // Créer les usines par défaut
    for (final factory in FactoryModel.defaultFactories) {
      final exists = await getFactoryById(factory.id);
      if (exists == null) {
        await createFactory(factory);
      }
    }

    // Créer des données de test pour les usines
    await _createGabesTestData();
    await _createSfaxTestData();
  }

  // Créer des données de test pour l'usine de Gabès
  Future<void> _createGabesTestData() async {
    print('Création des données de test pour l\'usine de Gabès...');

    try {
      // Créer plusieurs utilisateurs employés de test pour Gabès
      final gabesEmployees = [
        UserModel(
          id: 'employee_gabes_1',
          email: '<EMAIL>',
          firstName: 'Ahmed',
          lastName: 'Ben Ali',
          role: 'employe',
          factory: 'Gabès',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
        UserModel(
          id: 'employee_gabes_2',
          email: '<EMAIL>',
          firstName: 'Fatma',
          lastName: 'Trabelsi',
          role: 'employe',
          factory: 'Gabès',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 25)),
        ),
        UserModel(
          id: 'employee_gabes_3',
          email: '<EMAIL>',
          firstName: 'Mohamed',
          lastName: 'Sassi',
          role: 'employe',
          factory: 'Gabès',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
        ),
        UserModel(
          id: 'employee_gabes_4',
          email: '<EMAIL>',
          firstName: 'Salma',
          lastName: 'Ben Salem',
          role: 'employe',
          factory: 'Gabès',
          isActive: false, // Un employé inactif pour tester
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
        UserModel(
          id: 'supervisor_gabes_1',
          email: '<EMAIL>',
          firstName: 'Karim',
          lastName: 'Supervisor',
          role: 'superviseur',
          factory: 'Gabès',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
        ),
      ];

      // Créer tous les employés
      for (final employee in gabesEmployees) {
        final employeeDoc = await _db.collection(usersCollection).doc(employee.id).get();
        if (!employeeDoc.exists) {
          await _db.collection(usersCollection).doc(employee.id).set(employee.toMap());
          print('Utilisateur créé: ${employee.email} (${employee.role})');
        }
      }
      // Créer des réclamations de test pour Gabès avec différents employés
      final gabesClaims = [
        ClaimModel(
          id: 'claim_gabes_1',
          title: 'Fuite de produit chimique - Zone Production',
          description: 'Fuite détectée dans la zone de production principale',
          type: ClaimType.hazard,
          priority: ClaimPriority.high,
          status: ClaimStatus.pending,
          factory: 'Gabès',
          userId: 'employee_gabes_1',
          userEmail: '<EMAIL>',
          userName: 'Ahmed Ben Ali',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        ClaimModel(
          id: 'claim_gabes_2',
          title: 'Équipement de protection défaillant',
          description: 'Les masques de protection ne fonctionnent plus correctement',
          type: ClaimType.nonCompliance,
          priority: ClaimPriority.medium,
          status: ClaimStatus.inProgress,
          factory: 'Gabès',
          userId: 'employee_gabes_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        ClaimModel(
          id: 'claim_gabes_3',
          title: 'Amélioration éclairage atelier',
          description: 'L\'éclairage de l\'atelier B est insuffisant',
          type: ClaimType.suggestion,
          priority: ClaimPriority.low,
          status: ClaimStatus.resolved,
          factory: 'Gabès',
          userId: 'employee_gabes_3',
          userEmail: '<EMAIL>',
          userName: 'Mohamed Sassi',
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
        ClaimModel(
          id: 'claim_gabes_4',
          title: 'Incident machine - Ligne 2',
          description: 'Arrêt imprévu de la machine sur la ligne 2',
          type: ClaimType.incident,
          priority: ClaimPriority.high,
          status: ClaimStatus.inProgress,
          factory: 'Gabès',
          userId: 'supervisor_gabes_1',
          userEmail: '<EMAIL>',
          userName: 'Karim Supervisor',
          createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        ClaimModel(
          id: 'claim_gabes_5',
          title: 'Presque accident - Zone stockage',
          description: 'Un employé a failli être blessé dans la zone de stockage',
          type: ClaimType.nearMiss,
          priority: ClaimPriority.critical,
          status: ClaimStatus.pending,
          factory: 'Gabès',
          userId: 'employee_gabes_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
      ];

      // Créer des formations de test pour Gabès avec différents employés
      final gabesTrainings = [
        TrainingModel(
          id: 'training_gabes_1',
          title: 'Formation Sécurité Chimique Avancée',
          description: 'Formation sur la manipulation des produits chimiques dangereux',
          type: TrainingType.safety,
          status: TrainingStatus.pending,
          factory: 'Gabès',
          userId: 'employee_gabes_1',
          userEmail: '<EMAIL>',
          userName: 'Ahmed Ben Ali',
          requestedAt: DateTime.now().subtract(const Duration(hours: 6)),
          durationHours: 8,
          isMandatory: true,
        ),
        TrainingModel(
          id: 'training_gabes_2',
          title: 'Formation Premiers Secours',
          description: 'Formation aux gestes de premiers secours en milieu industriel',
          type: TrainingType.emergency,
          status: TrainingStatus.approved,
          factory: 'Gabès',
          userId: 'employee_gabes_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          requestedAt: DateTime.now().subtract(const Duration(days: 2)),
          durationHours: 4,
          isMandatory: false,
        ),
        TrainingModel(
          id: 'training_gabes_3',
          title: 'Formation Manipulation Équipements',
          description: 'Formation sur l\'utilisation sécurisée des équipements industriels',
          type: TrainingType.equipment,
          status: TrainingStatus.completed,
          factory: 'Gabès',
          userId: 'employee_gabes_3',
          userEmail: '<EMAIL>',
          userName: 'Mohamed Sassi',
          requestedAt: DateTime.now().subtract(const Duration(days: 10)),
          durationHours: 6,
          isMandatory: true,
        ),
        TrainingModel(
          id: 'training_gabes_4',
          title: 'Formation Procédures de Sécurité',
          description: 'Formation sur les nouvelles procédures de sécurité',
          type: TrainingType.procedure,
          status: TrainingStatus.pending,
          factory: 'Gabès',
          userId: 'supervisor_gabes_1',
          userEmail: '<EMAIL>',
          userName: 'Karim Supervisor',
          requestedAt: DateTime.now().subtract(const Duration(hours: 12)),
          durationHours: 3,
          isMandatory: false,
        ),
      ];

      // Ajouter les réclamations à Firestore
      for (final claim in gabesClaims) {
        final docRef = _db.collection(claimsCollection).doc(claim.id);
        final exists = await docRef.get();
        if (!exists.exists) {
          await docRef.set(claim.toMap());
          print('Réclamation créée: ${claim.title}');
        }
      }

      // Ajouter les formations à Firestore
      for (final training in gabesTrainings) {
        final docRef = _db.collection(trainingsCollection).doc(training.id);
        final exists = await docRef.get();
        if (!exists.exists) {
          await docRef.set(training.toMap());
          print('Formation créée: ${training.title}');
        }
      }

      print('Données de test pour Gabès créées avec succès!');
    } catch (e) {
      print('Erreur lors de la création des données de test pour Gabès: $e');
    }
  }

  // Créer des données de test pour l'usine de Sfax
  Future<void> _createSfaxTestData() async {
    print('Création des données de test pour l\'usine de Sfax...');

    try {
      // Créer un utilisateur employé de test pour Sfax
      final sfaxEmployee = UserModel(
        id: 'employee_sfax_2',
        email: '<EMAIL>',
        firstName: 'Fatma',
        lastName: 'Trabelsi',
        role: 'employe',
        factory: 'Sfax',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
      );

      final employeeDoc = await _db.collection(usersCollection).doc(sfaxEmployee.id).get();
      if (!employeeDoc.exists) {
        await _db.collection(usersCollection).doc(sfaxEmployee.id).set(sfaxEmployee.toMap());
        print('Utilisateur employé créé: ${sfaxEmployee.email}');
      }

      // Créer des réclamations de test pour Sfax
      final sfaxClaims = [
        ClaimModel(
          id: 'claim_sfax_1',
          title: 'Problème de ventilation - Atelier C',
          description: 'Le système de ventilation de l\'atelier C ne fonctionne pas correctement',
          type: ClaimType.nonCompliance,
          priority: ClaimPriority.high,
          status: ClaimStatus.pending,
          factory: 'Sfax',
          userId: 'employee_sfax_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        ClaimModel(
          id: 'claim_sfax_2',
          title: 'Machine défectueuse - Ligne 3',
          description: 'La machine de la ligne 3 fait des bruits anormaux',
          type: ClaimType.incident,
          priority: ClaimPriority.medium,
          status: ClaimStatus.inProgress,
          factory: 'Sfax',
          userId: 'employee_sfax_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
        ClaimModel(
          id: 'claim_sfax_3',
          title: 'Suggestion amélioration process',
          description: 'Proposition d\'amélioration du processus de production',
          type: ClaimType.suggestion,
          priority: ClaimPriority.low,
          status: ClaimStatus.resolved,
          factory: 'Sfax',
          userId: 'employee_sfax_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
        ),
      ];

      // Créer des formations de test pour Sfax
      final sfaxTrainings = [
        TrainingModel(
          id: 'training_sfax_1',
          title: 'Formation Manipulation d\'Équipements',
          description: 'Formation sur l\'utilisation sécurisée des équipements industriels',
          type: TrainingType.equipment,
          status: TrainingStatus.pending,
          factory: 'Sfax',
          userId: 'employee_sfax_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          requestedAt: DateTime.now().subtract(const Duration(hours: 8)),
          durationHours: 6,
          isMandatory: true,
        ),
        TrainingModel(
          id: 'training_sfax_2',
          title: 'Formation Procédures d\'Urgence',
          description: 'Formation sur les procédures d\'évacuation et d\'urgence',
          type: TrainingType.emergency,
          status: TrainingStatus.approved,
          factory: 'Sfax',
          userId: 'employee_sfax_2',
          userEmail: '<EMAIL>',
          userName: 'Fatma Trabelsi',
          requestedAt: DateTime.now().subtract(const Duration(days: 1)),
          durationHours: 4,
          isMandatory: false,
        ),
      ];

      // Ajouter les réclamations à Firestore
      for (final claim in sfaxClaims) {
        final docRef = _db.collection(claimsCollection).doc(claim.id);
        final exists = await docRef.get();
        if (!exists.exists) {
          await docRef.set(claim.toMap());
          print('Réclamation créée: ${claim.title}');
        }
      }

      // Ajouter les formations à Firestore
      for (final training in sfaxTrainings) {
        final docRef = _db.collection(trainingsCollection).doc(training.id);
        final exists = await docRef.get();
        if (!exists.exists) {
          await docRef.set(training.toMap());
          print('Formation créée: ${training.title}');
        }
      }

      print('Données de test pour Sfax créées avec succès!');
    } catch (e) {
      print('Erreur lors de la création des données de test pour Sfax: $e');
    }
  }
}
