import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String role;
  final String? factory;
  final String? phone;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLogin;
  final bool isActive;

  UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.factory,
    this.phone,
    required this.createdAt,
    this.updatedAt,
    this.lastLogin,
    this.isActive = true,
  });

  String get fullName => '$firstName $lastName';

  String get displayName {
    switch (role) {
      case 'super_admin':
        return 'Super Administrateur';
      case 'admin_securite_gct':
        return 'Admin Sécurité GCT';
      case 'admin_usine':
        return 'Admin Usine';
      case 'employe':
        return 'Employé';
      default:
        return 'Utilisateur';
    }
  }

  bool get isAdmin => ['super_admin', 'admin_securite_gct', 'admin_usine'].contains(role);
  bool get isSuperAdmin => role == 'super_admin';
  bool get isSecurityAdmin => role == 'admin_securite_gct';
  bool get isFactoryAdmin => role == 'admin_usine';
  bool get isEmployee => role == 'employe';

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'role': role,
      'factory': factory,
      'phone': phone,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'lastLogin': lastLogin,
      'isActive': isActive,
    };
  }

  // Création depuis Map Firestore
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      role: map['role'] ?? 'employe',
      factory: map['factory'],
      phone: map['phone'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      lastLogin: (map['lastLogin'] as Timestamp?)?.toDate(),
      isActive: map['isActive'] ?? true,
    );
  }

  // Création depuis DocumentSnapshot
  factory UserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? role,
    String? factory,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      role: role ?? this.role,
      factory: factory ?? this.factory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, role: $role, factory: $factory)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
