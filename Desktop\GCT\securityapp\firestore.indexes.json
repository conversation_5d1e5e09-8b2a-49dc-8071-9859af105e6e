{"indexes": [{"collectionGroup": "claims", "queryScope": "COLLECTION", "fields": [{"fieldPath": "factory", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "claims", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trainings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "factory", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "requestedAt", "order": "DESCENDING"}]}, {"collectionGroup": "trainings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "requestedAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "factory", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}