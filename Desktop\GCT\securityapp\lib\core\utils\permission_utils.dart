import 'package:permission_handler/permission_handler.dart';
import '../errors/exceptions.dart';

class PermissionUtils {
  // Request camera permission
  static Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation caméra: $e');
    }
  }

  // Request storage permission
  static Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      return status.isGranted;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation stockage: $e');
    }
  }

  // Request location permission
  static Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.location.request();
      return status.isGranted;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation localisation: $e');
    }
  }

  // Request notification permission
  static Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      return status.isGranted;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation notifications: $e');
    }
  }

  // Request microphone permission
  static Future<bool> requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      return status.isGranted;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation microphone: $e');
    }
  }

  // Check camera permission
  static Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  // Check storage permission
  static Future<bool> hasStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  // Check location permission
  static Future<bool> hasLocationPermission() async {
    try {
      final status = await Permission.location.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  // Check notification permission
  static Future<bool> hasNotificationPermission() async {
    try {
      final status = await Permission.notification.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  // Check microphone permission
  static Future<bool> hasMicrophonePermission() async {
    try {
      final status = await Permission.microphone.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  // Request multiple permissions
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    List<Permission> permissions,
  ) async {
    try {
      return await permissions.request();
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisations: $e');
    }
  }

  // Check if permission is permanently denied
  static Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    try {
      final status = await permission.status;
      return status.isPermanentlyDenied;
    } catch (e) {
      return false;
    }
  }

  // Open app settings
  static Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de l\'ouverture des paramètres: $e');
    }
  }

  // Request permission with rationale
  static Future<bool> requestPermissionWithRationale({
    required Permission permission,
    required String title,
    required String message,
    required Function() onShowRationale,
  }) async {
    try {
      // Check current status
      final status = await permission.status;
      
      if (status.isGranted) {
        return true;
      }
      
      if (status.isDenied) {
        // Show rationale if needed
        onShowRationale();
        
        // Request permission
        final newStatus = await permission.request();
        return newStatus.isGranted;
      }
      
      if (status.isPermanentlyDenied) {
        // Show dialog to go to settings
        onShowRationale();
        return false;
      }
      
      return false;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisation: $e');
    }
  }

  // Get permission status message
  static String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Autorisation accordée';
      case PermissionStatus.denied:
        return 'Autorisation refusée';
      case PermissionStatus.restricted:
        return 'Autorisation restreinte';
      case PermissionStatus.limited:
        return 'Autorisation limitée';
      case PermissionStatus.permanentlyDenied:
        return 'Autorisation définitivement refusée';
      case PermissionStatus.provisional:
        return 'Autorisation provisoire';
    }
  }

  // Get permission name
  static String getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.camera:
        return 'Caméra';
      case Permission.storage:
        return 'Stockage';
      case Permission.location:
        return 'Localisation';
      case Permission.notification:
        return 'Notifications';
      case Permission.microphone:
        return 'Microphone';
      case Permission.photos:
        return 'Photos';
      case Permission.contacts:
        return 'Contacts';
      case Permission.phone:
        return 'Téléphone';
      case Permission.sms:
        return 'SMS';
      default:
        return 'Autorisation inconnue';
    }
  }

  // Check all required permissions for the app
  static Future<Map<String, bool>> checkAllAppPermissions() async {
    final results = <String, bool>{};
    
    try {
      results['camera'] = await hasCameraPermission();
      results['storage'] = await hasStoragePermission();
      results['location'] = await hasLocationPermission();
      results['notification'] = await hasNotificationPermission();
      
      return results;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la vérification des autorisations: $e');
    }
  }

  // Request all required permissions for the app
  static Future<Map<String, bool>> requestAllAppPermissions() async {
    final results = <String, bool>{};
    
    try {
      results['camera'] = await requestCameraPermission();
      results['storage'] = await requestStoragePermission();
      results['location'] = await requestLocationPermission();
      results['notification'] = await requestNotificationPermission();
      
      return results;
    } catch (e) {
      throw PermissionException(message: 'Erreur lors de la demande d\'autorisations: $e');
    }
  }

  // Check if all critical permissions are granted
  static Future<bool> areAllCriticalPermissionsGranted() async {
    try {
      final camera = await hasCameraPermission();
      final storage = await hasStoragePermission();
      final location = await hasLocationPermission();
      
      return camera && storage && location;
    } catch (e) {
      return false;
    }
  }
}
