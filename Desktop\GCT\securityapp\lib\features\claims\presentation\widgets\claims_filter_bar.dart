import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../bloc/claims_bloc.dart';
import '../bloc/claims_event.dart';
import '../bloc/claims_state.dart';

class ClaimsFilterBar extends StatelessWidget {
  const ClaimsFilterBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClaimsBloc, ClaimsState>(
      builder: (context, state) {
        if (state is! ClaimsLoaded) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            border: Border(
              bottom: BorderSide(
                color: AppTheme.dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              // Stats row
              if (state.stats != null) _buildStatsRow(state.stats!),
              
              // Filter chips
              const SizedBox(height: 12),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildFilterChip(
                      context,
                      label: 'Tous',
                      isSelected: state.currentStatus == null,
                      onTap: () => _applyFilter(context, status: null),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      context,
                      label: 'En attente',
                      isSelected: state.currentStatus == 'PENDING',
                      onTap: () => _applyFilter(context, status: 'PENDING'),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      context,
                      label: 'En cours',
                      isSelected: state.currentStatus == 'IN_PROGRESS',
                      onTap: () => _applyFilter(context, status: 'IN_PROGRESS'),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      context,
                      label: 'Résolus',
                      isSelected: state.currentStatus == 'RESOLVED',
                      onTap: () => _applyFilter(context, status: 'RESOLVED'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsRow(Map<String, int> stats) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          label: 'Total',
          value: stats['total'] ?? 0,
          color: AppTheme.textPrimaryColor,
        ),
        _buildStatItem(
          label: 'En attente',
          value: stats['pending'] ?? 0,
          color: AppTheme.warningColor,
        ),
        _buildStatItem(
          label: 'En cours',
          value: stats['inProgress'] ?? 0,
          color: AppTheme.infoColor,
        ),
        _buildStatItem(
          label: 'Résolus',
          value: stats['resolved'] ?? 0,
          color: AppTheme.successColor,
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required String label,
    required int value,
    required Color color,
  }) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
          ),
        ),
      ),
    );
  }

  void _applyFilter(BuildContext context, {String? status}) {
    context.read<ClaimsBloc>().add(ClaimsFilterChanged(status: status));
  }
}
