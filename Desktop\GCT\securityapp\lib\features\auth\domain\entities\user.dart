import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String role;
  final String? factoryId;
  final String? factoryName;
  final String? profileImageUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? permissions;

  const User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    required this.role,
    this.factoryId,
    this.factoryName,
    this.profileImageUrl,
    required this.isActive,
    required this.createdAt,
    this.lastLoginAt,
    this.permissions,
  });

  String get fullName => '$firstName $lastName';

  bool get isSuperAdmin => role == 'SUPER_ADMIN';
  bool get isSecurityAdmin => role == 'SECURITY_ADMIN_GCT';
  bool get isFactoryAdmin => role == 'FACTORY_ADMIN';
  bool get isEmployee => role == 'EMPLOYEE';

  bool get canManageUsers => isSuperAdmin || isSecurityAdmin;
  bool get canManageFactories => isSuperAdmin;
  bool get canManageClaims => isSuperAdmin || isSecurityAdmin || isFactoryAdmin;
  bool get canManageTrainings => isSuperAdmin || isSecurityAdmin || isFactoryAdmin;
  bool get canViewAllFactories => isSuperAdmin || isSecurityAdmin;
  bool get canCreateClaims => true; // All users can create claims
  bool get canViewReports => isSuperAdmin || isSecurityAdmin || isFactoryAdmin;

  bool hasPermission(String permission) {
    if (permissions == null) return false;
    return permissions![permission] == true;
  }

  User copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? role,
    String? factoryId,
    String? factoryName,
    String? profileImageUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? permissions,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      permissions: permissions ?? this.permissions,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        phoneNumber,
        role,
        factoryId,
        factoryName,
        profileImageUrl,
        isActive,
        createdAt,
        lastLoginAt,
        permissions,
      ];
}
