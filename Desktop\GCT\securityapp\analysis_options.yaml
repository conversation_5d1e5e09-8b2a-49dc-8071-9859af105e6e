# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - lib/services/firebase_database_service.dart
    - lib/services/firebase_auth_service.dart
    - lib/utils/firebase_demo_data.dart
    - lib/pages/firebase_dashboards.dart
    - gct_mobile_test/**
    - test_firebase_data.dart
    - create_firebase_data.dart

linter:
  rules:
    avoid_print: false  # Disable print warnings for development
    prefer_single_quotes: true
    depend_on_referenced_packages: false
    deprecated_member_use: false

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
