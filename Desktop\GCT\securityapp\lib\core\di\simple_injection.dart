import 'package:shared_preferences/shared_preferences.dart';
import '../../features/auth/data/datasources/auth_firebase_datasource.dart';
import '../../features/auth/data/datasources/auth_local_datasource.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';
import '../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../../features/auth/domain/usecases/refresh_token_usecase.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';

/// Simple dependency injection without external packages
class SimpleInjection {
  static SimpleInjection? _instance;
  static SimpleInjection get instance => _instance ??= SimpleInjection._();
  
  SimpleInjection._();

  // Dependencies
  late SharedPreferences _sharedPreferences;
  late AuthLocalDataSourceImpl _authLocalDataSource;
  late AuthFirebaseDataSourceImpl _authFirebaseDataSource;
  late AuthRepositoryImpl _authRepository;
  late LoginUseCase _loginUseCase;
  late LogoutUseCase _logoutUseCase;
  late GetCurrentUserUseCase _getCurrentUserUseCase;
  late RefreshTokenUseCase _refreshTokenUseCase;
  late AuthBloc _authBloc;

  // Initialize all dependencies
  Future<void> init() async {
    // Initialize SharedPreferences
    _sharedPreferences = await SharedPreferences.getInstance();
    
    // Initialize data sources
    _authLocalDataSource = AuthLocalDataSourceImpl(_sharedPreferences);
    _authFirebaseDataSource = AuthFirebaseDataSourceImpl();
    
    // Initialize repository
    _authRepository = AuthRepositoryImpl(
      firebaseDataSource: _authFirebaseDataSource,
      localDataSource: _authLocalDataSource,
    );
    
    // Initialize use cases
    _loginUseCase = LoginUseCase(_authRepository);
    _logoutUseCase = LogoutUseCase(_authRepository);
    _getCurrentUserUseCase = GetCurrentUserUseCase(_authRepository);
    _refreshTokenUseCase = RefreshTokenUseCase(_authRepository);
    
    // Initialize bloc
    _authBloc = AuthBloc(
      loginUseCase: _loginUseCase,
      logoutUseCase: _logoutUseCase,
      getCurrentUserUseCase: _getCurrentUserUseCase,
      refreshTokenUseCase: _refreshTokenUseCase,
    );
  }

  // Getters
  SharedPreferences get sharedPreferences => _sharedPreferences;
  AuthBloc get authBloc => _authBloc;
  AuthRepositoryImpl get authRepository => _authRepository;
  
  // Dispose resources
  void dispose() {
    _authBloc.close();
  }
}
