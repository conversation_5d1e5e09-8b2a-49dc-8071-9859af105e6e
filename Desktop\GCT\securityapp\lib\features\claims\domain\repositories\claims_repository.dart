import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/claim.dart';

abstract class ClaimsRepository {
  Future<Either<Failure, List<Claim>>> getClaims({
    String? factoryId,
    String? status,
    String? type,
    String? assignedToId,
    int? page,
    int? limit,
  });

  Future<Either<Failure, Claim>> getClaimById(String id);

  Future<Either<Failure, Claim>> createClaim(Claim claim);

  Future<Either<Failure, Claim>> updateClaim(Claim claim);

  Future<Either<Failure, void>> deleteClaim(String id);

  Future<Either<Failure, String>> uploadAttachment(String claimId, File file);

  Future<Either<Failure, void>> deleteAttachment(String url);

  Future<Either<Failure, Claim>> addComment(String claimId, ClaimComment comment);

  Stream<Either<Failure, Claim>> watchClaim(String id);

  Stream<Either<Failure, List<Claim>>> watchClaims({
    String? factoryId,
    String? status,
    String? assignedToId,
  });

  Future<Either<Failure, Map<String, int>>> getClaimsStats({String? factoryId});
}
