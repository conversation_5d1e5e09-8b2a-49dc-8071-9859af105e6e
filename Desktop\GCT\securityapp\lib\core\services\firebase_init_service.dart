import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_service.dart';

class FirebaseInitService {
  static final FirebaseAuth _auth = FirebaseService.auth;
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  // Initialize demo data for development
  static Future<void> initializeDemoData() async {
    try {
      // Check if demo data already exists
      final factoriesSnapshot = await _firestore
          .collection(FirebaseService.factoriesCollection)
          .limit(1)
          .get();

      if (factoriesSnapshot.docs.isNotEmpty) {
        print('Demo data already exists, skipping initialization');
        return;
      }

      print('Initializing demo data...');

      // Create demo factories
      await _createDemoFactories();

      // Create demo users (this will be done through Firebase Auth Console in production)
      await _createDemoUsers();

      print('Demo data initialized successfully');
    } catch (e) {
      print('Error initializing demo data: $e');
    }
  }

  static Future<void> _createDemoFactories() async {
    final factories = [
      {
        'name': 'Usine Sfax',
        'code': 'GCT-SFX',
        'description': 'Usine principale de production chimique à Sfax',
        'address': 'Zone Industrielle Sfax',
        'city': 'Sfax',
        'region': 'Sfax',
        'country': 'Tunisie',
        'postalCode': '3000',
        'latitude': 34.7406,
        'longitude': 10.7603,
        'phoneNumber': '+216 74 123 456',
        'email': '<EMAIL>',
        'managerId': 'demo-manager-1',
        'managerName': 'Ahmed Ben Ali',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'name': 'Usine Tunis',
        'code': 'GCT-TUN',
        'description': 'Centre de recherche et développement à Tunis',
        'address': 'Technopole El Ghazala',
        'city': 'Ariana',
        'region': 'Tunis',
        'country': 'Tunisie',
        'postalCode': '2083',
        'latitude': 36.8983,
        'longitude': 10.1894,
        'phoneNumber': '+216 71 123 456',
        'email': '<EMAIL>',
        'managerId': 'demo-manager-2',
        'managerName': 'Fatma Trabelsi',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'name': 'Usine Gabès',
        'code': 'GCT-GAB',
        'description': 'Unité de traitement des phosphates à Gabès',
        'address': 'Zone Industrielle Gabès',
        'city': 'Gabès',
        'region': 'Gabès',
        'country': 'Tunisie',
        'postalCode': '6000',
        'latitude': 33.8815,
        'longitude': 10.0982,
        'phoneNumber': '+216 75 123 456',
        'email': '<EMAIL>',
        'managerId': 'demo-manager-3',
        'managerName': 'Mohamed Sassi',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    final batch = _firestore.batch();
    for (final factory in factories) {
      final docRef = _firestore.collection(FirebaseService.factoriesCollection).doc();
      batch.set(docRef, factory);
    }
    await batch.commit();
  }

  static Future<void> _createDemoUsers() async {
    // Note: In production, users should be created through Firebase Auth Console
    // or through admin SDK. This is just for demo purposes.
    
    final demoUsers = [
      {
        'email': '<EMAIL>',
        'firstName': 'Super',
        'lastName': 'Admin',
        'role': 'SUPER_ADMIN',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'email': '<EMAIL>',
        'firstName': 'Admin',
        'lastName': 'Sécurité',
        'role': 'SECURITY_ADMIN_GCT',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'email': '<EMAIL>',
        'firstName': 'Ahmed',
        'lastName': 'Ben Ali',
        'role': 'FACTORY_ADMIN',
        'factoryId': 'factory-sfax-id', // This should match actual factory ID
        'factoryName': 'Usine Sfax',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'email': '<EMAIL>',
        'firstName': 'Employé',
        'lastName': 'Test',
        'role': 'EMPLOYEE',
        'factoryId': 'factory-sfax-id',
        'factoryName': 'Usine Sfax',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    // Create user documents (without authentication - this should be done separately)
    final batch = _firestore.batch();
    for (final user in demoUsers) {
      final docRef = _firestore.collection(FirebaseService.usersCollection).doc();
      batch.set(docRef, user);
    }
    await batch.commit();
  }

  // Create demo training data
  static Future<void> createDemoTrainings() async {
    final trainings = [
      {
        'title': 'Formation Sécurité de Base',
        'description': 'Formation obligatoire sur les règles de sécurité de base dans l\'industrie chimique',
        'category': 'SAFETY_BASIC',
        'level': 'BASIC',
        'duration': 120, // minutes
        'isRequired': true,
        'expiryDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 365))),
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'title': 'Manipulation des Produits Chimiques',
        'description': 'Formation avancée sur la manipulation sécurisée des produits chimiques',
        'category': 'CHEMICAL_HANDLING',
        'level': 'INTERMEDIATE',
        'duration': 180,
        'isRequired': true,
        'expiryDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 730))),
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'title': 'Procédures d\'Urgence',
        'description': 'Formation sur les procédures d\'évacuation et de gestion des urgences',
        'category': 'EMERGENCY',
        'level': 'ADVANCED',
        'duration': 240,
        'isRequired': true,
        'expiryDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 365))),
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    final batch = _firestore.batch();
    for (final training in trainings) {
      final docRef = _firestore.collection(FirebaseService.trainingsCollection).doc();
      batch.set(docRef, training);
    }
    await batch.commit();
  }

  // Create demo claims
  static Future<void> createDemoClaims() async {
    final claims = [
      {
        'title': 'Fuite de produit chimique - Zone A',
        'description': 'Détection d\'une petite fuite de produit chimique dans la zone de stockage A. Intervention immédiate requise.',
        'type': 'INCIDENT',
        'status': 'PENDING',
        'priority': 'HIGH',
        'reporterId': 'demo-employee-1',
        'reporterName': 'Employé Test',
        'factoryId': 'demo-factory-1',
        'factoryName': 'Usine Sfax',
        'location': 'Zone de stockage A',
        'incidentDate': Timestamp.fromDate(DateTime.now().subtract(const Duration(hours: 2))),
        'reportedAt': Timestamp.fromDate(DateTime.now().subtract(const Duration(hours: 1))),
        'attachments': [],
        'comments': [],
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'title': 'Chute d\'un employé',
        'description': 'Un employé a glissé dans les escaliers. Blessure légère au genou.',
        'type': 'ACCIDENT',
        'status': 'IN_PROGRESS',
        'priority': 'MEDIUM',
        'reporterId': 'demo-employee-2',
        'reporterName': 'Superviseur Zone B',
        'assignedToId': 'demo-security-admin',
        'assignedToName': 'Admin Sécurité',
        'factoryId': 'demo-factory-1',
        'factoryName': 'Usine Sfax',
        'location': 'Escalier principal - Étage 2',
        'incidentDate': Timestamp.fromDate(DateTime.now().subtract(const Duration(days: 1))),
        'reportedAt': Timestamp.fromDate(DateTime.now().subtract(const Duration(days: 1))),
        'attachments': [],
        'comments': [],
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    final batch = _firestore.batch();
    for (final claim in claims) {
      final docRef = _firestore.collection(FirebaseService.claimsCollection).doc();
      batch.set(docRef, claim);
    }
    await batch.commit();
  }

  // Note: Firestore security rules should be configured in Firebase Console
  // This method is kept for reference but not used in the app
  static String getFirestoreSecurityRulesReference() {
    return '''
    // TODO: Configure these rules in Firebase Console
    // rules_version = '2';
    // service cloud.firestore {
    //   match /databases/{database}/documents {
    //     // Configure access rules based on user roles
    //   }
    // }
    ''';
  }
}
