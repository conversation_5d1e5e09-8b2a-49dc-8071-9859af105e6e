import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  final String? deviceId;
  final String? deviceName;

  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.deviceId,
    this.deviceName,
  });

  @override
  List<Object?> get props => [email, password, deviceId, deviceName];
}

class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

class AuthRefreshTokenRequested extends AuthEvent {
  const AuthRefreshTokenRequested();
}

class AuthUserChanged extends AuthEvent {
  const AuthUserChanged();
}
