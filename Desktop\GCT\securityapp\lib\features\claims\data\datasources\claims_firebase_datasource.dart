import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/claim_model.dart';

abstract class ClaimsFirebaseDataSource {
  Future<List<ClaimModel>> getClaims({
    String? factoryId,
    String? status,
    String? type,
    String? assignedToId,
    int? limit,
    DocumentSnapshot? lastDocument,
  });

  Future<ClaimModel> getClaimById(String id);

  Future<ClaimModel> createClaim(ClaimModel claim);

  Future<ClaimModel> updateClaim(ClaimModel claim);

  Future<void> deleteClaim(String id);

  Future<String> uploadAttachment(String claimId, File file);

  Future<void> deleteAttachment(String url);

  Future<ClaimModel> addComment(String claimId, ClaimCommentModel comment);

  Stream<ClaimModel> watchClaim(String id);

  Stream<List<ClaimModel>> watchClaims({
    String? factoryId,
    String? status,
    String? assignedToId,
  });
}

class ClaimsFirebaseDataSourceImpl implements ClaimsFirebaseDataSource {
  final FirebaseFirestore _firestore = FirebaseService.firestore;
  final FirebaseStorage _storage = FirebaseService.storage;

  @override
  Future<List<ClaimModel>> getClaims({
    String? factoryId,
    String? status,
    String? type,
    String? assignedToId,
    int? limit,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore.collection(FirebaseService.claimsCollection);

      // Apply filters
      if (factoryId != null) {
        query = query.where('factoryId', isEqualTo: factoryId);
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }
      if (type != null) {
        query = query.where('type', isEqualTo: type);
      }
      if (assignedToId != null) {
        query = query.where('assignedToId', isEqualTo: assignedToId);
      }

      // Order by creation date (most recent first)
      query = query.orderBy('reportedAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }
      if (limit != null) {
        query = query.limit(limit);
      } else {
        query = query.limit(AppConstants.defaultPageSize);
      }

      final querySnapshot = await query.get();
      
      return querySnapshot.docs
          .map((doc) => ClaimModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<ClaimModel> getClaimById(String id) async {
    try {
      final doc = await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(id)
          .get();

      if (!doc.exists) {
        throw const ServerException(message: 'Réclamation non trouvée');
      }

      return ClaimModel.fromFirestore(doc);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<ClaimModel> createClaim(ClaimModel claim) async {
    try {
      final docRef = _firestore.collection(FirebaseService.claimsCollection).doc();
      final claimData = claim.toFirestore();
      
      // Add server timestamp
      claimData['createdAt'] = FieldValue.serverTimestamp();
      claimData['updatedAt'] = FieldValue.serverTimestamp();

      await docRef.set(claimData);

      // Get the created document
      final doc = await docRef.get();
      return ClaimModel.fromFirestore(doc);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<ClaimModel> updateClaim(ClaimModel claim) async {
    try {
      final claimData = claim.toFirestore();
      claimData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(claim.id)
          .update(claimData);

      // Get the updated document
      final doc = await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(claim.id)
          .get();

      return ClaimModel.fromFirestore(doc);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<void> deleteClaim(String id) async {
    try {
      // Get claim to delete attachments
      final claim = await getClaimById(id);
      
      // Delete attachments from storage
      for (final attachment in claim.attachments) {
        try {
          await deleteAttachment(attachment);
        } catch (e) {
          // Continue even if attachment deletion fails
        }
      }

      // Delete the claim document
      await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(id)
          .delete();
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<String> uploadAttachment(String claimId, File file) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final path = 'claims/$claimId/attachments/$fileName';
      
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(file);
      
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      return downloadUrl;
    } catch (e) {
      throw FileException(message: 'Erreur lors de l\'upload: $e');
    }
  }

  @override
  Future<void> deleteAttachment(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
    } catch (e) {
      throw FileException(message: 'Erreur lors de la suppression: $e');
    }
  }

  @override
  Future<ClaimModel> addComment(String claimId, ClaimCommentModel comment) async {
    try {
      final commentData = comment.toJson();
      commentData['createdAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(claimId)
          .update({
        'comments': FieldValue.arrayUnion([commentData]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Get the updated claim
      final doc = await _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(claimId)
          .get();

      return ClaimModel.fromFirestore(doc);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Stream<ClaimModel> watchClaim(String id) {
    try {
      return _firestore
          .collection(FirebaseService.claimsCollection)
          .doc(id)
          .snapshots()
          .map((doc) {
        if (!doc.exists) {
          throw const ServerException(message: 'Réclamation non trouvée');
        }
        return ClaimModel.fromFirestore(doc);
      });
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Stream<List<ClaimModel>> watchClaims({
    String? factoryId,
    String? status,
    String? assignedToId,
  }) {
    try {
      Query query = _firestore.collection(FirebaseService.claimsCollection);

      // Apply filters
      if (factoryId != null) {
        query = query.where('factoryId', isEqualTo: factoryId);
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }
      if (assignedToId != null) {
        query = query.where('assignedToId', isEqualTo: assignedToId);
      }

      // Order by creation date
      query = query.orderBy('reportedAt', descending: true);
      query = query.limit(50); // Limit for real-time updates

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => ClaimModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  // Helper method to get claims statistics
  Future<Map<String, int>> getClaimsStats({String? factoryId}) async {
    try {
      Query query = _firestore.collection(FirebaseService.claimsCollection);
      
      if (factoryId != null) {
        query = query.where('factoryId', isEqualTo: factoryId);
      }

      final snapshot = await query.get();
      final claims = snapshot.docs.map((doc) => doc.data() as Map<String, dynamic>);

      final stats = <String, int>{
        'total': claims.length,
        'pending': 0,
        'inProgress': 0,
        'resolved': 0,
        'closed': 0,
      };

      for (final claim in claims) {
        final status = claim['status'] as String;
        switch (status) {
          case AppConstants.pendingStatus:
            stats['pending'] = (stats['pending'] ?? 0) + 1;
            break;
          case AppConstants.inProgressStatus:
            stats['inProgress'] = (stats['inProgress'] ?? 0) + 1;
            break;
          case AppConstants.resolvedStatus:
            stats['resolved'] = (stats['resolved'] ?? 0) + 1;
            break;
          case AppConstants.closedStatus:
            stats['closed'] = (stats['closed'] ?? 0) + 1;
            break;
        }
      }

      return stats;
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }
}
