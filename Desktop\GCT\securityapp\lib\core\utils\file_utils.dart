import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';

class FileUtils {
  static final ImagePicker _imagePicker = ImagePicker();

  // Pick image from camera
  static Future<File?> pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        final file = File(image.path);
        await _validateImageFile(file);
        return file;
      }
      return null;
    } catch (e) {
      throw FileException(message: 'Erreur lors de la prise de photo: $e');
    }
  }

  // Pick image from gallery
  static Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        final file = File(image.path);
        await _validateImageFile(file);
        return file;
      }
      return null;
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sélection d\'image: $e');
    }
  }

  // Pick multiple images
  static Future<List<File>> pickMultipleImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      final List<File> files = [];
      for (final image in images) {
        final file = File(image.path);
        await _validateImageFile(file);
        files.add(file);
      }
      
      return files;
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sélection d\'images: $e');
    }
  }

  // Pick document file
  static Future<File?> pickDocument() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: AppConstants.allowedDocumentTypes,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        await _validateDocumentFile(file);
        return file;
      }
      return null;
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sélection de document: $e');
    }
  }

  // Pick any file
  static Future<File?> pickAnyFile() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        await _validateFileSize(file);
        return file;
      }
      return null;
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sélection de fichier: $e');
    }
  }

  // Validate image file
  static Future<void> _validateImageFile(File file) async {
    await _validateFileSize(file);
    
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    
    if (!AppConstants.allowedImageTypes.contains(extension)) {
      throw FileException(
        message: 'Type d\'image non autorisé. Types autorisés: ${AppConstants.allowedImageTypes.join(', ')}',
      );
    }
  }

  // Validate document file
  static Future<void> _validateDocumentFile(File file) async {
    await _validateFileSize(file);
    
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    
    if (!AppConstants.allowedDocumentTypes.contains(extension)) {
      throw FileException(
        message: 'Type de document non autorisé. Types autorisés: ${AppConstants.allowedDocumentTypes.join(', ')}',
      );
    }
  }

  // Validate file size
  static Future<void> _validateFileSize(File file) async {
    final fileSize = await file.length();
    if (fileSize > AppConstants.maxFileSize) {
      final maxSizeInMB = AppConstants.maxFileSize / (1024 * 1024);
      throw FileException(
        message: 'Fichier trop volumineux. Taille maximale: ${maxSizeInMB.toStringAsFixed(1)} MB',
      );
    }
  }

  // Get file size in human readable format
  static String getFileSizeString(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  // Get file name without extension
  static String getFileNameWithoutExtension(String fileName) {
    final parts = fileName.split('.');
    if (parts.length > 1) {
      parts.removeLast();
    }
    return parts.join('.');
  }

  // Check if file is image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return AppConstants.allowedImageTypes.contains(extension);
  }

  // Check if file is document
  static bool isDocumentFile(String fileName) {
    final extension = getFileExtension(fileName);
    return AppConstants.allowedDocumentTypes.contains(extension);
  }

  // Save file to app directory
  static Future<File> saveFileToAppDirectory(File file, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final appDir = Directory('${directory.path}/gct_security');
      
      if (!await appDir.exists()) {
        await appDir.create(recursive: true);
      }
      
      final newFile = File('${appDir.path}/$fileName');
      return await file.copy(newFile.path);
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sauvegarde: $e');
    }
  }

  // Save bytes to file
  static Future<File> saveBytesToFile(Uint8List bytes, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final appDir = Directory('${directory.path}/gct_security');
      
      if (!await appDir.exists()) {
        await appDir.create(recursive: true);
      }
      
      final file = File('${appDir.path}/$fileName');
      return await file.writeAsBytes(bytes);
    } catch (e) {
      throw FileException(message: 'Erreur lors de la sauvegarde: $e');
    }
  }

  // Delete file
  static Future<void> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw FileException(message: 'Erreur lors de la suppression: $e');
    }
  }

  // Get app files directory
  static Future<Directory> getAppFilesDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final appDir = Directory('${directory.path}/gct_security');
    
    if (!await appDir.exists()) {
      await appDir.create(recursive: true);
    }
    
    return appDir;
  }

  // Clear app cache
  static Future<void> clearAppCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      throw FileException(message: 'Erreur lors du nettoyage du cache: $e');
    }
  }

  // Get cache size
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (!await cacheDir.exists()) return 0;
      
      int totalSize = 0;
      await for (final entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  // Generate unique file name
  static String generateUniqueFileName(String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = getFileExtension(originalName);
    final nameWithoutExt = getFileNameWithoutExtension(originalName);
    return '${nameWithoutExt}_$timestamp.$extension';
  }
}
