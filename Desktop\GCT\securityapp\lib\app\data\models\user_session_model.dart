import 'package:cloud_firestore/cloud_firestore.dart';

enum DeviceType {
  desktop,
  mobile,
  tablet,
  unknown,
}

class UserSessionModel {
  final String id;
  final String userId;
  final String userEmail;
  final DateTime loginTime;
  final DateTime? logoutTime;
  final String ipAddress;
  final String userAgent;
  final DeviceType deviceType;
  final bool isActive;
  final DateTime createdAt;

  UserSessionModel({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.loginTime,
    this.logoutTime,
    required this.ipAddress,
    required this.userAgent,
    this.deviceType = DeviceType.unknown,
    this.isActive = true,
    required this.createdAt,
  });

  Duration? get sessionDuration {
    if (logoutTime != null) {
      return logoutTime!.difference(loginTime);
    } else if (isActive) {
      return DateTime.now().difference(loginTime);
    }
    return null;
  }

  String get sessionDurationFormatted {
    final duration = sessionDuration;
    if (duration == null) return 'N/A';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}min';
    } else {
      return '${minutes}min';
    }
  }

  String get deviceTypeDisplayName {
    switch (deviceType) {
      case DeviceType.desktop:
        return 'Ordinateur';
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablette';
      case DeviceType.unknown:
        return 'Inconnu';
    }
  }

  String get statusDisplayName {
    if (isActive && logoutTime == null) {
      return 'Active';
    } else {
      return 'Terminée';
    }
  }

  bool get isCurrentlyActive => isActive && logoutTime == null;

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userEmail': userEmail,
      'loginTime': loginTime,
      'logoutTime': logoutTime,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'deviceType': deviceType.name,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  // Création depuis Map Firestore
  factory UserSessionModel.fromMap(Map<String, dynamic> map) {
    return UserSessionModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      loginTime: (map['loginTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      logoutTime: (map['logoutTime'] as Timestamp?)?.toDate(),
      ipAddress: map['ipAddress'] ?? '',
      userAgent: map['userAgent'] ?? '',
      deviceType: DeviceType.values.firstWhere(
        (e) => e.name == map['deviceType'],
        orElse: () => DeviceType.unknown,
      ),
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Création depuis DocumentSnapshot
  factory UserSessionModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserSessionModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  UserSessionModel copyWith({
    String? id,
    String? userId,
    String? userEmail,
    DateTime? loginTime,
    DateTime? logoutTime,
    String? ipAddress,
    String? userAgent,
    DeviceType? deviceType,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return UserSessionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      loginTime: loginTime ?? this.loginTime,
      logoutTime: logoutTime ?? this.logoutTime,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      deviceType: deviceType ?? this.deviceType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserSessionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserSessionModel(id: $id, userId: $userId, isActive: $isActive)';
  }
}
