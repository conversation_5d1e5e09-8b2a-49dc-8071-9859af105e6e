import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/claim_model.dart';
import '../models/training_model.dart';
import '../models/factory_model.dart';

class DemoDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<void> initializeDemoData() async {
    try {
      // Forcer la recréation des données
      print('Suppression et recréation des données de démonstration...');
      await clearDemoData();

      print('Initialisation des données de démonstration...');

      // Créer les usines en premier (dépendances)
      await _createDemoFactories();

      // Créer des utilisateurs de démonstration
      await _createDemoUsers();

      // Créer des réclamations de démonstration
      await _createDemoClaims();

      // Créer des formations de démonstration
      await _createDemoTrainings();

      // Créer des données supplémentaires
      await _createDemoDepartments();
      await _createDemoUserSessions();
      await _createDemoNotifications();
      await _createDemoReports();
      await _createDemoSettings();

      print('Données de démonstration créées avec succès');
    } catch (e) {
      print('Erreur lors de l\'initialisation des données de démonstration: $e');
    }
  }

  static Future<void> _createFirebaseAuthUsers() async {
    // Créer une collection pour stocker les informations d'authentification
    // En production, ces données seraient dans Firebase Auth, mais pour la démo
    // nous les stockons dans Firestore pour référence
    final authUsers = [
      // Admins
      {
        'uid': 'user1',
        'email': '<EMAIL>',
        'password': 'Admin123!', // En production, ce serait hashé
        'role': 'super_admin',
        'displayName': 'Super Admin',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 365)),
      },
      {
        'uid': 'user2',
        'email': '<EMAIL>',
        'password': 'Security123!',
        'role': 'admin_securite_gct',
        'displayName': 'Ahmed Benali',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 300)),
      },
      {
        'uid': 'user3',
        'email': '<EMAIL>',
        'password': 'Gabes123!',
        'role': 'admin_usine',
        'displayName': 'Fatma Trabelsi',
        'factory': 'Usine de Gabès',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 250)),
      },
      {
        'uid': 'user4',
        'email': '<EMAIL>',
        'password': 'Sfax123!',
        'role': 'admin_usine',
        'displayName': 'Mohamed Karray',
        'factory': 'Usine de Sfax',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 240)),
      },
      {
        'uid': 'user5',
        'email': '<EMAIL>',
        'password': 'Tunis123!',
        'role': 'admin_usine',
        'displayName': 'Leila Bouaziz',
        'factory': 'Usine de Tunis',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 230)),
      },
      {
        'uid': 'user6',
        'email': '<EMAIL>',
        'password': 'Sousse123!',
        'role': 'admin_usine',
        'displayName': 'Nabil Hamdi',
        'factory': 'Usine de Sousse',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 220)),
      },

      // Employés Usine de Gabès
      {
        'uid': 'emp_gabes_1',
        'email': '<EMAIL>',
        'password': 'Sarra2024!',
        'role': 'employe',
        'displayName': 'Sarra Mansouri',
        'factory': 'Usine de Gabès',
        'department': 'Production',
        'position': 'Opératrice',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 180)),
      },
      {
        'uid': 'emp_gabes_2',
        'email': '<EMAIL>',
        'password': 'Amina2024!',
        'role': 'employe',
        'displayName': 'Amina Ben Salem',
        'factory': 'Usine de Gabès',
        'department': 'Qualité',
        'position': 'Contrôleuse Qualité',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 160)),
      },
      {
        'uid': 'emp_gabes_3',
        'email': '<EMAIL>',
        'password': 'Omar2024!',
        'role': 'employe',
        'displayName': 'Omar Triki',
        'factory': 'Usine de Gabès',
        'department': 'Maintenance',
        'position': 'Technicien',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 140)),
      },
      {
        'uid': 'emp_gabes_4',
        'email': '<EMAIL>',
        'password': 'Salma2024!',
        'role': 'employe',
        'displayName': 'Salma Ben Ahmed',
        'factory': 'Usine de Gabès',
        'department': 'Sécurité',
        'position': 'Agent de Sécurité',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 120)),
      },
      {
        'uid': 'emp_gabes_5',
        'email': '<EMAIL>',
        'password': 'Mehdi2024!',
        'role': 'employe',
        'displayName': 'Mehdi Gharbi',
        'factory': 'Usine de Gabès',
        'department': 'Production',
        'position': 'Opérateur',
        'isActive': false, // Compte désactivé
        'createdAt': DateTime.now().subtract(const Duration(days: 100)),
      },

      // Employés Usine de Sfax
      {
        'uid': 'emp_sfax_1',
        'email': '<EMAIL>',
        'password': 'Karim2024!',
        'role': 'employe',
        'displayName': 'Karim Jebali',
        'factory': 'Usine de Sfax',
        'department': 'Production',
        'position': 'Chef d\'équipe',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 170)),
      },
      {
        'uid': 'emp_sfax_2',
        'email': '<EMAIL>',
        'password': 'Youssef2024!',
        'role': 'employe',
        'displayName': 'Youssef Gharbi',
        'factory': 'Usine de Sfax',
        'department': 'Logistique',
        'position': 'Magasinier',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 150)),
      },
      {
        'uid': 'emp_sfax_3',
        'email': '<EMAIL>',
        'password': 'Ines2024!',
        'role': 'employe',
        'displayName': 'Ines Ben Ali',
        'factory': 'Usine de Sfax',
        'department': 'Administration',
        'position': 'Assistante RH',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 130)),
      },
      {
        'uid': 'emp_sfax_4',
        'email': '<EMAIL>',
        'password': 'Rami2024!',
        'role': 'employe',
        'displayName': 'Rami Bouaziz',
        'factory': 'Usine de Sfax',
        'department': 'Maintenance',
        'position': 'Électricien',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 110)),
      },

      // Employés Usine de Tunis
      {
        'uid': 'emp_tunis_1',
        'email': '<EMAIL>',
        'password': 'Mariem2024!',
        'role': 'employe',
        'displayName': 'Mariem Ben Salem',
        'factory': 'Usine de Tunis',
        'department': 'Production',
        'position': 'Opératrice Senior',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 165)),
      },
      {
        'uid': 'emp_tunis_2',
        'email': '<EMAIL>',
        'password': 'Ali2024!',
        'role': 'employe',
        'displayName': 'Ali Ben Mohamed',
        'factory': 'Usine de Tunis',
        'department': 'Sécurité',
        'position': 'Responsable Sécurité',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 145)),
      },
      {
        'uid': 'emp_tunis_3',
        'email': '<EMAIL>',
        'password': 'Fatma2024!',
        'role': 'employe',
        'displayName': 'Fatma Karray',
        'factory': 'Usine de Tunis',
        'department': 'Qualité',
        'position': 'Inspectrice Qualité',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 125)),
      },

      // Employés Usine de Sousse
      {
        'uid': 'emp_sousse_1',
        'email': '<EMAIL>',
        'password': 'Ahmed2024!',
        'role': 'employe',
        'displayName': 'Ahmed Ben Youssef',
        'factory': 'Usine de Sousse',
        'department': 'Production',
        'position': 'Superviseur',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 155)),
      },
      {
        'uid': 'emp_sousse_2',
        'email': '<EMAIL>',
        'password': 'Nadia2024!',
        'role': 'employe',
        'displayName': 'Nadia Trabelsi',
        'factory': 'Usine de Sousse',
        'department': 'Administration',
        'position': 'Gestionnaire Administrative',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 135)),
      },
      {
        'uid': 'emp_sousse_3',
        'email': '<EMAIL>',
        'password': 'Mohamed2024!',
        'role': 'employe',
        'displayName': 'Mohamed Ben Ali',
        'factory': 'Usine de Sousse',
        'department': 'Maintenance',
        'position': 'Mécanicien',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 115)),
      },

      // Employés supplémentaires pour avoir plus de données
      {
        'uid': 'emp_gabes_6',
        'email': '<EMAIL>',
        'password': 'Hana2024!',
        'role': 'employe',
        'displayName': 'Hana Ben Salah',
        'factory': 'Usine de Gabès',
        'department': 'Laboratoire',
        'position': 'Technicienne Laboratoire',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 90)),
      },
      {
        'uid': 'emp_sfax_5',
        'email': '<EMAIL>',
        'password': 'Walid2024!',
        'role': 'employe',
        'displayName': 'Walid Ben Amor',
        'factory': 'Usine de Sfax',
        'department': 'Production',
        'position': 'Opérateur Machine',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 85)),
      },
      {
        'uid': 'emp_tunis_4',
        'email': '<EMAIL>',
        'password': 'Samia2024!',
        'role': 'employe',
        'displayName': 'Samia Ben Hassan',
        'factory': 'Usine de Tunis',
        'department': 'Environnement',
        'position': 'Responsable Environnement',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 80)),
      },
      {
        'uid': 'emp_sousse_4',
        'email': '<EMAIL>',
        'password': 'Tarek2024!',
        'role': 'employe',
        'displayName': 'Tarek Ben Salem',
        'factory': 'Usine de Sousse',
        'department': 'Logistique',
        'position': 'Responsable Expédition',
        'isActive': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 75)),
      },
    ];

    // Stocker les informations d'authentification
    for (final authUser in authUsers) {
      await _firestore.collection('auth_users').doc(authUser['uid'] as String).set({
        ...authUser,
        'createdAt': Timestamp.fromDate(authUser['createdAt'] as DateTime),
        'lastLogin': null,
        'loginAttempts': 0,
        'isLocked': false,
      });
    }
  }

  static Future<void> _createDemoFactories() async {
    final factories = [
      FactoryModel(
        id: 'factory_gabes',
        name: 'Usine de Gabès',
        code: 'GCT-GAB',
        address: 'Zone Industrielle de Gabès',
        city: 'Gabès',
        region: 'Gabès',
        country: 'Tunisie',
        phone: '+216 75 270 000',
        email: '<EMAIL>',
        manager: 'Fatma Trabelsi',
        employeeCount: 450,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        coordinates: {
          'latitude': 33.8815,
          'longitude': 10.0982,
        },
      ),
      FactoryModel(
        id: 'factory_sfax',
        name: 'Usine de Sfax',
        code: 'GCT-SFX',
        address: 'Zone Industrielle de Sfax',
        city: 'Sfax',
        region: 'Sfax',
        country: 'Tunisie',
        phone: '+216 74 400 000',
        email: '<EMAIL>',
        manager: 'Mohamed Karray',
        employeeCount: 320,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 300)),
        coordinates: {
          'latitude': 34.7406,
          'longitude': 10.7603,
        },
      ),
      FactoryModel(
        id: 'factory_tunis',
        name: 'Usine de Tunis',
        code: 'GCT-TUN',
        address: 'Zone Industrielle Ben Arous',
        city: 'Ben Arous',
        region: 'Tunis',
        country: 'Tunisie',
        phone: '+216 71 380 000',
        email: '<EMAIL>',
        manager: 'Leila Bouaziz',
        employeeCount: 280,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        coordinates: {
          'latitude': 36.7538,
          'longitude': 10.2275,
        },
      ),
      FactoryModel(
        id: 'factory_sousse',
        name: 'Usine de Sousse',
        code: 'GCT-SOU',
        address: 'Zone Industrielle de Sousse',
        city: 'Sousse',
        region: 'Sousse',
        country: 'Tunisie',
        phone: '+216 73 220 000',
        email: '<EMAIL>',
        manager: 'Nabil Hamdi',
        employeeCount: 180,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 150)),
        coordinates: {
          'latitude': 35.8256,
          'longitude': 10.6369,
        },
      ),
    ];

    for (final factory in factories) {
      await _firestore.collection('factories').doc(factory.id).set(factory.toMap());
    }
  }

  static Future<void> _createDemoUsers() async {
    // Créer les comptes d'authentification Firebase d'abord
    await _createFirebaseAuthUsers();

    final users = [
      // Super Admin
      UserModel(
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin',
        factory: null,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        isActive: true,
      ),

      // Admin Sécurité GCT
      UserModel(
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'Ahmed',
        lastName: 'Benali',
        role: 'admin_securite_gct',
        factory: null,
        createdAt: DateTime.now().subtract(const Duration(days: 300)),
        isActive: true,
      ),

      // Admins d'usines
      UserModel(
        id: 'user3',
        email: '<EMAIL>',
        firstName: 'Fatma',
        lastName: 'Trabelsi',
        role: 'admin_usine',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 250)),
        isActive: true,
      ),
      UserModel(
        id: 'user4',
        email: '<EMAIL>',
        firstName: 'Mohamed',
        lastName: 'Karray',
        role: 'admin_usine',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 240)),
        isActive: true,
      ),
      UserModel(
        id: 'user5',
        email: '<EMAIL>',
        firstName: 'Leila',
        lastName: 'Bouaziz',
        role: 'admin_usine',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 230)),
        isActive: true,
      ),
      UserModel(
        id: 'user6',
        email: '<EMAIL>',
        firstName: 'Nabil',
        lastName: 'Hamdi',
        role: 'admin_usine',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 220)),
        isActive: true,
      ),

      // Employés Usine de Gabès
      UserModel(
        id: 'emp_gabes_1',
        email: '<EMAIL>',
        firstName: 'Sarra',
        lastName: 'Mansouri',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_gabes_2',
        email: '<EMAIL>',
        firstName: 'Amina',
        lastName: 'Ben Salem',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 160)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_gabes_3',
        email: '<EMAIL>',
        firstName: 'Omar',
        lastName: 'Triki',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 140)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_gabes_4',
        email: '<EMAIL>',
        firstName: 'Salma',
        lastName: 'Ben Ahmed',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_gabes_5',
        email: '<EMAIL>',
        firstName: 'Mehdi',
        lastName: 'Gharbi',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 100)),
        isActive: false, // Employé inactif
      ),

      // Employés Usine de Sfax
      UserModel(
        id: 'emp_sfax_1',
        email: '<EMAIL>',
        firstName: 'Karim',
        lastName: 'Jebali',
        role: 'employe',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 170)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sfax_2',
        email: '<EMAIL>',
        firstName: 'Youssef',
        lastName: 'Gharbi',
        role: 'employe',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 150)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sfax_3',
        email: '<EMAIL>',
        firstName: 'Ines',
        lastName: 'Ben Ali',
        role: 'employe',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 130)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sfax_4',
        email: '<EMAIL>',
        firstName: 'Rami',
        lastName: 'Bouaziz',
        role: 'employe',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 110)),
        isActive: true,
      ),

      // Employés Usine de Tunis
      UserModel(
        id: 'emp_tunis_1',
        email: '<EMAIL>',
        firstName: 'Mariem',
        lastName: 'Ben Salem',
        role: 'employe',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 165)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_tunis_2',
        email: '<EMAIL>',
        firstName: 'Ali',
        lastName: 'Ben Mohamed',
        role: 'employe',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 145)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_tunis_3',
        email: '<EMAIL>',
        firstName: 'Fatma',
        lastName: 'Karray',
        role: 'employe',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 125)),
        isActive: true,
      ),

      // Employés Usine de Sousse
      UserModel(
        id: 'emp_sousse_1',
        email: '<EMAIL>',
        firstName: 'Ahmed',
        lastName: 'Ben Youssef',
        role: 'employe',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 155)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sousse_2',
        email: '<EMAIL>',
        firstName: 'Nadia',
        lastName: 'Trabelsi',
        role: 'employe',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 135)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sousse_3',
        email: '<EMAIL>',
        firstName: 'Mohamed',
        lastName: 'Ben Ali',
        role: 'employe',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 115)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sousse_4',
        email: '<EMAIL>',
        firstName: 'Tarek',
        lastName: 'Ben Salem',
        role: 'employe',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 75)),
        isActive: true,
      ),

      // Employés supplémentaires
      UserModel(
        id: 'emp_gabes_6',
        email: '<EMAIL>',
        firstName: 'Hana',
        lastName: 'Ben Salah',
        role: 'employe',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_sfax_5',
        email: '<EMAIL>',
        firstName: 'Walid',
        lastName: 'Ben Amor',
        role: 'employe',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 85)),
        isActive: true,
      ),
      UserModel(
        id: 'emp_tunis_4',
        email: '<EMAIL>',
        firstName: 'Samia',
        lastName: 'Ben Hassan',
        role: 'employe',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 80)),
        isActive: true,
      ),
    ];

    for (final user in users) {
      await _firestore.collection('users').doc(user.id).set(user.toMap());
    }
  }

  static Future<void> _createDemoClaims() async {
    final claims = [
      // Réclamations critiques
      ClaimModel(
        id: 'claim1',
        title: 'Fuite de produit chimique - Zone A',
        description: 'Fuite détectée dans la zone de stockage des produits chimiques. Intervention urgente requise pour éviter contamination.',
        type: ClaimType.accident,
        status: ClaimStatus.pending,
        priority: ClaimPriority.critical,
        userId: 'emp_gabes_1',
        userEmail: '<EMAIL>',
        userName: 'Sarra Mansouri',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim2',
        title: 'Explosion mineure - Atelier 3',
        description: 'Explosion mineure dans l\'atelier 3 due à une surchauffe. Aucun blessé mais dégâts matériels.',
        type: ClaimType.accident,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.critical,
        userId: 'emp_sfax_1',
        userEmail: '<EMAIL>',
        userName: 'Karim Jebali',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        assignedTo: 'user4',
        attachments: [],
      ),

      // Réclamations haute priorité
      ClaimModel(
        id: 'claim3',
        title: 'Équipement de protection défaillant',
        description: 'Les casques de sécurité dans l\'atelier 3 présentent des fissures importantes.',
        type: ClaimType.hazard,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.high,
        userId: 'emp_sfax_2',
        userEmail: '<EMAIL>',
        userName: 'Youssef Gharbi',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        assignedTo: 'user4',
        attachments: [],
      ),
      ClaimModel(
        id: 'claim4',
        title: 'Problème de ventilation - Atelier B',
        description: 'Système de ventilation défaillant dans l\'atelier de production. Accumulation de vapeurs toxiques.',
        type: ClaimType.hazard,
        status: ClaimStatus.pending,
        priority: ClaimPriority.high,
        userId: 'emp_sousse_1',
        userEmail: '<EMAIL>',
        userName: 'Ahmed Ben Youssef',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim5',
        title: 'Machine défectueuse - Ligne 2',
        description: 'Machine de production ligne 2 présente des dysfonctionnements dangereux.',
        type: ClaimType.hazard,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.high,
        userId: 'emp_tunis_1',
        userEmail: '<EMAIL>',
        userName: 'Mariem Ben Salem',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        assignedTo: 'user5',
        attachments: [],
      ),

      // Réclamations priorité moyenne
      ClaimModel(
        id: 'claim6',
        title: 'Formation sécurité manquante',
        description: 'Nouveaux employés sans formation sécurité obligatoire depuis 2 semaines.',
        type: ClaimType.nonCompliance,
        status: ClaimStatus.pending,
        priority: ClaimPriority.medium,
        userId: 'emp_gabes_2',
        userEmail: '<EMAIL>',
        userName: 'Amina Ben Salem',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(hours: 18)),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim7',
        title: 'Éclairage insuffisant - Zone stockage',
        description: 'Zone de stockage mal éclairée, risque d\'accident lors des manipulations.',
        type: ClaimType.hazard,
        status: ClaimStatus.resolved,
        priority: ClaimPriority.medium,
        userId: 'emp_sfax_3',
        userEmail: '<EMAIL>',
        userName: 'Ines Ben Ali',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        resolvedAt: DateTime.now().subtract(const Duration(days: 2)),
        resolution: 'Installation de nouveaux éclairages LED haute performance.',
        attachments: [],
      ),
      ClaimModel(
        id: 'claim8',
        title: 'Procédure d\'urgence obsolète',
        description: 'Les procédures d\'évacuation d\'urgence n\'ont pas été mises à jour depuis 2 ans.',
        type: ClaimType.nonCompliance,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.medium,
        userId: 'emp_tunis_2',
        userEmail: '<EMAIL>',
        userName: 'Ali Ben Mohamed',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        assignedTo: 'user2',
        attachments: [],
      ),

      // Réclamations faible priorité et suggestions
      ClaimModel(
        id: 'claim9',
        title: 'Suggestion amélioration sécurité',
        description: 'Installation de capteurs de mouvement pour les zones dangereuses afin d\'améliorer la détection.',
        type: ClaimType.suggestion,
        status: ClaimStatus.pending,
        priority: ClaimPriority.low,
        userId: 'emp_gabes_3',
        userEmail: '<EMAIL>',
        userName: 'Omar Triki',
        factory: 'Usine de Gabès',
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim10',
        title: 'Incident mineur - chute d\'objet',
        description: 'Chute d\'un outil depuis une plateforme élevée. Aucun blessé mais procédure à revoir.',
        type: ClaimType.nearMiss,
        status: ClaimStatus.resolved,
        priority: ClaimPriority.low,
        userId: 'emp_sousse_2',
        userEmail: '<EMAIL>',
        userName: 'Nadia Trabelsi',
        factory: 'Usine de Sousse',
        createdAt: DateTime.now().subtract(const Duration(days: 6)),
        resolvedAt: DateTime.now().subtract(const Duration(days: 1)),
        resolution: 'Mise en place de filets de sécurité et formation du personnel.',
        attachments: [],
      ),
      ClaimModel(
        id: 'claim11',
        title: 'Amélioration ergonomie poste de travail',
        description: 'Suggestion d\'amélioration de l\'ergonomie des postes de travail pour réduire la fatigue.',
        type: ClaimType.suggestion,
        status: ClaimStatus.pending,
        priority: ClaimPriority.low,
        userId: 'emp_sfax_4',
        userEmail: '<EMAIL>',
        userName: 'Rami Bouaziz',
        factory: 'Usine de Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 4)),
        attachments: [],
      ),
      ClaimModel(
        id: 'claim12',
        title: 'Signalisation manquante',
        description: 'Absence de signalisation de sécurité dans certaines zones de l\'usine.',
        type: ClaimType.nonCompliance,
        status: ClaimStatus.resolved,
        priority: ClaimPriority.medium,
        userId: 'emp_tunis_3',
        userEmail: '<EMAIL>',
        userName: 'Fatma Karray',
        factory: 'Usine de Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 12)),
        resolvedAt: DateTime.now().subtract(const Duration(days: 3)),
        resolution: 'Installation de panneaux de signalisation conformes aux normes.',
        attachments: [],
      ),
    ];

    for (final claim in claims) {
      await _firestore.collection('claims').doc(claim.id).set(claim.toMap());
    }
  }

  static Future<void> _createDemoTrainings() async {
    final trainings = [
      TrainingModel(
        id: 'training1',
        title: 'Formation Sécurité Chimique',
        description: 'Formation obligatoire sur la manipulation sécurisée des produits chimiques.',
        type: TrainingType.safety,
        status: TrainingStatus.pending,
        userId: 'user5',
        userEmail: '<EMAIL>',
        userName: 'Sarra Mansouri',
        factory: 'Usine de Gabès',
        requestedAt: DateTime.now().subtract(const Duration(days: 2)),
        durationHours: 8,
        isMandatory: true,
        prerequisites: [],
      ),
      TrainingModel(
        id: 'training2',
        title: 'Formation Équipements de Protection',
        description: 'Formation sur l\'utilisation correcte des équipements de protection individuelle.',
        type: TrainingType.equipment,
        status: TrainingStatus.approved,
        userId: 'user6',
        userEmail: '<EMAIL>',
        userName: 'Karim Jebali',
        factory: 'Usine de Sfax',
        requestedAt: DateTime.now().subtract(const Duration(days: 7)),
        approvedAt: DateTime.now().subtract(const Duration(days: 5)),
        approvedBy: 'user4',
        durationHours: 4,
        isMandatory: true,
        prerequisites: [],
      ),
      TrainingModel(
        id: 'training3',
        title: 'Formation Procédures d\'Urgence',
        description: 'Formation sur les procédures d\'évacuation et de gestion des urgences.',
        type: TrainingType.emergency,
        status: TrainingStatus.completed,
        userId: 'user5',
        userEmail: '<EMAIL>',
        userName: 'Sarra Mansouri',
        factory: 'Usine de Gabès',
        requestedAt: DateTime.now().subtract(const Duration(days: 15)),
        approvedAt: DateTime.now().subtract(const Duration(days: 12)),
        completedAt: DateTime.now().subtract(const Duration(days: 8)),
        approvedBy: 'user3',
        durationHours: 6,
        isMandatory: true,
        prerequisites: [],
      ),
      TrainingModel(
        id: 'training4',
        title: 'Formation Sécurité Informatique',
        description: 'Formation sur la cybersécurité et la protection des données.',
        type: TrainingType.security,
        status: TrainingStatus.pending,
        userId: 'user6',
        userEmail: '<EMAIL>',
        userName: 'Karim Jebali',
        factory: 'Usine de Sfax',
        requestedAt: DateTime.now().subtract(const Duration(hours: 12)),
        durationHours: 3,
        isMandatory: false,
        prerequisites: [],
      ),
      TrainingModel(
        id: 'training5',
        title: 'Formation Premiers Secours',
        description: 'Formation aux gestes de premiers secours en milieu industriel.',
        type: TrainingType.emergency,
        status: TrainingStatus.approved,
        userId: 'user8',
        userEmail: '<EMAIL>',
        userName: 'Nabil Hamdi',
        factory: 'Usine de Sousse',
        requestedAt: DateTime.now().subtract(const Duration(days: 10)),
        approvedAt: DateTime.now().subtract(const Duration(days: 7)),
        approvedBy: 'user2',
        durationHours: 16,
        isMandatory: true,
        prerequisites: [],
      ),
      TrainingModel(
        id: 'training6',
        title: 'Formation Manipulation Chimique',
        description: 'Formation spécialisée pour la manipulation des produits chimiques dangereux.',
        type: TrainingType.safety,
        status: TrainingStatus.completed,
        userId: 'user9',
        userEmail: '<EMAIL>',
        userName: 'Amina Ben Salem',
        factory: 'Usine de Gabès',
        requestedAt: DateTime.now().subtract(const Duration(days: 30)),
        approvedAt: DateTime.now().subtract(const Duration(days: 25)),
        completedAt: DateTime.now().subtract(const Duration(days: 20)),
        approvedBy: 'user3',
        durationHours: 12,
        isMandatory: true,
        prerequisites: ['Formation Sécurité de Base'],
      ),
      TrainingModel(
        id: 'training7',
        title: 'Formation Conduite d\'Équipements',
        description: 'Formation pour la conduite sécurisée des équipements lourds.',
        type: TrainingType.equipment,
        status: TrainingStatus.pending,
        userId: 'user10',
        userEmail: '<EMAIL>',
        userName: 'Youssef Gharbi',
        factory: 'Usine de Sfax',
        requestedAt: DateTime.now().subtract(const Duration(hours: 8)),
        durationHours: 20,
        isMandatory: false,
        prerequisites: ['Formation Sécurité de Base', 'Formation EPI'],
      ),
    ];

    for (final training in trainings) {
      await _firestore.collection('trainings').doc(training.id).set(training.toMap());
    }
  }

  static Future<void> _createDemoDepartments() async {
    final departments = [
      {
        'id': 'dept_production',
        'name': 'Production',
        'description': 'Département responsable de la production industrielle',
        'isActive': true,
        'positions': [
          {'id': 'pos_operateur', 'name': 'Opérateur', 'level': 1},
          {'id': 'pos_operatrice', 'name': 'Opératrice', 'level': 1},
          {'id': 'pos_operateur_senior', 'name': 'Opérateur Senior', 'level': 2},
          {'id': 'pos_operatrice_senior', 'name': 'Opératrice Senior', 'level': 2},
          {'id': 'pos_chef_equipe', 'name': 'Chef d\'équipe', 'level': 3},
          {'id': 'pos_superviseur', 'name': 'Superviseur', 'level': 4},
        ],
      },
      {
        'id': 'dept_maintenance',
        'name': 'Maintenance',
        'description': 'Département responsable de la maintenance des équipements',
        'isActive': true,
        'positions': [
          {'id': 'pos_technicien', 'name': 'Technicien', 'level': 1},
          {'id': 'pos_electricien', 'name': 'Électricien', 'level': 2},
          {'id': 'pos_mecanicien', 'name': 'Mécanicien', 'level': 2},
          {'id': 'pos_chef_maintenance', 'name': 'Chef Maintenance', 'level': 3},
        ],
      },
      {
        'id': 'dept_qualite',
        'name': 'Qualité',
        'description': 'Département responsable du contrôle qualité',
        'isActive': true,
        'positions': [
          {'id': 'pos_controleur', 'name': 'Contrôleur Qualité', 'level': 1},
          {'id': 'pos_controleuse', 'name': 'Contrôleuse Qualité', 'level': 1},
          {'id': 'pos_inspecteur', 'name': 'Inspecteur Qualité', 'level': 2},
          {'id': 'pos_inspectrice', 'name': 'Inspectrice Qualité', 'level': 2},
          {'id': 'pos_responsable_qualite', 'name': 'Responsable Qualité', 'level': 3},
        ],
      },
      {
        'id': 'dept_securite',
        'name': 'Sécurité',
        'description': 'Département responsable de la sécurité industrielle',
        'isActive': true,
        'positions': [
          {'id': 'pos_agent_securite', 'name': 'Agent de Sécurité', 'level': 1},
          {'id': 'pos_responsable_securite', 'name': 'Responsable Sécurité', 'level': 2},
          {'id': 'pos_chef_securite', 'name': 'Chef Sécurité', 'level': 3},
        ],
      },
      {
        'id': 'dept_logistique',
        'name': 'Logistique',
        'description': 'Département responsable de la logistique et du stockage',
        'isActive': true,
        'positions': [
          {'id': 'pos_magasinier', 'name': 'Magasinier', 'level': 1},
          {'id': 'pos_responsable_expedition', 'name': 'Responsable Expédition', 'level': 2},
          {'id': 'pos_chef_logistique', 'name': 'Chef Logistique', 'level': 3},
        ],
      },
      {
        'id': 'dept_administration',
        'name': 'Administration',
        'description': 'Département administratif et ressources humaines',
        'isActive': true,
        'positions': [
          {'id': 'pos_assistante_rh', 'name': 'Assistante RH', 'level': 1},
          {'id': 'pos_gestionnaire_admin', 'name': 'Gestionnaire Administrative', 'level': 2},
          {'id': 'pos_responsable_rh', 'name': 'Responsable RH', 'level': 3},
        ],
      },
      {
        'id': 'dept_laboratoire',
        'name': 'Laboratoire',
        'description': 'Département des analyses et tests en laboratoire',
        'isActive': true,
        'positions': [
          {'id': 'pos_technicien_labo', 'name': 'Technicien Laboratoire', 'level': 1},
          {'id': 'pos_technicienne_labo', 'name': 'Technicienne Laboratoire', 'level': 1},
          {'id': 'pos_analyste', 'name': 'Analyste', 'level': 2},
          {'id': 'pos_responsable_labo', 'name': 'Responsable Laboratoire', 'level': 3},
        ],
      },
      {
        'id': 'dept_environnement',
        'name': 'Environnement',
        'description': 'Département responsable de l\'environnement et du développement durable',
        'isActive': true,
        'positions': [
          {'id': 'pos_technicien_env', 'name': 'Technicien Environnement', 'level': 1},
          {'id': 'pos_responsable_env', 'name': 'Responsable Environnement', 'level': 2},
          {'id': 'pos_chef_env', 'name': 'Chef Environnement', 'level': 3},
        ],
      },
    ];

    for (final department in departments) {
      await _firestore.collection('departments').doc(department['id'] as String).set({
        ...department,
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    }
  }

  static Future<void> _createDemoUserSessions() async {
    // Créer des sessions de connexion récentes pour simuler l'activité
    final sessions = [
      {
        'id': 'session_1',
        'userId': 'emp_gabes_1',
        'userEmail': '<EMAIL>',
        'loginTime': DateTime.now().subtract(const Duration(hours: 2)),
        'logoutTime': null, // Session active
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'deviceType': 'desktop',
        'isActive': true,
      },
      {
        'id': 'session_2',
        'userId': 'emp_sfax_1',
        'userEmail': '<EMAIL>',
        'loginTime': DateTime.now().subtract(const Duration(hours: 1)),
        'logoutTime': null, // Session active
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (Android 12; Mobile; rv:68.0) Gecko/68.0 Firefox/68.0',
        'deviceType': 'mobile',
        'isActive': true,
      },
      {
        'id': 'session_3',
        'userId': 'user3',
        'userEmail': '<EMAIL>',
        'loginTime': DateTime.now().subtract(const Duration(hours: 4)),
        'logoutTime': DateTime.now().subtract(const Duration(hours: 1)),
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'deviceType': 'desktop',
        'isActive': false,
      },
      {
        'id': 'session_4',
        'userId': 'emp_tunis_1',
        'userEmail': '<EMAIL>',
        'loginTime': DateTime.now().subtract(const Duration(minutes: 30)),
        'logoutTime': null, // Session active
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
        'deviceType': 'tablet',
        'isActive': true,
      },
      {
        'id': 'session_5',
        'userId': 'user2',
        'userEmail': '<EMAIL>',
        'loginTime': DateTime.now().subtract(const Duration(hours: 6)),
        'logoutTime': DateTime.now().subtract(const Duration(hours: 2)),
        'ipAddress': '************',
        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'deviceType': 'desktop',
        'isActive': false,
      },
    ];

    for (final session in sessions) {
      await _firestore.collection('user_sessions').doc(session['id'] as String).set({
        ...session,
        'loginTime': Timestamp.fromDate(session['loginTime'] as DateTime),
        'logoutTime': session['logoutTime'] != null
            ? Timestamp.fromDate(session['logoutTime'] as DateTime)
            : null,
        'createdAt': Timestamp.fromDate(DateTime.now()),
      });
    }

    // Créer des statistiques d'utilisation
    final usageStats = [
      {
        'id': 'stats_daily_${DateTime.now().toString().substring(0, 10)}',
        'date': DateTime.now(),
        'totalLogins': 15,
        'uniqueUsers': 12,
        'avgSessionDuration': 45, // minutes
        'peakHour': 9,
        'deviceBreakdown': {
          'desktop': 8,
          'mobile': 5,
          'tablet': 2,
        },
        'factoryBreakdown': {
          'Usine de Gabès': 4,
          'Usine de Sfax': 3,
          'Usine de Tunis': 3,
          'Usine de Sousse': 2,
        },
      },
      {
        'id': 'stats_daily_${DateTime.now().subtract(const Duration(days: 1)).toString().substring(0, 10)}',
        'date': DateTime.now().subtract(const Duration(days: 1)),
        'totalLogins': 18,
        'uniqueUsers': 14,
        'avgSessionDuration': 52,
        'peakHour': 10,
        'deviceBreakdown': {
          'desktop': 10,
          'mobile': 6,
          'tablet': 2,
        },
        'factoryBreakdown': {
          'Usine de Gabès': 5,
          'Usine de Sfax': 4,
          'Usine de Tunis': 5,
          'Usine de Sousse': 4,
        },
      },
    ];

    for (final stats in usageStats) {
      await _firestore.collection('usage_statistics').doc(stats['id'] as String).set({
        ...stats,
        'date': Timestamp.fromDate(stats['date'] as DateTime),
        'createdAt': Timestamp.fromDate(DateTime.now()),
      });
    }
  }

  static Future<void> _createDemoNotifications() async {
    final notifications = [
      {
        'id': 'notif1',
        'userId': 'user5',
        'title': 'Nouvelle réclamation assignée',
        'message': 'Une réclamation urgente vous a été assignée.',
        'type': 'claim',
        'isRead': false,
        'createdAt': DateTime.now().subtract(const Duration(hours: 2)),
        'data': {'claimId': 'claim1'},
      },
      {
        'id': 'notif2',
        'userId': 'user6',
        'title': 'Formation approuvée',
        'message': 'Votre demande de formation a été approuvée.',
        'type': 'training',
        'isRead': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 1)),
        'data': {'trainingId': 'training2'},
      },
      {
        'id': 'notif3',
        'userId': 'user3',
        'title': 'Rapport mensuel disponible',
        'message': 'Le rapport de sécurité mensuel est prêt.',
        'type': 'report',
        'isRead': false,
        'createdAt': DateTime.now().subtract(const Duration(hours: 6)),
        'data': {'reportId': 'report_monthly_2024_01'},
      },
    ];

    for (final notification in notifications) {
      await _firestore.collection('notifications').doc(notification['id'] as String).set({
        ...notification,
        'createdAt': Timestamp.fromDate(notification['createdAt'] as DateTime),
      });
    }
  }

  static Future<void> _createDemoReports() async {
    final reports = [
      {
        'id': 'report_monthly_2024_01',
        'title': 'Rapport Mensuel - Janvier 2024',
        'type': 'monthly',
        'period': '2024-01',
        'factoryId': 'factory_gabes',
        'factoryName': 'Usine de Gabès',
        'generatedBy': 'user3',
        'generatedAt': DateTime.now().subtract(const Duration(days: 5)),
        'data': {
          'totalClaims': 12,
          'resolvedClaims': 8,
          'pendingClaims': 4,
          'totalTrainings': 25,
          'completedTrainings': 20,
          'pendingTrainings': 5,
          'safetyScore': 85.5,
          'incidents': 2,
          'nearMisses': 5,
        },
        'status': 'completed',
      },
      {
        'id': 'report_weekly_2024_w05',
        'title': 'Rapport Hebdomadaire - Semaine 5 2024',
        'type': 'weekly',
        'period': '2024-W05',
        'factoryId': 'factory_sfax',
        'factoryName': 'Usine de Sfax',
        'generatedBy': 'user4',
        'generatedAt': DateTime.now().subtract(const Duration(days: 2)),
        'data': {
          'totalClaims': 3,
          'resolvedClaims': 2,
          'pendingClaims': 1,
          'totalTrainings': 8,
          'completedTrainings': 6,
          'pendingTrainings': 2,
          'safetyScore': 92.0,
          'incidents': 0,
          'nearMisses': 1,
        },
        'status': 'completed',
      },
    ];

    for (final report in reports) {
      await _firestore.collection('reports').doc(report['id'] as String).set({
        ...report,
        'generatedAt': Timestamp.fromDate(report['generatedAt'] as DateTime),
      });
    }
  }

  static Future<void> _createDemoSettings() async {
    final settings = {
      'app_settings': {
        'appName': 'GCT Security',
        'version': '1.0.0',
        'maintenanceMode': false,
        'supportEmail': '<EMAIL>',
        'supportPhone': '+216 71 123 456',
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      },
      'notification_settings': {
        'emailNotifications': true,
        'pushNotifications': true,
        'smsNotifications': false,
        'urgentClaimsEmail': true,
        'trainingReminders': true,
        'reportGeneration': true,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      },
      'security_settings': {
        'passwordMinLength': 8,
        'passwordRequireSpecialChars': true,
        'sessionTimeout': 3600, // 1 hour in seconds
        'maxLoginAttempts': 5,
        'lockoutDuration': 900, // 15 minutes in seconds
        'twoFactorAuth': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      },
      'training_settings': {
        'mandatoryTrainingReminder': 7, // days before expiry
        'autoApprovalForBasicTraining': false,
        'maxTrainingRequestsPerMonth': 5,
        'trainingValidityMonths': 12,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      },
    };

    for (final entry in settings.entries) {
      await _firestore.collection('settings').doc(entry.key).set(entry.value);
    }
  }

  static Future<void> clearDemoData() async {
    try {
      // Supprimer toutes les collections de démonstration
      await _clearCollection('users');
      await _clearCollection('claims');
      await _clearCollection('trainings');
      await _clearCollection('factories');
      await _clearCollection('auth_users');
      await _clearCollection('departments');
      await _clearCollection('user_sessions');
      await _clearCollection('usage_statistics');
      await _clearCollection('notifications');
      await _clearCollection('reports');
      await _clearCollection('settings');
      
      print('Données de démonstration supprimées');
    } catch (e) {
      print('Erreur lors de la suppression des données de démonstration: $e');
    }
  }

  static Future<void> _clearCollection(String collectionName) async {
    final snapshot = await _firestore.collection(collectionName).get();
    for (final doc in snapshot.docs) {
      await doc.reference.delete();
    }
  }
}
