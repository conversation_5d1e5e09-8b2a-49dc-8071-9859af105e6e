import 'dart:io';

void main() async {
  print('Script pour créer des données Firebase via REST API');
  
  // Configuration Firebase
  const projectId = 'securityapp-8b742';
  const baseUrl = 'https://firestore.googleapis.com/v1/projects/$projectId/databases/(default)/documents';
  
  // Créer des données de test simples
  final userData = {
    'fields': {
      'id': {'stringValue': 'test_user_1'},
      'email': {'stringValue': '<EMAIL>'},
      'firstName': {'stringValue': 'Super'},
      'lastName': {'stringValue': 'Admin'},
      'role': {'stringValue': 'super_admin'},
      'isActive': {'booleanValue': true},
      'createdAt': {'timestampValue': DateTime.now().toIso8601String()},
    }
  };
  
  final claimData = {
    'fields': {
      'id': {'stringValue': 'test_claim_1'},
      'title': {'stringValue': 'Réclamation de test'},
      'description': {'stringValue': 'Description de test'},
      'type': {'stringValue': 'hazard'},
      'status': {'stringValue': 'pending'},
      'priority': {'stringValue': 'medium'},
      'userId': {'stringValue': 'test_user_1'},
      'userEmail': {'stringValue': '<EMAIL>'},
      'userName': {'stringValue': 'Super Admin'},
      'factory': {'stringValue': 'Usine de Test'},
      'createdAt': {'timestampValue': DateTime.now().toIso8601String()},
      'attachments': {'arrayValue': {'values': []}},
    }
  };
  
  final trainingData = {
    'fields': {
      'id': {'stringValue': 'test_training_1'},
      'title': {'stringValue': 'Formation de test'},
      'description': {'stringValue': 'Description de formation'},
      'type': {'stringValue': 'safety'},
      'status': {'stringValue': 'pending'},
      'userId': {'stringValue': 'test_user_1'},
      'userEmail': {'stringValue': '<EMAIL>'},
      'userName': {'stringValue': 'Super Admin'},
      'factory': {'stringValue': 'Usine de Test'},
      'requestedAt': {'timestampValue': DateTime.now().toIso8601String()},
      'durationHours': {'integerValue': '8'},
      'isMandatory': {'booleanValue': true},
      'prerequisites': {'arrayValue': {'values': []}},
    }
  };
  
  final factoryData = {
    'fields': {
      'id': {'stringValue': 'test_factory_1'},
      'name': {'stringValue': 'Usine de Test'},
      'code': {'stringValue': 'TEST'},
      'address': {'stringValue': 'Adresse de test'},
      'city': {'stringValue': 'Tunis'},
      'region': {'stringValue': 'Tunis'},
      'country': {'stringValue': 'Tunisie'},
      'isActive': {'booleanValue': true},
      'createdAt': {'timestampValue': DateTime.now().toIso8601String()},
    }
  };
  
  print('Données préparées pour Firebase');
  print('Utilisateur: ${userData['fields']!['email']}');
  print('Réclamation: ${claimData['fields']!['title']}');
  print('Formation: ${trainingData['fields']!['title']}');
  print('Usine: ${factoryData['fields']!['name']}');
  
  print('\nPour ajouter ces données à Firebase:');
  print('1. Allez sur https://console.firebase.google.com/');
  print('2. Sélectionnez votre projet: securityapp-8b742');
  print('3. Allez dans Firestore Database');
  print('4. Créez les collections: users, claims, trainings, factories');
  print('5. Ajoutez les documents avec les données ci-dessus');
  
  print('\nOu utilisez l\'interface web de Firebase pour ajouter manuellement:');
  print('- Collection "users" -> Document "test_user_1"');
  print('- Collection "claims" -> Document "test_claim_1"');
  print('- Collection "trainings" -> Document "test_training_1"');
  print('- Collection "factories" -> Document "test_factory_1"');
}
