import 'package:get/get.dart';
import '../../data/services/auth_service.dart';
import '../../data/services/firestore_service.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/dashboard_controller.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Services (Singletons)
    Get.put<FirestoreService>(FirestoreService(), permanent: true);
    Get.put<AuthService>(AuthService(), permanent: true);
    
    // Controllers
    Get.put<AuthController>(AuthController(), permanent: true);
    Get.put<DashboardController>(DashboardController(), permanent: true);
  }
}
