import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

/// Demo authentication data source for development
/// This simulates Firebase Auth without requiring real authentication
class AuthDemoDataSource {
  // Demo users for testing
  static final Map<String, Map<String, dynamic>> _demoUsers = {
    '<EMAIL>': {
      'id': 'demo-admin-001',
      'email': '<EMAIL>',
      'firstName': 'Super',
      'lastName': 'Admin',
      'role': 'SUPER_ADMIN',
      'isActive': true,
      'createdAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    },
    '<EMAIL>': {
      'id': 'demo-security-001',
      'email': '<EMAIL>',
      'firstName': 'Admin',
      'lastName': 'Sécurité',
      'role': 'SECURITY_ADMIN_GCT',
      'isActive': true,
      'createdAt': DateTime.now().subtract(const Duration(days: 25)).toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    },
    '<EMAIL>': {
      'id': 'demo-manager-001',
      'email': '<EMAIL>',
      'firstName': 'Ahmed',
      'lastName': 'Ben Ali',
      'role': 'FACTORY_ADMIN',
      'factoryId': 'demo-factory-sfax',
      'factoryName': 'Usine Sfax',
      'isActive': true,
      'createdAt': DateTime.now().subtract(const Duration(days: 20)).toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    },
    '<EMAIL>': {
      'id': 'demo-employee-001',
      'email': '<EMAIL>',
      'firstName': 'Employé',
      'lastName': 'Test',
      'role': 'EMPLOYEE',
      'factoryId': 'demo-factory-sfax',
      'factoryName': 'Usine Sfax',
      'isActive': true,
      'createdAt': DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    },
  };

  // Demo passwords (in production, this would be handled by Firebase Auth)
  static final Map<String, String> _demoPasswords = {
    '<EMAIL>': 'Admin123!',
    '<EMAIL>': 'Security123!',
    '<EMAIL>': 'Manager123!',
    '<EMAIL>': 'Employee123!',
  };

  static UserModel? _currentUser;

  static Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));

    // Check if user exists
    if (!_demoUsers.containsKey(email)) {
      throw const AuthenticationException(message: 'Utilisateur non trouvé');
    }

    // Check password
    if (_demoPasswords[email] != password) {
      throw const AuthenticationException(message: 'Mot de passe incorrect');
    }

    // Get user data
    final userData = Map<String, dynamic>.from(_demoUsers[email]!);
    userData['lastLoginAt'] = DateTime.now().toIso8601String();

    final user = UserModel.fromJson(userData);
    _currentUser = user;

    return user;
  }

  static Future<void> signOut() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    _currentUser = null;
  }

  static Future<UserModel?> getCurrentUser() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _currentUser;
  }

  static bool get isAuthenticated => _currentUser != null;

  static String? get currentUserId => _currentUser?.id;

  // Get demo credentials for testing
  static Map<String, String> getDemoCredentials() {
    return Map<String, String>.from(_demoPasswords);
  }

  // Add a new demo user (for testing)
  static void addDemoUser({
    required String email,
    required String password,
    required Map<String, dynamic> userData,
  }) {
    _demoUsers[email] = userData;
    _demoPasswords[email] = password;
  }

  // Reset demo data
  static void resetDemoData() {
    _currentUser = null;
  }

  // Get all demo users (for admin testing)
  static List<UserModel> getAllDemoUsers() {
    return _demoUsers.values
        .map((userData) => UserModel.fromJson(userData))
        .toList();
  }

  // Simulate user creation (in production, this would be done through Firebase Auth)
  static Future<UserModel> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? factoryId,
    String? factoryName,
    String? phoneNumber,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 2000));

    // Check if user already exists
    if (_demoUsers.containsKey(email)) {
      throw const AuthenticationException(message: 'Email déjà utilisé');
    }

    // Create new user data
    final userData = {
      'id': 'demo-${DateTime.now().millisecondsSinceEpoch}',
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'role': role,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'isActive': true,
      'createdAt': DateTime.now().toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    };

    // Add to demo data
    _demoUsers[email] = userData;
    _demoPasswords[email] = password;

    final user = UserModel.fromJson(userData);
    _currentUser = user;

    return user;
  }

  // Simulate password reset
  static Future<void> sendPasswordResetEmail(String email) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    if (!_demoUsers.containsKey(email)) {
      throw const AuthenticationException(message: 'Utilisateur non trouvé');
    }

    // In demo mode, just print the reset info
    print('Demo: Password reset email sent to $email');
    print('Demo: New password would be: Reset123!');
    
    // Update demo password
    _demoPasswords[email] = 'Reset123!';
  }

  // Get user by ID (for testing)
  static UserModel? getUserById(String id) {
    for (final userData in _demoUsers.values) {
      if (userData['id'] == id) {
        return UserModel.fromJson(userData);
      }
    }
    return null;
  }

  // Update user profile
  static Future<UserModel> updateUserProfile(UserModel user) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    final userData = user.toJson();
    userData['updatedAt'] = DateTime.now().toIso8601String();

    // Update in demo data
    _demoUsers[user.email] = userData;

    if (_currentUser?.id == user.id) {
      _currentUser = UserModel.fromJson(userData);
    }

    return UserModel.fromJson(userData);
  }
}
