{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-baba4e3a828f4d74396b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-5d045f1b48a9327be522.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cb152c380a4a1431765d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-5d045f1b48a9327be522.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cb152c380a4a1431765d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-baba4e3a828f4d74396b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}