import 'package:cloud_firestore/cloud_firestore.dart';

enum TrainingStatus {
  pending,
  approved,
  rejected,
  completed,
  expired,
}

enum TrainingType {
  safety,
  security,
  emergency,
  equipment,
  procedure,
}

class TrainingModel {
  final String id;
  final String title;
  final String description;
  final TrainingType type;
  final TrainingStatus status;
  final String userId;
  final String userEmail;
  final String userName;
  final String factory;
  final DateTime requestedAt;
  final DateTime? approvedAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final String? approvedBy;
  final String? rejectionReason;
  final int durationHours;
  final bool isMandatory;
  final List<String> prerequisites;
  final Map<String, dynamic>? certificate;

  TrainingModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.status = TrainingStatus.pending,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.factory,
    required this.requestedAt,
    this.approvedAt,
    this.completedAt,
    this.expiresAt,
    this.approvedBy,
    this.rejectionReason,
    this.durationHours = 8,
    this.isMandatory = false,
    this.prerequisites = const [],
    this.certificate,
  });

  String get typeDisplayName {
    switch (type) {
      case TrainingType.safety:
        return 'Sécurité';
      case TrainingType.security:
        return 'Sûreté';
      case TrainingType.emergency:
        return 'Urgence';
      case TrainingType.equipment:
        return 'Équipement';
      case TrainingType.procedure:
        return 'Procédure';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TrainingStatus.pending:
        return 'En attente';
      case TrainingStatus.approved:
        return 'Approuvé';
      case TrainingStatus.rejected:
        return 'Rejeté';
      case TrainingStatus.completed:
        return 'Terminé';
      case TrainingStatus.expired:
        return 'Expiré';
    }
  }

  bool get isCompleted => status == TrainingStatus.completed;
  bool get isPending => status == TrainingStatus.pending;
  bool get isApproved => status == TrainingStatus.approved;
  bool get isExpired => status == TrainingStatus.expired || 
      (expiresAt != null && DateTime.now().isAfter(expiresAt!));

  // Calcul de la validité restante
  Duration? get remainingValidity {
    if (expiresAt == null || !isCompleted) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'factory': factory,
      'requestedAt': requestedAt,
      'approvedAt': approvedAt,
      'completedAt': completedAt,
      'expiresAt': expiresAt,
      'approvedBy': approvedBy,
      'rejectionReason': rejectionReason,
      'durationHours': durationHours,
      'isMandatory': isMandatory,
      'prerequisites': prerequisites,
      'certificate': certificate,
    };
  }

  // Création depuis Map Firestore
  factory TrainingModel.fromMap(Map<String, dynamic> map) {
    return TrainingModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: TrainingType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TrainingType.safety,
      ),
      status: TrainingStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => TrainingStatus.pending,
      ),
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      userName: map['userName'] ?? '',
      factory: map['factory'] ?? '',
      requestedAt: (map['requestedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      approvedAt: (map['approvedAt'] as Timestamp?)?.toDate(),
      completedAt: (map['completedAt'] as Timestamp?)?.toDate(),
      expiresAt: (map['expiresAt'] as Timestamp?)?.toDate(),
      approvedBy: map['approvedBy'],
      rejectionReason: map['rejectionReason'],
      durationHours: map['durationHours'] ?? 8,
      isMandatory: map['isMandatory'] ?? false,
      prerequisites: List<String>.from(map['prerequisites'] ?? []),
      certificate: map['certificate'],
    );
  }

  // Création depuis DocumentSnapshot
  factory TrainingModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TrainingModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  TrainingModel copyWith({
    String? id,
    String? title,
    String? description,
    TrainingType? type,
    TrainingStatus? status,
    String? userId,
    String? userEmail,
    String? userName,
    String? factory,
    DateTime? requestedAt,
    DateTime? approvedAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? approvedBy,
    String? rejectionReason,
    int? durationHours,
    bool? isMandatory,
    List<String>? prerequisites,
    Map<String, dynamic>? certificate,
  }) {
    return TrainingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      factory: factory ?? this.factory,
      requestedAt: requestedAt ?? this.requestedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      completedAt: completedAt ?? this.completedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      approvedBy: approvedBy ?? this.approvedBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      durationHours: durationHours ?? this.durationHours,
      isMandatory: isMandatory ?? this.isMandatory,
      prerequisites: prerequisites ?? this.prerequisites,
      certificate: certificate ?? this.certificate,
    );
  }

  @override
  String toString() {
    return 'TrainingModel(id: $id, title: $title, type: $type, status: $status, factory: $factory)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrainingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
