import 'package:flutter/material.dart';

class AppColors {
  // === COULEURS CORPORATE GCT MODERNES ===

  // Couleurs principales GCT - Palette Corporate
  static const Color primary = Color(0xFF1B4F72); // Bleu corporate profond
  static const Color primaryLight = Color(0xFF2E86AB); // Bleu corporate moderne
  static const Color primaryDark = Color(0xFF0F2A44); // Bleu corporate très foncé

  // Couleurs secondaires - Vert GCT moderne
  static const Color secondary = Color(0xFF28B463); // Vert GCT moderne
  static const Color secondaryLight = Color(0xFF58D68D); // Vert clair élégant
  static const Color secondaryDark = Color(0xFF1E8449); // Vert foncé professionnel

  // Couleurs d'accent - Or corporate élégant
  static const Color accent = Color(0xFFD4AF37); // Or corporate élégant
  static const Color accentLight = Color(0xFFE8C547); // Or clair premium
  static const Color accentDark = Color(0xFFB8941F); // Or foncé luxueux
  
  // Couleurs de statut
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Couleurs neutres
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);
  
  // === GRADIENTS CORPORATE MODERNES ===

  // Gradient principal corporate
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1B4F72), // Bleu corporate profond
      Color(0xFF2E86AB), // Bleu corporate moderne
      Color(0xFF28B463), // Vert GCT moderne
    ],
    stops: [0.0, 0.6, 1.0],
  );

  // Gradient hero section
  static const LinearGradient heroGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0F2A44), // Bleu très foncé
      Color(0xFF1B4F72), // Bleu corporate
      Color(0xFF2E86AB), // Bleu moderne
    ],
    stops: [0.0, 0.5, 1.0],
  );

  // Gradient glassmorphism
  static const LinearGradient glassGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x20FFFFFF), // Blanc transparent
      Color(0x10FFFFFF), // Blanc très transparent
    ],
  );

  // Gradient accent or
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFD4AF37), // Or corporate
      Color(0xFFE8C547), // Or clair
      Color(0xFFF4D03F), // Or brillant
    ],
  );
  
  // Couleurs spécifiques aux rôles
  static const Color superAdmin = Color(0xFFE91E63); // Rose/Rouge
  static const Color securityAdmin = Color(0xFF9C27B0); // Violet
  static const Color factoryAdmin = Color(0xFF3F51B5); // Indigo
  static const Color employee = Color(0xFF009688); // Teal
  
  // Couleurs pour les types de réclamations
  static const Color accident = Color(0xFFF44336); // Rouge
  static const Color incident = Color(0xFFFF9800); // Orange
  static const Color nearMiss = Color(0xFFFFC107); // Jaune
  static const Color nonCompliance = Color(0xFF9C27B0); // Violet
  static const Color suggestion = Color(0xFF4CAF50); // Vert
  static const Color hazard = Color(0xFFE91E63); // Rose
  
  // Couleurs pour les statuts
  static const Color pending = Color(0xFFFF9800); // Orange
  static const Color inProgress = Color(0xFF2196F3); // Bleu
  static const Color resolved = Color(0xFF4CAF50); // Vert
  static const Color rejected = Color(0xFFF44336); // Rouge
  static const Color closed = Color(0xFF9E9E9E); // Gris
}

class AppTheme {
  static ThemeData get corporateTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Inter', // Police moderne et professionnelle
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        tertiary: AppColors.accent,
        surface: AppColors.grey50,
        background: AppColors.white,
        error: AppColors.error,
        onPrimary: AppColors.white,
        onSecondary: AppColors.white,
        onSurface: AppColors.grey900,
        onBackground: AppColors.grey900,
        onError: AppColors.white,
      ),

      // AppBar moderne avec gradient
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.white,
          letterSpacing: 0.5,
        ),
        iconTheme: const IconThemeData(
          color: AppColors.white,
          size: 24,
        ),
      ),
      // Boutons élevés corporate
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          elevation: 8,
          shadowColor: AppColors.primary.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Boutons outline élégants
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: const BorderSide(color: AppColors.primary, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Cards avec glassmorphism
      cardTheme: CardTheme(
        elevation: 12,
        shadowColor: AppColors.primary.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        color: AppColors.white,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      ),
      // Champs de saisie modernes
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.grey300, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.grey300, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.primary, width: 2.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: AppColors.grey50,
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        labelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.grey600,
        ),
        hintStyle: const TextStyle(
          fontSize: 16,
          color: AppColors.grey500,
        ),
      ),

      // Typographie corporate
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 48,
          fontWeight: FontWeight.w900,
          color: AppColors.grey900,
          letterSpacing: -1,
        ),
        displayMedium: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.w800,
          color: AppColors.grey900,
          letterSpacing: -0.5,
        ),
        headlineLarge: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w700,
          color: AppColors.grey900,
          letterSpacing: 0,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: AppColors.grey900,
          letterSpacing: 0.25,
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.grey900,
          letterSpacing: 0.15,
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.grey800,
          letterSpacing: 0.15,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: AppColors.grey700,
          letterSpacing: 0.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.grey600,
          letterSpacing: 0.25,
        ),
      ),
    );
  }

  // Getter pour compatibilité
  static ThemeData get lightTheme => corporateTheme;
}
