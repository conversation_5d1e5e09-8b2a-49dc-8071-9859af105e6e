import 'dart:async';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/firebase_service.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_firebase_datasource.dart';
import '../datasources/auth_local_datasource.dart';
import '../models/user_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthFirebaseDataSource firebaseDataSource;
  final AuthLocalDataSource localDataSource;

  final StreamController<User?> _userController = StreamController<User?>.broadcast();
  StreamSubscription? _authStateSubscription;

  AuthRepositoryImpl({
    required this.firebaseDataSource,
    required this.localDataSource,
  }) {
    // Listen to Firebase auth state changes
    _authStateSubscription = firebaseDataSource.authStateChanges.listen((firebaseUser) async {
      if (firebaseUser != null) {
        try {
          final userModel = await firebaseDataSource.getCurrentUser();
          if (userModel != null) {
            await localDataSource.saveUserData(userModel);
            _userController.add(userModel.toEntity());
          }
        } catch (e) {
          _userController.add(null);
        }
      } else {
        await localDataSource.clearUserData();
        _userController.add(null);
      }
    });
  }

  @override
  Stream<User?> get userStream => _userController.stream;

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  }) async {
    try {
      final userModel = await firebaseDataSource.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Save user data locally
      await localDataSource.saveUserData(userModel);

      // Update FCM token if available
      final fcmToken = await FirebaseService.getFCMToken();
      if (fcmToken != null && firebaseDataSource is AuthFirebaseDataSourceImpl) {
        await (firebaseDataSource as AuthFirebaseDataSourceImpl)
            .updateFCMToken(userModel.id, fcmToken);
      }

      final user = userModel.toEntity();
      _userController.add(user);

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message, code: e.statusCode));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur inattendue: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      // Sign out from Firebase
      await firebaseDataSource.signOut();

      // Clear local data
      await localDataSource.clearUserData();
      _userController.add(null);

      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur lors de la déconnexion: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      // Try to get from Firebase first
      final userModel = await firebaseDataSource.getCurrentUser();
      if (userModel != null) {
        await localDataSource.saveUserData(userModel);
        final user = userModel.toEntity();
        _userController.add(user);
        return Right(user);
      }

      // Fallback to local data
      final localUserModel = await localDataSource.getUserData();
      if (localUserModel != null) {
        final user = localUserModel.toEntity();
        _userController.add(user);
        return Right(user);
      }

      return const Left(AuthenticationFailure(message: 'Aucun utilisateur connecté'));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(message: e.message, code: e.statusCode));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur lors de la récupération de l\'utilisateur: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> refreshToken() async {
    try {
      // Firebase handles token refresh automatically
      // Just get the current user to verify authentication
      final userModel = await firebaseDataSource.getCurrentUser();
      if (userModel != null) {
        await localDataSource.saveUserData(userModel);
        final user = userModel.toEntity();
        _userController.add(user);
        return Right(user);
      } else {
        await localDataSource.clearUserData();
        _userController.add(null);
        return const Left(AuthenticationFailure(message: 'Session expirée'));
      }
    } on AuthenticationException catch (e) {
      await localDataSource.clearUserData();
      _userController.add(null);
      return Left(AuthenticationFailure(message: e.message, code: e.statusCode));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur lors du rafraîchissement: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isLoggedIn() async {
    try {
      final isLoggedIn = await localDataSource.isLoggedIn();
      return Right(isLoggedIn);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur lors de la vérification de connexion: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearUserData() async {
    try {
      await localDataSource.clearUserData();
      _userController.add(null);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Erreur lors de la suppression des données: $e'));
    }
  }

  void dispose() {
    _authStateSubscription?.cancel();
    _userController.close();
  }
}
