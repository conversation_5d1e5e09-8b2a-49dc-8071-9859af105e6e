import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/claim.dart';

class ClaimModel extends Claim {
  const ClaimModel({
    required super.id,
    required super.title,
    required super.description,
    required super.type,
    required super.status,
    required super.priority,
    required super.reporterId,
    required super.reporterName,
    super.assignedToId,
    super.assignedToName,
    required super.factoryId,
    required super.factoryName,
    required super.location,
    super.latitude,
    super.longitude,
    required super.incidentDate,
    required super.reportedAt,
    super.resolvedAt,
    super.attachments = const [],
    super.comments = const [],
    super.metadata,
  });

  factory ClaimModel.fromJson(Map<String, dynamic> json) {
    return ClaimModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      reporterId: json['reporterId'] as String,
      reporterName: json['reporterName'] as String,
      assignedToId: json['assignedToId'] as String?,
      assignedToName: json['assignedToName'] as String?,
      factoryId: json['factoryId'] as String,
      factoryName: json['factoryName'] as String,
      location: json['location'] as String,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      incidentDate: DateTime.parse(json['incidentDate'] as String),
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      resolvedAt: json['resolvedAt'] != null ? DateTime.parse(json['resolvedAt'] as String) : null,
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>() ?? [],
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => ClaimCommentModel.fromJson(e as Map<String, dynamic>).toEntity())
          .toList() ?? [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type,
      'status': status,
      'priority': priority,
      'reporterId': reporterId,
      'reporterName': reporterName,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'incidentDate': incidentDate.toIso8601String(),
      'reportedAt': reportedAt.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
      'attachments': attachments,
      'comments': comments.map((comment) => ClaimCommentModel.fromEntity(comment).toJson()).toList(),
      'metadata': metadata,
    };
  }

  // Convert from Firestore document
  factory ClaimModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    
    // Convert Firestore Timestamps to DateTime
    if (data['incidentDate'] is Timestamp) {
      data['incidentDate'] = (data['incidentDate'] as Timestamp).toDate().toIso8601String();
    }
    if (data['reportedAt'] is Timestamp) {
      data['reportedAt'] = (data['reportedAt'] as Timestamp).toDate().toIso8601String();
    }
    if (data['resolvedAt'] is Timestamp) {
      data['resolvedAt'] = (data['resolvedAt'] as Timestamp).toDate().toIso8601String();
    }
    
    // Convert comments
    if (data['comments'] != null) {
      data['comments'] = (data['comments'] as List)
          .map((comment) => ClaimCommentModel.fromJson(comment as Map<String, dynamic>).toEntity())
          .toList();
    }
    
    return ClaimModel.fromJson(data);
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    final data = toJson();
    data.remove('id'); // Remove ID from document data
    
    // Convert DateTime to Firestore Timestamp
    data['incidentDate'] = Timestamp.fromDate(incidentDate);
    data['reportedAt'] = Timestamp.fromDate(reportedAt);
    if (resolvedAt != null) {
      data['resolvedAt'] = Timestamp.fromDate(resolvedAt!);
    }
    
    // Convert comments
    data['comments'] = comments.map((comment) =>
        ClaimCommentModel.fromEntity(comment).toJson()).toList();
    
    return data;
  }

  factory ClaimModel.fromEntity(Claim claim) {
    return ClaimModel(
      id: claim.id,
      title: claim.title,
      description: claim.description,
      type: claim.type,
      status: claim.status,
      priority: claim.priority,
      reporterId: claim.reporterId,
      reporterName: claim.reporterName,
      assignedToId: claim.assignedToId,
      assignedToName: claim.assignedToName,
      factoryId: claim.factoryId,
      factoryName: claim.factoryName,
      location: claim.location,
      latitude: claim.latitude,
      longitude: claim.longitude,
      incidentDate: claim.incidentDate,
      reportedAt: claim.reportedAt,
      resolvedAt: claim.resolvedAt,
      attachments: claim.attachments,
      comments: claim.comments,
      metadata: claim.metadata,
    );
  }

  Claim toEntity() {
    return Claim(
      id: id,
      title: title,
      description: description,
      type: type,
      status: status,
      priority: priority,
      reporterId: reporterId,
      reporterName: reporterName,
      assignedToId: assignedToId,
      assignedToName: assignedToName,
      factoryId: factoryId,
      factoryName: factoryName,
      location: location,
      latitude: latitude,
      longitude: longitude,
      incidentDate: incidentDate,
      reportedAt: reportedAt,
      resolvedAt: resolvedAt,
      attachments: attachments,
      comments: comments,
      metadata: metadata,
    );
  }

  ClaimModel copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? status,
    String? priority,
    String? reporterId,
    String? reporterName,
    String? assignedToId,
    String? assignedToName,
    String? factoryId,
    String? factoryName,
    String? location,
    double? latitude,
    double? longitude,
    DateTime? incidentDate,
    DateTime? reportedAt,
    DateTime? resolvedAt,
    List<String>? attachments,
    List<ClaimComment>? comments,
    Map<String, dynamic>? metadata,
  }) {
    return ClaimModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      reporterId: reporterId ?? this.reporterId,
      reporterName: reporterName ?? this.reporterName,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      incidentDate: incidentDate ?? this.incidentDate,
      reportedAt: reportedAt ?? this.reportedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      metadata: metadata ?? this.metadata,
    );
  }
}

class ClaimCommentModel extends ClaimComment {
  const ClaimCommentModel({
    required super.id,
    required super.claimId,
    required super.authorId,
    required super.authorName,
    required super.content,
    required super.createdAt,
    super.attachments = const [],
  });

  factory ClaimCommentModel.fromJson(Map<String, dynamic> json) {
    return ClaimCommentModel(
      id: json['id'] as String,
      claimId: json['claimId'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'claimId': claimId,
      'authorId': authorId,
      'authorName': authorName,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'attachments': attachments,
    };
  }

  factory ClaimCommentModel.fromEntity(ClaimComment comment) {
    return ClaimCommentModel(
      id: comment.id,
      claimId: comment.claimId,
      authorId: comment.authorId,
      authorName: comment.authorName,
      content: comment.content,
      createdAt: comment.createdAt,
      attachments: comment.attachments,
    );
  }

  ClaimComment toEntity() {
    return ClaimComment(
      id: id,
      claimId: claimId,
      authorId: authorId,
      authorName: authorName,
      content: content,
      createdAt: createdAt,
      attachments: attachments,
    );
  }

  ClaimCommentModel copyWith({
    String? id,
    String? claimId,
    String? authorId,
    String? authorName,
    String? content,
    DateTime? createdAt,
    List<String>? attachments,
  }) {
    return ClaimCommentModel(
      id: id ?? this.id,
      claimId: claimId ?? this.claimId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      attachments: attachments ?? this.attachments,
    );
  }
}
