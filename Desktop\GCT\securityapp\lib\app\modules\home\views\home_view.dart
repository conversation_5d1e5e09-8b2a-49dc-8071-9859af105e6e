import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_theme.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B4F72),
              Color(0xFF2E86AB),
              Color(0xFF28B463),
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 60),

                  // Logo et titre principal
                  _buildMainHeader(),

                  const SizedBox(height: 100),

                  // Bouton de connexion unique
                  _buildLoginButton(),

                  const SizedBox(height: 80),

                  // Footer simple
                  _buildSimpleFooter(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainHeader() {
    return Column(
      children: [
        // Logo GCT depuis les assets
        Container(
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(35),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 30,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(33),
            child: Image.asset(
              'assets/a.jpg',
              width: 156,
              height: 156,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.security_rounded,
                  size: 70,
                  color: Colors.white,
                );
              },
            ),
          ),
        ),

        const SizedBox(height: 40),

        // Titre principal
        Text(
          'GCT SECURITY',
          style: GoogleFonts.inter(
            fontSize: 42,
            fontWeight: FontWeight.w900,
            color: Colors.white,
            letterSpacing: 2,
            height: 1.1,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return const SizedBox.shrink(); // Suppression de la description
  }

  Widget _buildLoginButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 25,
            offset: const Offset(0, 12),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () => Get.toNamed('/login'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF1B4F72),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.login_rounded,
              size: 24,
              color: Color(0xFF1B4F72),
            ),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                'Accéder à la Plateforme',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF1B4F72),
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleFooter() {
    return Text(
      '© 2025 Groupe Chimique Tunisien',
      style: GoogleFonts.inter(
        fontSize: 14,
        color: Colors.white.withOpacity(0.7),
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
