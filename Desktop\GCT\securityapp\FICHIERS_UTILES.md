# ✅ FICHIERS UTILES - GCT Security App

## 🎯 ARCHITECTURE PRINCIPALE (GetX)

### **Point d'entrée**
```
lib/main.dart ✅ - Point d'entrée principal avec GetX
lib/firebase_options.dart ✅ - Configuration Firebase
```

### **Architecture GetX complète**
```
lib/app/ ✅ - Dossier principal de l'architecture GetX
├── controllers/ ✅
│   ├── auth_controller.dart ✅ - Contrôleur d'authentification
│   └── dashboard_controller.dart ✅ - Contrôleur dashboard avec Firebase
├── core/ ✅
│   ├── bindings/ ✅ - Bindings GetX
│   └── theme/ ✅ - Thème de l'application
├── data/ ✅
│   ├── models/ ✅ - Modèles de données
│   └── services/ ✅ - Services Firebase et auth
├── modules/ ✅
│   ├── auth/ ✅ - Module d'authentification
│   ├── dashboard/ ✅ - Modules dashboard par rôle
│   ├── claims/ ✅ - Module réclamations
│   ├── trainings/ ✅ - Module formations
│   └── users/ ✅ - Module utilisateurs
└── routes/ ✅
    ├── app_pages.dart ✅ - Configuration des routes
    └── app_routes.dart ✅ - Définition des routes
```

## 🔧 CONFIGURATION

### **Configuration Flutter**
```
pubspec.yaml ✅ - Dépendances principales
analysis_options.yaml ✅ - Options d'analyse
```

### **Configuration Firebase**
```
firebase.json ✅ - Configuration Firebase
firestore.rules ✅ - Règles Firestore
firestore.indexes.json ✅ - Index Firestore
```

### **Plateformes supportées**
```
android/ ✅ - Configuration Android
ios/ ✅ - Configuration iOS  
web/ ✅ - Configuration Web
windows/ ✅ - Configuration Windows
linux/ ✅ - Configuration Linux
macos/ ✅ - Configuration macOS
```

## 📱 RESSOURCES

### **Assets**
```
assets/ ✅ - Ressources de l'application
├── a.jpg ✅ - Logo GCT
├── icons/ ✅ - Icônes
├── images/ ✅ - Images
└── logos/ ✅ - Logos
```

### **Web**
```
web/ ✅ - Configuration web
├── index.html ✅ - Page principale web
├── manifest.json ✅ - Manifest PWA
└── icons/ ✅ - Icônes web
```

## 📚 DOCUMENTATION

```
README.md ✅ - Documentation principale
COMPTES_TEST.md ✅ - Comptes de test
FICHIERS_A_SUPPRIMER.md ✅ - Liste des fichiers obsolètes
FICHIERS_UTILES.md ✅ - Ce fichier
cleanup_obsolete_files.ps1 ✅ - Script de nettoyage
```

## 🧪 TESTS

```
test/ ✅ - Tests unitaires
└── widget_test.dart ✅ - Tests de widgets
```

## 🎯 FONCTIONNALITÉS PRINCIPALES

### **Authentification**
- `lib/app/modules/auth/` ✅ - Login/Register avec Firebase
- `lib/app/controllers/auth_controller.dart` ✅ - Gestion des utilisateurs

### **Dashboards par rôle**
- `lib/app/modules/dashboard/views/super_admin_dashboard_view.dart` ✅
- `lib/app/modules/dashboard/views/security_admin_dashboard_view.dart` ✅  
- `lib/app/modules/dashboard/views/factory_admin_dashboard_view.dart` ✅
- `lib/app/modules/dashboard/views/employee_dashboard_view.dart` ✅

### **Gestion des réclamations**
- `lib/app/modules/claims/` ✅ - CRUD réclamations avec Firebase

### **Gestion des formations**
- `lib/app/modules/trainings/` ✅ - CRUD formations avec Firebase

### **Gestion des utilisateurs**
- `lib/app/modules/users/` ✅ - CRUD utilisateurs avec Firebase

## 🔥 SERVICES FIREBASE

```
lib/app/data/services/ ✅
├── auth_service.dart ✅ - Authentification Firebase
├── firestore_service.dart ✅ - Base de données Firestore
└── demo_data_service.dart ✅ - Données de démonstration
```

## 🎨 THÈME ET UI

```
lib/app/core/theme/ ✅
├── app_theme.dart ✅ - Thème principal
└── app_colors.dart ✅ - Couleurs de l'application
```

## 🚀 COMMANDES UTILES

```bash
# Nettoyer et reconstruire
flutter clean && flutter pub get

# Lancer l'application
flutter run

# Lancer sur web
flutter run -d chrome

# Lancer sur Android
flutter run -d android

# Build pour production
flutter build apk
flutter build web
```
