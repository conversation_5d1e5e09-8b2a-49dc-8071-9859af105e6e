import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/claims_repository.dart';

class DeleteClaimUseCase {
  final ClaimsRepository repository;

  DeleteClaimUseCase(this.repository);

  Future<Either<Failure, void>> call(DeleteClaimParams params) async {
    return await repository.deleteClaim(params.claimId);
  }
}

class DeleteClaimParams {
  final String claimId;

  DeleteClaimParams({required this.claimId});
}
