import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/claim.dart';
import '../repositories/claims_repository.dart';

class GetClaimsUseCase {
  final ClaimsRepository repository;

  GetClaimsUseCase(this.repository);

  Future<Either<Failure, List<Claim>>> call(GetClaimsParams params) async {
    return await repository.getClaims(
      factoryId: params.factoryId,
      status: params.status,
      type: params.type,
      assignedToId: params.assignedToId,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetClaimsParams {
  final String? factoryId;
  final String? status;
  final String? type;
  final String? assignedToId;
  final int? page;
  final int? limit;

  GetClaimsParams({
    this.factoryId,
    this.status,
    this.type,
    this.assignedToId,
    this.page,
    this.limit,
  });
}
