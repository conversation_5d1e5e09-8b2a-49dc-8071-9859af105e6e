import 'package:equatable/equatable.dart';

class Training extends Equatable {
  final String id;
  final String title;
  final String description;
  final String category;
  final String level; // BASIC, INTERMEDIATE, ADVANCED
  final Duration duration;
  final bool isRequired;
  final DateTime? expiryDate;
  final List<String> prerequisites;
  final List<TrainingModule> modules;
  final String? certificateUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const Training({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.level,
    required this.duration,
    required this.isRequired,
    this.expiryDate,
    this.prerequisites = const [],
    this.modules = const [],
    this.certificateUrl,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  bool get isBasic => level == 'BASIC';
  bool get isIntermediate => level == 'INTERMEDIATE';
  bool get isAdvanced => level == 'ADVANCED';

  bool get hasPrerequisites => prerequisites.isNotEmpty;
  bool get hasModules => modules.isNotEmpty;
  bool get hasCertificate => certificateUrl != null;
  bool get hasExpiry => expiryDate != null;

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  Duration? get timeUntilExpiry {
    if (expiryDate == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiryDate!)) return null;
    return expiryDate!.difference(now);
  }

  Training copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? level,
    Duration? duration,
    bool? isRequired,
    DateTime? expiryDate,
    List<String>? prerequisites,
    List<TrainingModule>? modules,
    String? certificateUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Training(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      level: level ?? this.level,
      duration: duration ?? this.duration,
      isRequired: isRequired ?? this.isRequired,
      expiryDate: expiryDate ?? this.expiryDate,
      prerequisites: prerequisites ?? this.prerequisites,
      modules: modules ?? this.modules,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        category,
        level,
        duration,
        isRequired,
        expiryDate,
        prerequisites,
        modules,
        certificateUrl,
        createdAt,
        updatedAt,
        metadata,
      ];
}

class TrainingModule extends Equatable {
  final String id;
  final String trainingId;
  final String title;
  final String description;
  final int order;
  final Duration duration;
  final String contentType; // VIDEO, DOCUMENT, QUIZ, INTERACTIVE
  final String? contentUrl;
  final Map<String, dynamic>? content;
  final bool isRequired;

  const TrainingModule({
    required this.id,
    required this.trainingId,
    required this.title,
    required this.description,
    required this.order,
    required this.duration,
    required this.contentType,
    this.contentUrl,
    this.content,
    required this.isRequired,
  });

  bool get isVideo => contentType == 'VIDEO';
  bool get isDocument => contentType == 'DOCUMENT';
  bool get isQuiz => contentType == 'QUIZ';
  bool get isInteractive => contentType == 'INTERACTIVE';

  bool get hasContent => contentUrl != null || content != null;

  TrainingModule copyWith({
    String? id,
    String? trainingId,
    String? title,
    String? description,
    int? order,
    Duration? duration,
    String? contentType,
    String? contentUrl,
    Map<String, dynamic>? content,
    bool? isRequired,
  }) {
    return TrainingModule(
      id: id ?? this.id,
      trainingId: trainingId ?? this.trainingId,
      title: title ?? this.title,
      description: description ?? this.description,
      order: order ?? this.order,
      duration: duration ?? this.duration,
      contentType: contentType ?? this.contentType,
      contentUrl: contentUrl ?? this.contentUrl,
      content: content ?? this.content,
      isRequired: isRequired ?? this.isRequired,
    );
  }

  @override
  List<Object?> get props => [
        id,
        trainingId,
        title,
        description,
        order,
        duration,
        contentType,
        contentUrl,
        content,
        isRequired,
      ];
}

class UserTraining extends Equatable {
  final String id;
  final String userId;
  final String trainingId;
  final String status; // NOT_STARTED, IN_PROGRESS, COMPLETED, EXPIRED
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final double? progress; // 0.0 to 1.0
  final int? score;
  final List<UserTrainingModule> moduleProgress;
  final String? certificateUrl;
  final DateTime? validatedAt;
  final String? validatedBy;

  const UserTraining({
    required this.id,
    required this.userId,
    required this.trainingId,
    required this.status,
    this.startedAt,
    this.completedAt,
    this.expiresAt,
    this.progress,
    this.score,
    this.moduleProgress = const [],
    this.certificateUrl,
    this.validatedAt,
    this.validatedBy,
  });

  bool get isNotStarted => status == 'NOT_STARTED';
  bool get isInProgress => status == 'IN_PROGRESS';
  bool get isCompleted => status == 'COMPLETED';
  bool get isExpired => status == 'EXPIRED';

  bool get isValidated => validatedAt != null;
  bool get hasCertificate => certificateUrl != null;

  double get progressPercentage => (progress ?? 0.0) * 100;

  bool get needsRenewal {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  Duration? get timeUntilExpiry {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return null;
    return expiresAt!.difference(now);
  }

  UserTraining copyWith({
    String? id,
    String? userId,
    String? trainingId,
    String? status,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    double? progress,
    int? score,
    List<UserTrainingModule>? moduleProgress,
    String? certificateUrl,
    DateTime? validatedAt,
    String? validatedBy,
  }) {
    return UserTraining(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      trainingId: trainingId ?? this.trainingId,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      progress: progress ?? this.progress,
      score: score ?? this.score,
      moduleProgress: moduleProgress ?? this.moduleProgress,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      validatedAt: validatedAt ?? this.validatedAt,
      validatedBy: validatedBy ?? this.validatedBy,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        trainingId,
        status,
        startedAt,
        completedAt,
        expiresAt,
        progress,
        score,
        moduleProgress,
        certificateUrl,
        validatedAt,
        validatedBy,
      ];
}

class UserTrainingModule extends Equatable {
  final String id;
  final String userTrainingId;
  final String moduleId;
  final String status; // NOT_STARTED, IN_PROGRESS, COMPLETED
  final DateTime? startedAt;
  final DateTime? completedAt;
  final double? progress; // 0.0 to 1.0
  final int? score;
  final Duration? timeSpent;

  const UserTrainingModule({
    required this.id,
    required this.userTrainingId,
    required this.moduleId,
    required this.status,
    this.startedAt,
    this.completedAt,
    this.progress,
    this.score,
    this.timeSpent,
  });

  bool get isNotStarted => status == 'NOT_STARTED';
  bool get isInProgress => status == 'IN_PROGRESS';
  bool get isCompleted => status == 'COMPLETED';

  double get progressPercentage => (progress ?? 0.0) * 100;

  UserTrainingModule copyWith({
    String? id,
    String? userTrainingId,
    String? moduleId,
    String? status,
    DateTime? startedAt,
    DateTime? completedAt,
    double? progress,
    int? score,
    Duration? timeSpent,
  }) {
    return UserTrainingModule(
      id: id ?? this.id,
      userTrainingId: userTrainingId ?? this.userTrainingId,
      moduleId: moduleId ?? this.moduleId,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      progress: progress ?? this.progress,
      score: score ?? this.score,
      timeSpent: timeSpent ?? this.timeSpent,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userTrainingId,
        moduleId,
        status,
        startedAt,
        completedAt,
        progress,
        score,
        timeSpent,
      ];
}
