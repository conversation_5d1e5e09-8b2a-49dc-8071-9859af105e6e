class ServerException implements Exception {
  final String message;
  final int? statusCode;

  const ServerException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'ServerException: $message (Code: $statusCode)';
}

class NetworkException implements Exception {
  final String message;
  final int? code;

  const NetworkException({
    required this.message,
    this.code,
  });

  @override
  String toString() => 'NetworkException: $message';
}

class CacheException implements Exception {
  final String message;

  const CacheException({
    required this.message,
  });

  @override
  String toString() => 'CacheException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  final int? statusCode;

  const AuthenticationException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'AuthenticationException: $message (Code: $statusCode)';
}

class AuthorizationException implements Exception {
  final String message;
  final int? statusCode;

  const AuthorizationException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'AuthorizationException: $message (Code: $statusCode)';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? errors;

  const ValidationException({
    required this.message,
    this.errors,
  });

  @override
  String toString() => 'ValidationException: $message';
}

class FileException implements Exception {
  final String message;

  const FileException({
    required this.message,
  });

  @override
  String toString() => 'FileException: $message';
}

class LocationException implements Exception {
  final String message;

  const LocationException({
    required this.message,
  });

  @override
  String toString() => 'LocationException: $message';
}

class PermissionException implements Exception {
  final String message;

  const PermissionException({
    required this.message,
  });

  @override
  String toString() => 'PermissionException: $message';
}
