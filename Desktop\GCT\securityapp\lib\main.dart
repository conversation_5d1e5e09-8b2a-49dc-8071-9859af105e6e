import 'package:flutter/material.dart';
// import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
// import 'firebase_options.dart';
import 'app/routes/app_pages.dart';
import 'app/core/theme/app_theme.dart';
// import 'app/core/bindings/initial_binding.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('🚀 Démarrage de l\'application GCT Security...');

  // Firebase temporairement désactivé pour test
  print('⚠️ Firebase désactivé pour test...');

  print('📱 Lancement de l\'interface utilisateur...');
  runApp(const GCTSecurityApp());
}

class GCTSecurityApp extends StatelessWidget {
  const GCTSecurityApp({super.key});

  @override
  Widget build(BuildContext context) {
    print('🎨 Construction de l\'interface GetMaterialApp...');
    return GetMaterialApp(
      title: 'GCT Security',
      debugShowCheckedModeBanner: false,

      // Theme
      theme: AppTheme.lightTheme,

      // Initial Binding (temporairement désactivé)
      // initialBinding: InitialBinding(),

      // Routes
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,

      // Page de fallback en cas d'erreur
      unknownRoute: GetPage(
        name: '/unknown',
        page: () => const Scaffold(
          body: Center(
            child: Text('Page non trouvée'),
          ),
        ),
      ),
    );
  }
}
