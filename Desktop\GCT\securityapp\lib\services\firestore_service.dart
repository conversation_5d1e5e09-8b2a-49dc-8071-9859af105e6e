import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/claim_model.dart';
import '../models/training_model.dart';
import '../models/factory_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String usersCollection = 'users';
  static const String claimsCollection = 'claims';
  static const String trainingsCollection = 'trainings';
  static const String factoriesCollection = 'factories';

  // ==================== UTILISATEURS ====================

  // Créer un profil utilisateur
  Future<void> createUserProfile(UserModel user) async {
    try {
      await _firestore.collection(usersCollection).doc(user.id).set(user.toJson());
    } catch (e) {
      throw Exception('Erreur création profil: $e');
    }
  }

  // Obtenir un utilisateur par ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await _firestore.collection(usersCollection).doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur récupération utilisateur: $e');
    }
  }

  // Obtenir tous les utilisateurs (Admin seulement)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _firestore.collection(usersCollection).get();
      return querySnapshot.docs.map((doc) {
        return UserModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération utilisateurs: $e');
    }
  }

  // Obtenir les utilisateurs par usine
  Future<List<UserModel>> getUsersByFactory(String factoryId) async {
    try {
      final querySnapshot = await _firestore
          .collection(usersCollection)
          .where('factoryId', isEqualTo: factoryId)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return UserModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération utilisateurs par usine: $e');
    }
  }

  // Mettre à jour un utilisateur
  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore.collection(usersCollection).doc(user.id).update(user.toJson());
    } catch (e) {
      throw Exception('Erreur mise à jour utilisateur: $e');
    }
  }

  // ==================== RÉCLAMATIONS ====================

  // Créer une réclamation
  Future<String> createClaim(ClaimModel claim) async {
    try {
      final docRef = await _firestore.collection(claimsCollection).add(claim.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur création réclamation: $e');
    }
  }

  // Obtenir toutes les réclamations
  Future<List<ClaimModel>> getAllClaims() async {
    try {
      final querySnapshot = await _firestore
          .collection(claimsCollection)
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return ClaimModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération réclamations: $e');
    }
  }

  // Obtenir les réclamations par usine
  Future<List<ClaimModel>> getClaimsByFactory(String factoryId) async {
    try {
      final querySnapshot = await _firestore
          .collection(claimsCollection)
          .where('factoryId', isEqualTo: factoryId)
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return ClaimModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération réclamations par usine: $e');
    }
  }

  // Obtenir les réclamations par utilisateur
  Future<List<ClaimModel>> getClaimsByUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(claimsCollection)
          .where('reportedBy', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return ClaimModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération réclamations par utilisateur: $e');
    }
  }

  // Mettre à jour le statut d'une réclamation
  Future<void> updateClaimStatus(String claimId, ClaimStatus status, {String? comment}) async {
    try {
      final updates = {
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      };
      
      if (comment != null) {
        updates['statusComment'] = comment;
      }
      
      await _firestore.collection(claimsCollection).doc(claimId).update(updates);
    } catch (e) {
      throw Exception('Erreur mise à jour statut réclamation: $e');
    }
  }

  // ==================== FORMATIONS ====================

  // Créer une formation (validité 6 mois selon spécifications)
  Future<String> createTraining(TrainingModel training) async {
    try {
      // Forcer la validité à 6 mois selon les spécifications
      final trainingWith6Months = training.copyWith(validityMonths: 6);
      final docRef = await _firestore.collection(trainingsCollection).add(trainingWith6Months.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur création formation: $e');
    }
  }

  // Obtenir toutes les formations
  Future<List<TrainingModel>> getAllTrainings() async {
    try {
      final querySnapshot = await _firestore
          .collection(trainingsCollection)
          .orderBy('requestedAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return TrainingModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération formations: $e');
    }
  }

  // Obtenir les formations par utilisateur
  Future<List<TrainingModel>> getTrainingsByUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(trainingsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('requestedAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        return TrainingModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération formations par utilisateur: $e');
    }
  }

  // Obtenir les formations expirées ou expirant bientôt
  Future<List<TrainingModel>> getExpiringTrainings({int daysBeforeExpiry = 30}) async {
    try {
      final expiryDate = DateTime.now().add(Duration(days: daysBeforeExpiry));
      
      final querySnapshot = await _firestore
          .collection(trainingsCollection)
          .where('status', isEqualTo: 'completed')
          .where('expiresAt', isLessThanOrEqualTo: Timestamp.fromDate(expiryDate))
          .get();
      
      return querySnapshot.docs.map((doc) {
        return TrainingModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération formations expirantes: $e');
    }
  }

  // Approuver/Rejeter une formation
  Future<void> updateTrainingStatus(String trainingId, TrainingStatus status, {String? comment}) async {
    try {
      final updates = {
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      };
      
      if (status == TrainingStatus.approved) {
        updates['approvedAt'] = FieldValue.serverTimestamp();
        updates['approvedBy'] = _auth.currentUser?.uid;
      }
      
      if (status == TrainingStatus.completed) {
        updates['completedAt'] = FieldValue.serverTimestamp();
        // Calculer la date d'expiration (6 mois selon spécifications)
        final expiryDate = DateTime.now().add(const Duration(days: 180)); // 6 mois
        updates['expiresAt'] = Timestamp.fromDate(expiryDate);
      }
      
      if (comment != null) {
        updates['approvalComment'] = comment;
      }
      
      await _firestore.collection(trainingsCollection).doc(trainingId).update(updates);
    } catch (e) {
      throw Exception('Erreur mise à jour formation: $e');
    }
  }

  // ==================== USINES ====================

  // Créer une usine
  Future<String> createFactory(FactoryModel factory) async {
    try {
      final docRef = await _firestore.collection(factoriesCollection).add(factory.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur création usine: $e');
    }
  }

  // Obtenir toutes les usines
  Future<List<FactoryModel>> getAllFactories() async {
    try {
      final querySnapshot = await _firestore.collection(factoriesCollection).get();
      return querySnapshot.docs.map((doc) {
        return FactoryModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur récupération usines: $e');
    }
  }

  // ==================== STREAMS TEMPS RÉEL ====================

  // Stream des réclamations
  Stream<List<ClaimModel>> watchClaims() {
    return _firestore
        .collection(claimsCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ClaimModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    });
  }

  // Stream des formations
  Stream<List<TrainingModel>> watchTrainings() {
    return _firestore
        .collection(trainingsCollection)
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return TrainingModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    });
  }

  // Stream des réclamations par usine
  Stream<List<ClaimModel>> watchClaimsByFactory(String factoryId) {
    return _firestore
        .collection(claimsCollection)
        .where('factoryId', isEqualTo: factoryId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ClaimModel.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    });
  }
}
