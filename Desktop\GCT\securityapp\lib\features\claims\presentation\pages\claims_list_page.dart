import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/di/simple_injection.dart';
import '../bloc/claims_bloc.dart';
import '../bloc/claims_event.dart';
import '../bloc/claims_state.dart';
import '../widgets/claim_card.dart';
import '../widgets/claims_filter_bar.dart';

class ClaimsListPage extends StatelessWidget {
  const ClaimsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<ClaimsBloc>()..add(const ClaimsLoadRequested()),
      child: const ClaimsListView(),
    );
  }
}

class ClaimsListView extends StatelessWidget {
  const ClaimsListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Réclamations de Sécurité'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ClaimsBloc>().add(const ClaimsLoadRequested(refresh: true));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          const ClaimsFilterBar(),
          Expanded(
            child: BlocBuilder<ClaimsBloc, ClaimsState>(
              builder: (context, state) {
                if (state is ClaimsLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (state is ClaimsError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppTheme.errorColor,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Erreur',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            context.read<ClaimsBloc>().add(const ClaimsLoadRequested(refresh: true));
                          },
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                if (state is ClaimsLoaded) {
                  if (state.claims.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.inbox_outlined,
                            size: 64,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucune réclamation',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Aucune réclamation trouvée avec les filtres actuels.',
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () => context.go('/dashboard/claims/create'),
                            icon: const Icon(Icons.add),
                            label: const Text('Nouvelle Réclamation'),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<ClaimsBloc>().add(const ClaimsLoadRequested(refresh: true));
                    },
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: state.claims.length,
                      itemBuilder: (context, index) {
                        final claim = state.claims[index];
                        return ClaimCard(
                          claim: claim,
                          onTap: () => context.go('/dashboard/claims/${claim.id}'),
                        );
                      },
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/dashboard/claims/create'),
        backgroundColor: AppTheme.secondaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Filtrer les réclamations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status filter
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Statut',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Tous')),
                DropdownMenuItem(value: 'PENDING', child: Text('En attente')),
                DropdownMenuItem(value: 'IN_PROGRESS', child: Text('En cours')),
                DropdownMenuItem(value: 'RESOLVED', child: Text('Résolu')),
                DropdownMenuItem(value: 'CLOSED', child: Text('Fermé')),
              ],
              onChanged: (value) {
                // TODO: Implement filter logic
              },
            ),
            const SizedBox(height: 16),
            // Type filter
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Tous')),
                DropdownMenuItem(value: 'ACCIDENT', child: Text('Accident')),
                DropdownMenuItem(value: 'INCIDENT', child: Text('Incident')),
                DropdownMenuItem(value: 'RISK_BEHAVIOR', child: Text('Comportement à risque')),
                DropdownMenuItem(value: 'NEAR_MISS', child: Text('Presque accident')),
              ],
              onChanged: (value) {
                // TODO: Implement filter logic
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              // TODO: Apply filters
            },
            child: const Text('Appliquer'),
          ),
        ],
      ),
    );
  }
}
