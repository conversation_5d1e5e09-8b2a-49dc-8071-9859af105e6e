import 'package:cloud_firestore/cloud_firestore.dart';

class TrainingModel {
  final String id;
  final String title;
  final String description;
  final String category;
  final String urgency;
  final String duration;
  final String justification;
  final String status;
  final String employeeId;
  final String employeeName;
  final String employeeEmail;
  final String factory;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? approvedBy;
  final DateTime? approvedAt;
  final DateTime? scheduledAt;
  final DateTime? completedAt;
  final String? rejectionReason;

  TrainingModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.urgency,
    required this.duration,
    required this.justification,
    required this.status,
    required this.employeeId,
    required this.employeeName,
    required this.employeeEmail,
    required this.factory,
    required this.createdAt,
    required this.updatedAt,
    this.approvedBy,
    this.approvedAt,
    this.scheduledAt,
    this.completedAt,
    this.rejectionReason,
  });

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'urgency': urgency,
      'duration': duration,
      'justification': justification,
      'status': status,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'employeeEmail': employeeEmail,
      'factory': factory,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'approvedBy': approvedBy,
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
      'scheduledAt': scheduledAt != null ? Timestamp.fromDate(scheduledAt!) : null,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'rejectionReason': rejectionReason,
    };
  }

  // Create from Firestore document
  factory TrainingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return TrainingModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      category: data['category'] ?? 'Sécurité au travail',
      urgency: data['urgency'] ?? 'Normale',
      duration: data['duration'] ?? '1 jour',
      justification: data['justification'] ?? '',
      status: data['status'] ?? 'En attente',
      employeeId: data['employeeId'] ?? '',
      employeeName: data['employeeName'] ?? '',
      employeeEmail: data['employeeEmail'] ?? '',
      factory: data['factory'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      approvedBy: data['approvedBy'],
      approvedAt: (data['approvedAt'] as Timestamp?)?.toDate(),
      scheduledAt: (data['scheduledAt'] as Timestamp?)?.toDate(),
      completedAt: (data['completedAt'] as Timestamp?)?.toDate(),
      rejectionReason: data['rejectionReason'],
    );
  }

  // Create a copy with updated fields
  TrainingModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? urgency,
    String? duration,
    String? justification,
    String? status,
    String? employeeId,
    String? employeeName,
    String? employeeEmail,
    String? factory,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? approvedBy,
    DateTime? approvedAt,
    DateTime? scheduledAt,
    DateTime? completedAt,
    String? rejectionReason,
  }) {
    return TrainingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      urgency: urgency ?? this.urgency,
      duration: duration ?? this.duration,
      justification: justification ?? this.justification,
      status: status ?? this.status,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      employeeEmail: employeeEmail ?? this.employeeEmail,
      factory: factory ?? this.factory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      completedAt: completedAt ?? this.completedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  @override
  String toString() {
    return 'TrainingModel(id: $id, title: $title, status: $status, urgency: $urgency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrainingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
