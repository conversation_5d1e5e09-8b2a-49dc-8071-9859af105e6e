import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/dashboard_controller.dart';
import '../../../data/models/training_model.dart';
import '../../../core/theme/app_theme.dart';

class TrainingsView extends StatelessWidget {
  const TrainingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final DashboardController dashboardController = Get.find();

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'Gestion des Formations',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF10B981),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              // TODO: Naviguer vers la création de formation
              Get.snackbar('Info', 'Fonctionnalité en développement');
            },
          ),
        ],
      ),
      body: GetBuilder<DashboardController>(
        builder: (dashboardController) {
          if (dashboardController.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final trainings = dashboardController.trainings;

          if (trainings.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.school_outlined,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Aucune formation trouvée',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: dashboardController.refreshData,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: trainings.length,
              itemBuilder: (context, index) {
                final training = trainings[index];
                return _buildTrainingCard(training);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildTrainingCard(TrainingModel training) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header avec statut et type
          Row(
            children: [
              Expanded(
                child: Text(
                  training.title,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(training.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getStatusText(training.status),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getStatusColor(training.status),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            training.description,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 12),
          
          // Type et durée
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTypeColor(training.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getTypeText(training.type),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getTypeColor(training.type),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.access_time,
                size: 16,
                color: Colors.grey.shade500,
              ),
              const SizedBox(width: 4),
              Text(
                '${training.durationHours}h',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              const Spacer(),
              if (training.isMandatory)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    'Obligatoire',
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.red.shade700,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Informations utilisateur et usine
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16,
                color: Colors.grey.shade500,
              ),
              const SizedBox(width: 4),
              Text(
                training.userName,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.factory,
                size: 16,
                color: Colors.grey.shade500,
              ),
              const SizedBox(width: 4),
              Text(
                training.factory,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Date de demande
          Text(
            'Demandé le ${_formatDate(training.requestedAt)}',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TrainingStatus status) {
    switch (status) {
      case TrainingStatus.pending:
        return Colors.orange;
      case TrainingStatus.approved:
        return Colors.blue;
      case TrainingStatus.completed:
        return Colors.green;
      case TrainingStatus.rejected:
        return Colors.red;
      case TrainingStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText(TrainingStatus status) {
    switch (status) {
      case TrainingStatus.pending:
        return 'En attente';
      case TrainingStatus.approved:
        return 'Approuvée';
      case TrainingStatus.completed:
        return 'Terminée';
      case TrainingStatus.rejected:
        return 'Rejetée';
      case TrainingStatus.expired:
        return 'Expirée';
    }
  }

  Color _getTypeColor(TrainingType type) {
    switch (type) {
      case TrainingType.safety:
        return Colors.red;
      case TrainingType.security:
        return Colors.blue;
      case TrainingType.emergency:
        return Colors.orange;
      case TrainingType.equipment:
        return Colors.green;
      case TrainingType.procedure:
        return Colors.purple;
    }
  }

  String _getTypeText(TrainingType type) {
    switch (type) {
      case TrainingType.safety:
        return 'Sécurité';
      case TrainingType.security:
        return 'Sûreté';
      case TrainingType.emergency:
        return 'Urgence';
      case TrainingType.equipment:
        return 'Équipement';
      case TrainingType.procedure:
        return 'Procédure';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
