name: securityapp
description: "Application de sécurité GCT - Version simplifiée"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3

  # HTTP Client pour API Backend
  http: ^1.1.0
  dio: ^5.4.0

  # State Management
  get: ^4.6.6

  # UI & Design
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1

  # Local Storage
  shared_preferences: ^2.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  # Assets simplifiés
  assets:
    - assets/

  # Fonts (commenté car les fichiers n'existent pas)
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700
