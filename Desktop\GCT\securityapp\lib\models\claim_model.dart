enum ClaimType {
  accident,
  incident,
  nearMiss,
  nonCompliance,
  suggestion,
  hazard,
}

enum ClaimStatus {
  pending,
  inProgress,
  resolved,
  rejected,
  closed,
}

enum ClaimPriority {
  low,
  medium,
  high,
  critical,
}

class ClaimModel {
  final String id;
  final String title;
  final String description;
  final ClaimType type;
  final ClaimStatus status;
  final ClaimPriority priority;
  final String reportedBy;
  final String reporterName;
  final String factoryId;
  final String factoryName;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? assignedTo;
  final String? assignedToName;
  final String? location;
  final List<String> attachments;
  final String? statusComment;
  final Map<String, dynamic>? metadata;

  const ClaimModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    required this.reportedBy,
    required this.reporterName,
    required this.factoryId,
    required this.factoryName,
    required this.createdAt,
    this.updatedAt,
    this.assignedTo,
    this.assignedToName,
    this.location,
    this.attachments = const [],
    this.statusComment,
    this.metadata,
  });

  String get typeDisplayName {
    switch (type) {
      case ClaimType.accident:
        return 'Accident';
      case ClaimType.incident:
        return 'Incident';
      case ClaimType.nearMiss:
        return 'Presque-accident';
      case ClaimType.nonCompliance:
        return 'Non-conformité';
      case ClaimType.suggestion:
        return 'Suggestion';
      case ClaimType.hazard:
        return 'Danger identifié';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case ClaimStatus.pending:
        return 'En attente';
      case ClaimStatus.inProgress:
        return 'En cours';
      case ClaimStatus.resolved:
        return 'Résolue';
      case ClaimStatus.rejected:
        return 'Rejetée';
      case ClaimStatus.closed:
        return 'Fermée';
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case ClaimPriority.low:
        return 'Faible';
      case ClaimPriority.medium:
        return 'Moyenne';
      case ClaimPriority.high:
        return 'Élevée';
      case ClaimPriority.critical:
        return 'Critique';
    }
  }

  ClaimModel copyWith({
    String? id,
    String? title,
    String? description,
    ClaimType? type,
    ClaimStatus? status,
    ClaimPriority? priority,
    String? reportedBy,
    String? reporterName,
    String? factoryId,
    String? factoryName,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? assignedTo,
    String? assignedToName,
    String? location,
    List<String>? attachments,
    String? statusComment,
    Map<String, dynamic>? metadata,
  }) {
    return ClaimModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      reportedBy: reportedBy ?? this.reportedBy,
      reporterName: reporterName ?? this.reporterName,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedToName: assignedToName ?? this.assignedToName,
      location: location ?? this.location,
      attachments: attachments ?? this.attachments,
      statusComment: statusComment ?? this.statusComment,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'reportedBy': reportedBy,
      'reporterName': reporterName,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'assignedTo': assignedTo,
      'assignedToName': assignedToName,
      'location': location,
      'attachments': attachments,
      'statusComment': statusComment,
      'metadata': metadata,
    };
  }

  factory ClaimModel.fromJson(Map<String, dynamic> json) {
    return ClaimModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: ClaimType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => ClaimType.incident,
      ),
      status: ClaimStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ClaimStatus.pending,
      ),
      priority: ClaimPriority.values.firstWhere(
        (e) => e.toString().split('.').last == json['priority'],
        orElse: () => ClaimPriority.medium,
      ),
      reportedBy: json['reportedBy'] ?? '',
      reporterName: json['reporterName'] ?? '',
      factoryId: json['factoryId'] ?? '',
      factoryName: json['factoryName'] ?? '',
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : DateTime.now(),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      assignedTo: json['assignedTo'],
      assignedToName: json['assignedToName'],
      location: json['location'],
      attachments: json['attachments'] != null ? List<String>.from(json['attachments']) : [],
      statusComment: json['statusComment'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }
}
