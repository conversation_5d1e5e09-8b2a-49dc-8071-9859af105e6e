rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // RÈGLES TEMPORAIRES POUR LE DÉVELOPPEMENT - À SÉCURISER EN PRODUCTION
    match /{document=**} {
      allow read, write: if true;
    }

    // Fonction pour vérifier si l'utilisateur est authentifié
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Fonction pour obtenir les données utilisateur
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    // Fonction pour vérifier le rôle utilisateur
    function hasRole(role) {
      return isAuthenticated() && getUserData().role == role;
    }
    
    // Fonction pour vérifier si l'utilisateur appartient à une usine
    function belongsToFactory(factoryId) {
      return isAuthenticated() && getUserData().factoryId == factoryId;
    }
    
    // Fonction pour vérifier si c'est le propriétaire du document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // ==================== RÈGLES UTILISATEURS ====================
    match /users/{userId} {
      // Lecture : Utilisateur peut lire son propre profil, admins peuvent lire tous
      allow read: if isOwner(userId) 
                  || hasRole('superAdmin') 
                  || hasRole('securityAdmin')
                  || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId));
      
      // Création : Seuls les employés peuvent créer leur compte via inscription
      allow create: if isAuthenticated() 
                    && request.auth.uid == userId 
                    && request.resource.data.role == 'employee'
                    && request.resource.data.id == userId;
      
      // Mise à jour : Utilisateur peut modifier son profil, admins peuvent modifier selon permissions
      allow update: if isOwner(userId) 
                    || hasRole('superAdmin')
                    || (hasRole('securityAdmin') && resource.data.role != 'superAdmin')
                    || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId) && resource.data.role == 'employee');
      
      // Suppression : Seul le super admin peut supprimer
      allow delete: if hasRole('superAdmin');
    }
    
    // ==================== RÈGLES RÉCLAMATIONS ====================
    match /claims/{claimId} {
      // Lecture : Selon le rôle et l'usine
      allow read: if hasRole('superAdmin') 
                  || hasRole('securityAdmin')
                  || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId))
                  || (hasRole('employee') && (isOwner(resource.data.reportedBy) || belongsToFactory(resource.data.factoryId)));
      
      // Création : Tous les utilisateurs authentifiés peuvent créer des réclamations
      allow create: if isAuthenticated() 
                    && request.resource.data.reportedBy == request.auth.uid
                    && belongsToFactory(request.resource.data.factoryId);
      
      // Mise à jour : Admins peuvent modifier le statut, employés peuvent modifier leurs propres réclamations non traitées
      allow update: if hasRole('superAdmin')
                    || hasRole('securityAdmin')
                    || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId))
                    || (hasRole('employee') && isOwner(resource.data.reportedBy) && resource.data.status == 'pending');
      
      // Suppression : Seuls les super admins et admins sécurité
      allow delete: if hasRole('superAdmin') || hasRole('securityAdmin');
    }
    
    // ==================== RÈGLES FORMATIONS ====================
    match /trainings/{trainingId} {
      // Lecture : Selon le rôle et l'usine
      allow read: if hasRole('superAdmin') 
                  || hasRole('securityAdmin')
                  || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId))
                  || (hasRole('employee') && isOwner(resource.data.userId));
      
      // Création : Employés peuvent demander des formations pour eux-mêmes
      allow create: if isAuthenticated() 
                    && request.resource.data.userId == request.auth.uid
                    && belongsToFactory(request.resource.data.factoryId)
                    && request.resource.data.status == 'pending';
      
      // Mise à jour : Admins peuvent approuver/rejeter, employés peuvent modifier leurs demandes en attente
      allow update: if hasRole('superAdmin')
                    || hasRole('securityAdmin')
                    || (hasRole('factoryAdmin') && belongsToFactory(resource.data.factoryId))
                    || (hasRole('employee') && isOwner(resource.data.userId) && resource.data.status == 'pending');
      
      // Suppression : Seuls les super admins et admins sécurité
      allow delete: if hasRole('superAdmin') || hasRole('securityAdmin');
    }
    
    // ==================== RÈGLES USINES ====================
    match /factories/{factoryId} {
      // Lecture : Tous les utilisateurs authentifiés peuvent lire les usines
      allow read: if isAuthenticated();
      
      // Création : Seuls les super admins
      allow create: if hasRole('superAdmin');
      
      // Mise à jour : Super admins et admins sécurité
      allow update: if hasRole('superAdmin') || hasRole('securityAdmin');
      
      // Suppression : Seuls les super admins
      allow delete: if hasRole('superAdmin');
    }
    
    // ==================== RÈGLES STATISTIQUES ====================
    match /statistics/{document=**} {
      // Lecture : Admins seulement
      allow read: if hasRole('superAdmin') 
                  || hasRole('securityAdmin')
                  || hasRole('factoryAdmin');
      
      // Écriture : Seuls les super admins et admins sécurité
      allow write: if hasRole('superAdmin') || hasRole('securityAdmin');
    }
    
    // ==================== RÈGLES NOTIFICATIONS ====================
    match /notifications/{notificationId} {
      // Lecture : Utilisateur peut lire ses propres notifications
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      
      // Création : Système peut créer des notifications
      allow create: if isAuthenticated();
      
      // Mise à jour : Utilisateur peut marquer ses notifications comme lues
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      
      // Suppression : Utilisateur peut supprimer ses notifications
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }
    
    // ==================== RÈGLES PAR DÉFAUT ====================
    // Bloquer tout accès non explicitement autorisé
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
