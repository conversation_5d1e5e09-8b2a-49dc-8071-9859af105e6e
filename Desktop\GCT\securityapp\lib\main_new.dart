import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
import 'firebase_options.dart';
import 'app/routes/app_pages.dart';
import 'app/core/theme/app_theme.dart';
import 'app/core/bindings/initial_binding.dart';
import 'app/data/services/firestore_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(const GCTSecurityApp());
}

class GCTSecurityApp extends StatelessWidget {
  const GCTSecurityApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'GCT Security',
      debugShowCheckedModeBanner: false,
      
      // Theme
      theme: AppTheme.lightTheme,
      
      // Initial Binding
      initialBinding: InitialBinding(),
      
      // Routes
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      
      // Initialize default data
      onInit: () async {
        final firestoreService = Get.find<FirestoreService>();
        await firestoreService.initializeDefaultData();
      },
    );
  }
}
