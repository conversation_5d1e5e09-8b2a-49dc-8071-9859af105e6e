import 'package:dio/dio.dart';
// import 'package:retrofit/retrofit.dart';
import 'dart:io';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';

// TODO: Uncomment when code generation is needed and API is ready
// part 'api_client.g.dart';

// TODO: Uncomment when API is ready
// @RestApi(baseUrl: AppConstants.baseUrl)
abstract class ApiClient {
  // TODO: Uncomment when code generation is needed
  // factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;

  // Placeholder methods for future API implementation

  // TODO: Uncomment when API is ready
  /*
  // Auth endpoints
  @POST('/auth/login')
  Future<Map<String, dynamic>> login(@Body() Map<String, dynamic> credentials);

  @POST('/auth/logout')
  Future<void> logout();

  @POST('/auth/refresh')
  Future<Map<String, dynamic>> refreshToken(@Body() Map<String, dynamic> token);

  @GET('/auth/profile')
  Future<Map<String, dynamic>> getProfile();
  */

  /*
  // Claims endpoints
  @GET('/claims')
  Future<Map<String, dynamic>> getClaims({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('status') String? status,
    @Query('type') String? type,
    @Query('factory_id') String? factoryId,
  });

  @GET('/claims/{id}')
  Future<Map<String, dynamic>> getClaim(@Path('id') String id);

  @POST('/claims')
  Future<Map<String, dynamic>> createClaim(@Body() Map<String, dynamic> claim);

  @PUT('/claims/{id}')
  Future<Map<String, dynamic>> updateClaim(
    @Path('id') String id,
    @Body() Map<String, dynamic> claim,
  );

  @DELETE('/claims/{id}')
  Future<void> deleteClaim(@Path('id') String id);
  */

  /*
  // Training endpoints
  @GET('/trainings')
  Future<Map<String, dynamic>> getTrainings({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('status') String? status,
    @Query('user_id') String? userId,
  });

  @GET('/trainings/{id}')
  Future<Map<String, dynamic>> getTraining(@Path('id') String id);

  @POST('/trainings/{id}/complete')
  Future<Map<String, dynamic>> completeTraining(@Path('id') String id);

  @POST('/trainings/{id}/validate')
  Future<Map<String, dynamic>> validateTraining(
    @Path('id') String id,
    @Body() Map<String, dynamic> validation,
  );

  // Factory endpoints
  @GET('/factories')
  Future<Map<String, dynamic>> getFactories();

  @GET('/factories/{id}')
  Future<Map<String, dynamic>> getFactory(@Path('id') String id);

  @POST('/factories')
  Future<Map<String, dynamic>> createFactory(@Body() Map<String, dynamic> factory);

  @PUT('/factories/{id}')
  Future<Map<String, dynamic>> updateFactory(
    @Path('id') String id,
    @Body() Map<String, dynamic> factory,
  );

  // Dashboard endpoints
  @GET('/dashboard/stats')
  Future<Map<String, dynamic>> getDashboardStats({
    @Query('factory_id') String? factoryId,
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
  });

  @GET('/dashboard/recent-claims')
  Future<Map<String, dynamic>> getRecentClaims({
    @Query('limit') int? limit,
    @Query('factory_id') String? factoryId,
  });

  // File upload endpoints
  @POST('/files/upload')
  @MultiPart()
  Future<Map<String, dynamic>> uploadFile(@Part() File file);

  @GET('/files/{id}')
  Future<Response> downloadFile(@Path('id') String id);

  // Notification endpoints
  @GET('/notifications')
  Future<Map<String, dynamic>> getNotifications({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('read') bool? read,
  });

  @PUT('/notifications/{id}/read')
  Future<void> markNotificationAsRead(@Path('id') String id);

  @PUT('/notifications/read-all')
  Future<void> markAllNotificationsAsRead();
  */
}
