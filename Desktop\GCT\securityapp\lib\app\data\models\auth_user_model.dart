import 'package:cloud_firestore/cloud_firestore.dart';

class AuthUserModel {
  final String uid;
  final String email;
  final String password; // En production, ce serait hashé
  final String role;
  final String displayName;
  final String? factory;
  final String? department;
  final String? position;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final int loginAttempts;
  final bool isLocked;

  AuthUserModel({
    required this.uid,
    required this.email,
    required this.password,
    required this.role,
    required this.displayName,
    this.factory,
    this.department,
    this.position,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
    this.loginAttempts = 0,
    this.isLocked = false,
  });

  String get roleDisplayName {
    switch (role) {
      case 'super_admin':
        return 'Super Administrateur';
      case 'admin_securite_gct':
        return 'Admin Sécurité GCT';
      case 'admin_usine':
        return 'Admin Usine';
      case 'employe':
        return 'Employé';
      default:
        return 'Utilisateur';
    }
  }

  bool get isAdmin => ['super_admin', 'admin_securite_gct', 'admin_usine'].contains(role);
  bool get isSuperAdmin => role == 'super_admin';
  bool get isSecurityAdmin => role == 'admin_securite_gct';
  bool get isFactoryAdmin => role == 'admin_usine';
  bool get isEmployee => role == 'employe';

  bool get canLogin => isActive && !isLocked;

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'password': password,
      'role': role,
      'displayName': displayName,
      'factory': factory,
      'department': department,
      'position': position,
      'isActive': isActive,
      'createdAt': createdAt,
      'lastLogin': lastLogin,
      'loginAttempts': loginAttempts,
      'isLocked': isLocked,
    };
  }

  // Création depuis Map Firestore
  factory AuthUserModel.fromMap(Map<String, dynamic> map) {
    return AuthUserModel(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      password: map['password'] ?? '',
      role: map['role'] ?? 'employe',
      displayName: map['displayName'] ?? '',
      factory: map['factory'],
      department: map['department'],
      position: map['position'],
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLogin: (map['lastLogin'] as Timestamp?)?.toDate(),
      loginAttempts: map['loginAttempts'] ?? 0,
      isLocked: map['isLocked'] ?? false,
    );
  }

  // Création depuis DocumentSnapshot
  factory AuthUserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AuthUserModel.fromMap({...data, 'uid': doc.id});
  }

  // Copie avec modifications
  AuthUserModel copyWith({
    String? uid,
    String? email,
    String? password,
    String? role,
    String? displayName,
    String? factory,
    String? department,
    String? position,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
    int? loginAttempts,
    bool? isLocked,
  }) {
    return AuthUserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      password: password ?? this.password,
      role: role ?? this.role,
      displayName: displayName ?? this.displayName,
      factory: factory ?? this.factory,
      department: department ?? this.department,
      position: position ?? this.position,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      loginAttempts: loginAttempts ?? this.loginAttempts,
      isLocked: isLocked ?? this.isLocked,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthUserModel && other.uid == uid;
  }

  @override
  int get hashCode => uid.hashCode;

  @override
  String toString() {
    return 'AuthUserModel(uid: $uid, email: $email, role: $role, displayName: $displayName)';
  }
}
