import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/dashboard_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/claim_model.dart';
import '../../../data/models/training_model.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/stat_card.dart';
import '../widgets/claim_card.dart';
import '../widgets/training_card.dart';

class EmployeeDashboardView extends StatelessWidget {
  const EmployeeDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final DashboardController dashboardController = Get.find();
    final AuthController authController = Get.find();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.heroGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              DashboardHeader(
                title: 'Mon Espace',
                subtitle: 'Tableau de bord employé',
                user: authController.currentUser!,
                onLogout: authController.logout,
              ),
              
              // Content
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: RefreshIndicator(
                    onRefresh: dashboardController.refreshData,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          
                          // Quick Stats
                          _buildQuickStats(dashboardController),
                          
                          const SizedBox(height: 32),
                          
                          // Quick Actions
                          _buildQuickActions(),
                          
                          const SizedBox(height: 32),
                          
                          // My Claims
                          _buildMyClaimsSection(dashboardController),
                          
                          const SizedBox(height: 32),
                          
                          // My Trainings
                          _buildMyTrainingsSection(dashboardController),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats(DashboardController controller) {
    return GetBuilder<DashboardController>(
      builder: (ctrl) => Row(
        children: [
          Expanded(
            child: StatCard(
              title: 'Mes Réclamations',
              value: ctrl.totalClaims.toString(),
              icon: Icons.report_problem_rounded,
              color: AppTheme.errorColor,
              subtitle: '${ctrl.pendingClaimsCount} en attente',
              onTap: () => Get.toNamed('/claims'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: StatCard(
              title: 'Mes Formations',
              value: ctrl.totalTrainings.toString(),
              icon: Icons.school_rounded,
              color: AppTheme.successColor,
              subtitle: '${ctrl.completedTrainingsCount} terminées',
              onTap: () => Get.toNamed('/trainings'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions Rapides',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Nouvelle Réclamation',
                Icons.add_alert_rounded,
                AppTheme.errorColor,
                () => Get.toNamed('/claims/create'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                'Demander Formation',
                Icons.school_rounded,
                AppTheme.successColor,
                () => Get.toNamed('/trainings/request'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(14),
              ),
              child: Icon(
                icon,
                color: color,
                size: 26,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              title,
              textAlign: TextAlign.center,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMyClaimsSection(DashboardController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Mes Réclamations',
              style: GoogleFonts.inter(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => Get.toNamed('/claims'),
              child: Text(
                'Voir tout',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        GetBuilder<DashboardController>(
          builder: (controller) {
            final claims = controller.claims.take(3).toList();

            if (claims.isEmpty) {
              return _buildEmptyState(
                'Aucune réclamation',
                'Vous n\'avez pas encore créé de réclamation',
                Icons.report_problem_outlined,
              );
            }

            return Column(
              children: claims.map((claim) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: ClaimCard(
                  claim: claim,
                  onTap: () => Get.toNamed('/claims/details', arguments: claim),
                ),
              )).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMyTrainingsSection(DashboardController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Mes Formations',
              style: GoogleFonts.inter(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => Get.toNamed('/trainings'),
              child: Text(
                'Voir tout',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        GetBuilder<DashboardController>(
          builder: (controller) {
            final trainings = controller.trainings.take(3).toList();

            if (trainings.isEmpty) {
              return _buildEmptyState(
                'Aucune formation',
                'Vous n\'avez pas encore demandé de formation',
                Icons.school_outlined,
              );
            }

            return Column(
              children: trainings.map((training) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: TrainingCard(
                  training: training,
                  onTap: () => Get.toNamed('/trainings/details', arguments: training),
                ),
              )).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.textSecondaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: AppTheme.textSecondaryColor.withOpacity(0.5),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            title,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: AppTheme.textSecondaryColor.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
}
