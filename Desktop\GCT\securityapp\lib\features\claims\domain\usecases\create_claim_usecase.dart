import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/claim.dart';
import '../repositories/claims_repository.dart';

class CreateClaimUseCase {
  final ClaimsRepository repository;

  CreateClaimUseCase(this.repository);

  Future<Either<Failure, Claim>> call(CreateClaimParams params) async {
    final claim = Claim(
      id: '', // Will be generated by Firebase
      title: params.title,
      description: params.description,
      type: params.type,
      status: 'PENDING', // Default status
      priority: params.priority,
      reporterId: params.reporterId,
      reporterName: params.reporterName,
      factoryId: params.factoryId,
      factoryName: params.factoryName,
      location: params.location,
      latitude: params.latitude,
      longitude: params.longitude,
      incidentDate: params.incidentDate,
      reportedAt: DateTime.now(),
      attachments: params.attachments ?? [],
      comments: [],
      metadata: params.metadata,
    );

    return await repository.createClaim(claim);
  }
}

class CreateClaimParams {
  final String title;
  final String description;
  final String type;
  final String priority;
  final String reporterId;
  final String reporterName;
  final String factoryId;
  final String factoryName;
  final String location;
  final double? latitude;
  final double? longitude;
  final DateTime incidentDate;
  final List<String>? attachments;
  final Map<String, dynamic>? metadata;

  CreateClaimParams({
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    required this.reporterId,
    required this.reporterName,
    required this.factoryId,
    required this.factoryName,
    required this.location,
    this.latitude,
    this.longitude,
    required this.incidentDate,
    this.attachments,
    this.metadata,
  });
}
