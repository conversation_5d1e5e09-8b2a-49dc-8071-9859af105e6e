import 'package:equatable/equatable.dart';
import '../../domain/entities/claim.dart';

abstract class ClaimsState extends Equatable {
  const ClaimsState();

  @override
  List<Object?> get props => [];
}

class ClaimsInitial extends ClaimsState {
  const ClaimsInitial();
}

class ClaimsLoading extends ClaimsState {
  const ClaimsLoading();
}

class ClaimsLoaded extends ClaimsState {
  final List<Claim> claims;
  final Map<String, int>? stats;
  final bool hasReachedMax;
  final String? currentFactoryId;
  final String? currentStatus;
  final String? currentType;
  final String? currentAssignedToId;

  const ClaimsLoaded({
    required this.claims,
    this.stats,
    this.hasReachedMax = false,
    this.currentFactoryId,
    this.currentStatus,
    this.currentType,
    this.currentAssignedToId,
  });

  @override
  List<Object?> get props => [
        claims,
        stats,
        hasReachedMax,
        currentFactoryId,
        currentStatus,
        currentType,
        currentAssignedToId,
      ];

  ClaimsLoaded copyWith({
    List<Claim>? claims,
    Map<String, int>? stats,
    bool? hasReachedMax,
    String? currentFactoryId,
    String? currentStatus,
    String? currentType,
    String? currentAssignedToId,
  }) {
    return ClaimsLoaded(
      claims: claims ?? this.claims,
      stats: stats ?? this.stats,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentFactoryId: currentFactoryId ?? this.currentFactoryId,
      currentStatus: currentStatus ?? this.currentStatus,
      currentType: currentType ?? this.currentType,
      currentAssignedToId: currentAssignedToId ?? this.currentAssignedToId,
    );
  }
}

class ClaimsError extends ClaimsState {
  final String message;
  final String? errorCode;

  const ClaimsError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class ClaimCreating extends ClaimsState {
  const ClaimCreating();
}

class ClaimCreated extends ClaimsState {
  final Claim claim;

  const ClaimCreated({required this.claim});

  @override
  List<Object?> get props => [claim];
}

class ClaimUpdating extends ClaimsState {
  final String claimId;

  const ClaimUpdating({required this.claimId});

  @override
  List<Object?> get props => [claimId];
}

class ClaimUpdated extends ClaimsState {
  final Claim claim;

  const ClaimUpdated({required this.claim});

  @override
  List<Object?> get props => [claim];
}

class ClaimDeleting extends ClaimsState {
  final String claimId;

  const ClaimDeleting({required this.claimId});

  @override
  List<Object?> get props => [claimId];
}

class ClaimDeleted extends ClaimsState {
  final String claimId;

  const ClaimDeleted({required this.claimId});

  @override
  List<Object?> get props => [claimId];
}

class ClaimCommentAdding extends ClaimsState {
  final String claimId;

  const ClaimCommentAdding({required this.claimId});

  @override
  List<Object?> get props => [claimId];
}

class ClaimCommentAdded extends ClaimsState {
  final Claim claim;

  const ClaimCommentAdded({required this.claim});

  @override
  List<Object?> get props => [claim];
}
