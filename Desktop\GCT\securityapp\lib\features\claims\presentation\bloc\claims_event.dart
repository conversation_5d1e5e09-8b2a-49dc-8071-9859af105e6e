import 'package:equatable/equatable.dart';
import '../../domain/entities/claim.dart';

abstract class ClaimsEvent extends Equatable {
  const ClaimsEvent();

  @override
  List<Object?> get props => [];
}

class ClaimsLoadRequested extends ClaimsEvent {
  final String? factoryId;
  final String? status;
  final String? type;
  final String? assignedToId;
  final bool refresh;

  const ClaimsLoadRequested({
    this.factoryId,
    this.status,
    this.type,
    this.assignedToId,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [factoryId, status, type, assignedToId, refresh];
}

class ClaimCreateRequested extends ClaimsEvent {
  final String title;
  final String description;
  final String type;
  final String priority;
  final String factoryId;
  final String factoryName;
  final String location;
  final double? latitude;
  final double? longitude;
  final DateTime incidentDate;
  final List<String>? attachments;

  const ClaimCreateRequested({
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    required this.factoryId,
    required this.factoryName,
    required this.location,
    this.latitude,
    this.longitude,
    required this.incidentDate,
    this.attachments,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        type,
        priority,
        factoryId,
        factoryName,
        location,
        latitude,
        longitude,
        incidentDate,
        attachments,
      ];
}

class ClaimUpdateRequested extends ClaimsEvent {
  final Claim claim;

  const ClaimUpdateRequested({required this.claim});

  @override
  List<Object?> get props => [claim];
}

class ClaimDeleteRequested extends ClaimsEvent {
  final String claimId;

  const ClaimDeleteRequested({required this.claimId});

  @override
  List<Object?> get props => [claimId];
}

class ClaimStatusUpdateRequested extends ClaimsEvent {
  final String claimId;
  final String newStatus;
  final String? assignedToId;
  final String? assignedToName;

  const ClaimStatusUpdateRequested({
    required this.claimId,
    required this.newStatus,
    this.assignedToId,
    this.assignedToName,
  });

  @override
  List<Object?> get props => [claimId, newStatus, assignedToId, assignedToName];
}

class ClaimCommentAddRequested extends ClaimsEvent {
  final String claimId;
  final String content;
  final String authorId;
  final String authorName;
  final List<String>? attachments;

  const ClaimCommentAddRequested({
    required this.claimId,
    required this.content,
    required this.authorId,
    required this.authorName,
    this.attachments,
  });

  @override
  List<Object?> get props => [claimId, content, authorId, authorName, attachments];
}

class ClaimsFilterChanged extends ClaimsEvent {
  final String? factoryId;
  final String? status;
  final String? type;
  final String? assignedToId;

  const ClaimsFilterChanged({
    this.factoryId,
    this.status,
    this.type,
    this.assignedToId,
  });

  @override
  List<Object?> get props => [factoryId, status, type, assignedToId];
}

class ClaimsSearchRequested extends ClaimsEvent {
  final String query;

  const ClaimsSearchRequested({required this.query});

  @override
  List<Object?> get props => [query];
}

class ClaimsStatsRequested extends ClaimsEvent {
  final String? factoryId;

  const ClaimsStatsRequested({this.factoryId});

  @override
  List<Object?> get props => [factoryId];
}
