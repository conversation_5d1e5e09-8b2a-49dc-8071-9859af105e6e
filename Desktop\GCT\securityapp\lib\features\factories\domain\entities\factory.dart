import 'package:equatable/equatable.dart';

class Factory extends Equatable {
  final String id;
  final String name;
  final String code;
  final String description;
  final String address;
  final String city;
  final String region;
  final String country;
  final String postalCode;
  final double latitude;
  final double longitude;
  final String? phoneNumber;
  final String? email;
  final String? website;
  final String managerId;
  final String managerName;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final FactoryStats? stats;
  final Map<String, dynamic>? metadata;

  const Factory({
    required this.id,
    required this.name,
    required this.code,
    required this.description,
    required this.address,
    required this.city,
    required this.region,
    required this.country,
    required this.postalCode,
    required this.latitude,
    required this.longitude,
    this.phoneNumber,
    this.email,
    this.website,
    required this.managerId,
    required this.managerName,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.stats,
    this.metadata,
  });

  String get fullAddress => '$address, $city, $region $postalCode, $country';
  
  bool get hasContact => phoneNumber != null || email != null;
  bool get hasWebsite => website != null;
  bool get hasStats => stats != null;

  Factory copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    String? address,
    String? city,
    String? region,
    String? country,
    String? postalCode,
    double? latitude,
    double? longitude,
    String? phoneNumber,
    String? email,
    String? website,
    String? managerId,
    String? managerName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    FactoryStats? stats,
    Map<String, dynamic>? metadata,
  }) {
    return Factory(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      region: region ?? this.region,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      website: website ?? this.website,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      stats: stats ?? this.stats,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        code,
        description,
        address,
        city,
        region,
        country,
        postalCode,
        latitude,
        longitude,
        phoneNumber,
        email,
        website,
        managerId,
        managerName,
        isActive,
        createdAt,
        updatedAt,
        stats,
        metadata,
      ];
}

class FactoryStats extends Equatable {
  final int totalEmployees;
  final int totalClaims;
  final int pendingClaims;
  final int resolvedClaims;
  final int totalTrainings;
  final int completedTrainings;
  final int expiredTrainings;
  final double safetyScore;
  final DateTime lastIncident;
  final int daysSinceLastIncident;
  final Map<String, int> claimsByType;
  final Map<String, int> claimsByPriority;
  final Map<String, int> trainingsByStatus;

  const FactoryStats({
    required this.totalEmployees,
    required this.totalClaims,
    required this.pendingClaims,
    required this.resolvedClaims,
    required this.totalTrainings,
    required this.completedTrainings,
    required this.expiredTrainings,
    required this.safetyScore,
    required this.lastIncident,
    required this.daysSinceLastIncident,
    this.claimsByType = const {},
    this.claimsByPriority = const {},
    this.trainingsByStatus = const {},
  });

  double get claimsResolutionRate {
    if (totalClaims == 0) return 0.0;
    return (resolvedClaims / totalClaims) * 100;
  }

  double get trainingCompletionRate {
    if (totalTrainings == 0) return 0.0;
    return (completedTrainings / totalTrainings) * 100;
  }

  bool get hasRecentIncidents => daysSinceLastIncident < 30;
  bool get isHighRisk => safetyScore < 60;
  bool get isLowRisk => safetyScore >= 80;

  FactoryStats copyWith({
    int? totalEmployees,
    int? totalClaims,
    int? pendingClaims,
    int? resolvedClaims,
    int? totalTrainings,
    int? completedTrainings,
    int? expiredTrainings,
    double? safetyScore,
    DateTime? lastIncident,
    int? daysSinceLastIncident,
    Map<String, int>? claimsByType,
    Map<String, int>? claimsByPriority,
    Map<String, int>? trainingsByStatus,
  }) {
    return FactoryStats(
      totalEmployees: totalEmployees ?? this.totalEmployees,
      totalClaims: totalClaims ?? this.totalClaims,
      pendingClaims: pendingClaims ?? this.pendingClaims,
      resolvedClaims: resolvedClaims ?? this.resolvedClaims,
      totalTrainings: totalTrainings ?? this.totalTrainings,
      completedTrainings: completedTrainings ?? this.completedTrainings,
      expiredTrainings: expiredTrainings ?? this.expiredTrainings,
      safetyScore: safetyScore ?? this.safetyScore,
      lastIncident: lastIncident ?? this.lastIncident,
      daysSinceLastIncident: daysSinceLastIncident ?? this.daysSinceLastIncident,
      claimsByType: claimsByType ?? this.claimsByType,
      claimsByPriority: claimsByPriority ?? this.claimsByPriority,
      trainingsByStatus: trainingsByStatus ?? this.trainingsByStatus,
    );
  }

  @override
  List<Object?> get props => [
        totalEmployees,
        totalClaims,
        pendingClaims,
        resolvedClaims,
        totalTrainings,
        completedTrainings,
        expiredTrainings,
        safetyScore,
        lastIncident,
        daysSinceLastIncident,
        claimsByType,
        claimsByPriority,
        trainingsByStatus,
      ];
}
