import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../controllers/dashboard_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/claim_model.dart';
import '../../../data/models/training_model.dart';
import '../../../data/models/user_model.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/stat_card.dart';

class FactoryAdminDashboardView extends StatelessWidget {
  const FactoryAdminDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final DashboardController dashboardController = Get.find();
    final AuthController authController = Get.find();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.heroGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              DashboardHeader(
                title: 'Admin Usine',
                subtitle: 'Gestion ${authController.currentUser?.factory ?? "usine"}',
                user: authController.currentUser!,
                onLogout: authController.logout,
              ),
              
              // Content
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: RefreshIndicator(
                    onRefresh: dashboardController.refreshData,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          
                          // Factory Stats
                          Obx(() => GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              childAspectRatio: 2.2,
                              children: [
                                StatCard(
                                  title: 'Réclamations Usine',
                                  value: dashboardController.totalClaims.toString(),
                                  icon: Icons.report_problem_rounded,
                                  color: AppTheme.errorColor,
                                  subtitle: '${dashboardController.pendingClaimsCount} en attente',
                                  onTap: () => Get.toNamed('/claims'),
                                ),
                                StatCard(
                                  title: 'Employés',
                                  value: dashboardController.totalUsers.toString(),
                                  icon: Icons.people_rounded,
                                  color: AppTheme.infoColor,
                                  subtitle: 'Dans cette usine',
                                  onTap: () => Get.toNamed('/users'),
                                ),
                                StatCard(
                                  title: 'Formations',
                                  value: dashboardController.totalTrainings.toString(),
                                  icon: Icons.school_rounded,
                                  color: AppTheme.successColor,
                                  subtitle: '${dashboardController.pendingTrainingsCount} en attente',
                                  onTap: () => Get.toNamed('/trainings'),
                                ),
                                StatCard(
                                  title: 'Prioritaires',
                                  value: dashboardController.highPriorityClaims.length.toString(),
                                  icon: Icons.priority_high_rounded,
                                  color: AppTheme.warningColor,
                                  subtitle: 'Réclamations urgentes',
                                  onTap: () => Get.toNamed('/claims'),
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 32),

                          // Réclamations Récentes
                          _buildSectionHeader('Réclamations Récentes - ${authController.currentUser?.factory ?? "Usine"}'),
                          const SizedBox(height: 16),
                          Obx(() => _buildRecentClaimsSection(dashboardController)),

                          const SizedBox(height: 32),

                          // Employés de l'Usine
                          _buildSectionHeader('Employés de l\'Usine'),
                          const SizedBox(height: 16),
                          Obx(() => _buildEmployeesSection(dashboardController)),

                          const SizedBox(height: 32),

                          // Formations en Cours
                          _buildSectionHeader('Formations en Cours'),
                          const SizedBox(height: 16),
                          Obx(() => _buildTrainingsSection(dashboardController)),

                          const SizedBox(height: 32),

                          // Quick Actions
                          _buildSectionHeader('Actions Rapides'),
                          
                          const SizedBox(height: 20),
                          
                          GridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 1.3,
                            children: [
                              _buildActionCard(
                                'Réclamations',
                                Icons.report_problem_rounded,
                                AppTheme.errorColor,
                                () => Get.toNamed('/claims'),
                              ),
                              _buildActionCard(
                                'Formations',
                                Icons.school_rounded,
                                AppTheme.successColor,
                                () => Get.toNamed('/trainings'),
                              ),
                              _buildActionCard(
                                'Employés',
                                Icons.people_rounded,
                                AppTheme.infoColor,
                                () => Get.toNamed('/users'),
                              ),
                              _buildActionCard(
                                'Rapports',
                                Icons.analytics_rounded,
                                AppTheme.accentColor,
                                () => Get.toNamed('/reports'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),

            const SizedBox(height: 8),

            Flexible(
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w700,
        color: AppTheme.textPrimaryColor,
      ),
    );
  }

  Widget _buildRecentClaimsSection(DashboardController controller) {
    if (controller.claims.isEmpty) {
      return _buildEmptyState('Aucune réclamation récente', Icons.report_problem_outlined);
    }

    final recentClaims = controller.claims.take(5).toList();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: recentClaims.map((claim) => _buildClaimItem(claim)).toList(),
      ),
    );
  }

  Widget _buildClaimItem(ClaimModel claim) {
    Color statusColor;
    String statusText;

    switch (claim.status) {
      case ClaimStatus.pending:
        statusColor = AppTheme.warningColor;
        statusText = 'En attente';
        break;
      case ClaimStatus.inProgress:
        statusColor = AppTheme.infoColor;
        statusText = 'En cours';
        break;
      case ClaimStatus.resolved:
        statusColor = AppTheme.successColor;
        statusText = 'Résolue';
        break;
      default:
        statusColor = AppTheme.errorColor;
        statusText = 'Fermée';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 40,
            decoration: BoxDecoration(
              color: _getPriorityColor(claim.priority),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  claim.title,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'Par: ${claim.userName}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(claim.createdAt),
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              statusText,
              style: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeesSection(DashboardController controller) {
    if (controller.users.isEmpty) {
      return _buildEmptyState('Aucun employé trouvé', Icons.people_outlined);
    }

    final employees = controller.users.take(5).toList();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: employees.map((user) => _buildEmployeeItem(user)).toList(),
      ),
    );
  }

  Widget _buildEmployeeItem(UserModel user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
            child: Text(
              user.firstName[0].toUpperCase(),
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user.email,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: user.isActive ? AppTheme.successColor.withOpacity(0.1) : AppTheme.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              user.isActive ? 'Actif' : 'Inactif',
              style: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: user.isActive ? AppTheme.successColor : AppTheme.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainingsSection(DashboardController controller) {
    if (controller.trainings.isEmpty) {
      return _buildEmptyState('Aucune formation en cours', Icons.school_outlined);
    }

    final recentTrainings = controller.trainings.take(5).toList();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: recentTrainings.map((training) => _buildTrainingItem(training)).toList(),
      ),
    );
  }

  Widget _buildTrainingItem(TrainingModel training) {
    Color statusColor;
    String statusText;

    switch (training.status) {
      case TrainingStatus.pending:
        statusColor = AppTheme.warningColor;
        statusText = 'En attente';
        break;
      case TrainingStatus.approved:
        statusColor = AppTheme.infoColor;
        statusText = 'Approuvée';
        break;
      case TrainingStatus.completed:
        statusColor = AppTheme.successColor;
        statusText = 'Terminée';
        break;
      default:
        statusColor = AppTheme.errorColor;
        statusText = 'Rejetée';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getTrainingTypeColor(training.type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getTrainingTypeIcon(training.type),
              color: _getTrainingTypeColor(training.type),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  training.title,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'Par: ${training.userName}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${training.durationHours}h - ${_formatDate(training.requestedAt)}',
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  statusText,
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: statusColor,
                  ),
                ),
              ),
              if (training.isMandatory) ...[
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Obligatoire',
                    style: GoogleFonts.inter(
                      fontSize: 9,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.errorColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: AppTheme.textSecondaryColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(ClaimPriority priority) {
    switch (priority) {
      case ClaimPriority.low:
        return AppTheme.successColor;
      case ClaimPriority.medium:
        return AppTheme.warningColor;
      case ClaimPriority.high:
        return AppTheme.errorColor;
      case ClaimPriority.critical:
        return const Color(0xFF8B0000);
    }
  }

  Color _getTrainingTypeColor(TrainingType type) {
    switch (type) {
      case TrainingType.safety:
        return AppTheme.errorColor;
      case TrainingType.security:
        return AppTheme.warningColor;
      case TrainingType.emergency:
        return const Color(0xFFFF4444);
      case TrainingType.equipment:
        return AppTheme.infoColor;
      case TrainingType.procedure:
        return AppTheme.successColor;
    }
  }

  IconData _getTrainingTypeIcon(TrainingType type) {
    switch (type) {
      case TrainingType.safety:
        return Icons.security_rounded;
      case TrainingType.security:
        return Icons.shield_rounded;
      case TrainingType.emergency:
        return Icons.emergency_rounded;
      case TrainingType.equipment:
        return Icons.build_rounded;
      case TrainingType.procedure:
        return Icons.list_alt_rounded;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'Il y a ${difference.inMinutes} min';
      }
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
