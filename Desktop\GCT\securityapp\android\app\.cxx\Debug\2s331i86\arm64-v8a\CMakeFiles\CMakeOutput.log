The target system is: Android - 1 - aarch64
The host system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;
Id flags: -c;--target=aarch64-none-linux-android23 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;;
Id flags: -c;--target=aarch64-none-linux-android23 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):C:\Mobile\Android_SDK\Android_SDK\cmake\3.22.1\bin\ninja.exe cmTC_28b59 && [1/2] Building C object CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: aarch64-none-linux-android23

Thread model: posix

InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android23 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -fdebug-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -resource-dir "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_28b59.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -ferror-limit 19 -femulated-tls -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o -x c C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c

clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include

 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_28b59

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: aarch64-none-linux-android23

Thread model: posix

InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=gnu --eh-frame-hdr -m aarch64linux -pie -dynamic-linker /system/bin/linker64 -o cmTC_28b59 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o "-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64" -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23 -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:\Mobile\Android_SDK\Android_SDK\cmake\3.22.1\bin\ninja.exe cmTC_28b59 && [1/2] Building C object CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: aarch64-none-linux-android23]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android23 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -fdebug-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -resource-dir "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_28b59.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -ferror-limit 19 -femulated-tls -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o -x c C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include]
  ignore line: [ C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_28b59]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: aarch64-none-linux-android23]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=gnu --eh-frame-hdr -m aarch64linux -pie -dynamic-linker /system/bin/linker64 -o cmTC_28b59 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o "-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64" -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23 -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-pie] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_28b59] ==> ignore
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o] ==> obj [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o]
    arg [-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64] ==> dir [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_28b59.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o] ==> obj [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
  remove lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/aarch64]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
  implicit dirs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/aarch64;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):C:\Mobile\Android_SDK\Android_SDK\cmake\3.22.1\bin\ninja.exe cmTC_5c892 && [1/2] Building CXX object CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: aarch64-none-linux-android23

Thread model: posix

InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android23 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -fdebug-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -resource-dir "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_5c892.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -ferror-limit 19 -femulated-tls -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -x c++ C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp

clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1

 C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include

 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_5c892

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: aarch64-none-linux-android23

Thread model: posix

InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=gnu --eh-frame-hdr -m aarch64linux -pie -dynamic-linker /system/bin/linker64 -o cmTC_5c892 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o "-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64" -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23 -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:\Mobile\Android_SDK\Android_SDK\cmake\3.22.1\bin\ninja.exe cmTC_5c892 && [1/2] Building CXX object CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: aarch64-none-linux-android23]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android23 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -fdebug-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=C:/Users/<USER>/Desktop/GCT/securityapp/android/app/.cxx/Debug/2s331i86/arm64-v8a/CMakeFiles/CMakeTmp -resource-dir "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_5c892.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -ferror-limit 19 -femulated-tls -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -x c++ C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include]
  ignore line: [ C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_5c892]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: aarch64-none-linux-android23]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=gnu --eh-frame-hdr -m aarch64linux -pie -dynamic-linker /system/bin/linker64 -o cmTC_5c892 C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o "-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64" -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23 -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-pie] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_5c892] ==> ignore
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o] ==> obj [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o]
    arg [-LC:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64] ==> dir [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LC:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_5c892.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-Bstatic] ==> search static
    arg [-lc++] ==> lib [c++]
    arg [-Bdynamic] ==> search dynamic
    arg [-lm] ==> lib [m]
    arg [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o] ==> obj [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
  remove lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/aarch64] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/aarch64]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtbegin_dynamic.o;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23/crtend_android.o]
  implicit dirs: [C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/aarch64;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/23;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;C:/Mobile/Android_SDK/Android_SDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


