import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/claim_model.dart';

class ClaimCard extends StatelessWidget {
  final ClaimModel claim;
  final VoidCallback? onTap;

  const ClaimCard({
    super.key,
    required this.claim,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getStatusColor().withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and priority
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatusChip(),
                _buildPriorityChip(),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Title
            Text(
              claim.title,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 8),
            
            // Description
            Text(
              claim.description,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 16),
            
            // Footer with date and type
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      _getTypeIcon(),
                      size: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      claim.typeDisplayName,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(claim.createdAt),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    final color = _getStatusColor();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        claim.statusDisplayName,
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildPriorityChip() {
    final color = _getPriorityColor();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.priority_high_rounded,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            claim.priorityDisplayName,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (claim.status) {
      case ClaimStatus.pending:
        return AppTheme.warningColor;
      case ClaimStatus.inProgress:
        return AppTheme.infoColor;
      case ClaimStatus.resolved:
        return AppTheme.successColor;
      case ClaimStatus.rejected:
        return AppTheme.errorColor;
      case ClaimStatus.closed:
        return AppTheme.textSecondaryColor;
    }
  }

  Color _getPriorityColor() {
    switch (claim.priority) {
      case ClaimPriority.low:
        return AppTheme.successColor;
      case ClaimPriority.medium:
        return AppTheme.warningColor;
      case ClaimPriority.high:
        return AppTheme.errorColor;
      case ClaimPriority.critical:
        return const Color(0xFF8B0000); // Dark red
    }
  }

  IconData _getTypeIcon() {
    switch (claim.type) {
      case ClaimType.accident:
        return Icons.local_hospital_rounded;
      case ClaimType.incident:
        return Icons.warning_rounded;
      case ClaimType.nearMiss:
        return Icons.near_me_rounded;
      case ClaimType.nonCompliance:
        return Icons.rule_rounded;
      case ClaimType.suggestion:
        return Icons.lightbulb_rounded;
      case ClaimType.hazard:
        return Icons.dangerous_rounded;
    }
  }
}
