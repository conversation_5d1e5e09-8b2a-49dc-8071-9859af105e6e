import '../constants/app_constants.dart';

class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'L\'email est requis';
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Veuillez entrer un email valide';
    }
    
    return null;
  }

  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Le mot de passe est requis';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'Le mot de passe doit contenir au moins ${AppConstants.minPasswordLength} caractères';
    }
    
    if (value.length > AppConstants.maxPasswordLength) {
      return 'Le mot de passe ne peut pas dépasser ${AppConstants.maxPasswordLength} caractères';
    }
    
    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins une lettre majuscule';
    }
    
    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins une lettre minuscule';
    }
    
    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins un chiffre';
    }
    
    return null;
  }

  // Confirm password validation
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'La confirmation du mot de passe est requise';
    }
    
    if (value != password) {
      return 'Les mots de passe ne correspondent pas';
    }
    
    return null;
  }

  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName est requis';
    }
    return null;
  }

  // Phone number validation
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Le numéro de téléphone est requis';
    }
    
    // Remove spaces and special characters
    final cleanedValue = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Check for Tunisian phone number format
    final phoneRegex = RegExp(r'^(\+216)?[2-9]\d{7}$');
    if (!phoneRegex.hasMatch(cleanedValue)) {
      return 'Veuillez entrer un numéro de téléphone valide';
    }
    
    return null;
  }

  // Title validation
  static String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Le titre est requis';
    }
    
    if (value.length > AppConstants.maxTitleLength) {
      return 'Le titre ne peut pas dépasser ${AppConstants.maxTitleLength} caractères';
    }
    
    return null;
  }

  // Description validation
  static String? validateDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'La description est requise';
    }
    
    if (value.length > AppConstants.maxDescriptionLength) {
      return 'La description ne peut pas dépasser ${AppConstants.maxDescriptionLength} caractères';
    }
    
    return null;
  }

  // Date validation
  static String? validateDate(DateTime? value) {
    if (value == null) {
      return 'La date est requise';
    }
    
    // Check if date is not in the future (for incident reports)
    if (value.isAfter(DateTime.now())) {
      return 'La date ne peut pas être dans le futur';
    }
    
    // Check if date is not too old (more than 1 year)
    final oneYearAgo = DateTime.now().subtract(const Duration(days: 365));
    if (value.isBefore(oneYearAgo)) {
      return 'La date ne peut pas être antérieure à un an';
    }
    
    return null;
  }

  // File size validation
  static String? validateFileSize(int fileSizeInBytes) {
    if (fileSizeInBytes > AppConstants.maxFileSize) {
      final maxSizeInMB = AppConstants.maxFileSize / (1024 * 1024);
      return 'La taille du fichier ne peut pas dépasser ${maxSizeInMB.toStringAsFixed(1)} MB';
    }
    return null;
  }

  // File type validation
  static String? validateFileType(String fileName, List<String> allowedTypes) {
    final extension = fileName.split('.').last.toLowerCase();
    if (!allowedTypes.contains(extension)) {
      return 'Type de fichier non autorisé. Types autorisés: ${allowedTypes.join(', ')}';
    }
    return null;
  }

  // Priority validation
  static String? validatePriority(String? value) {
    if (value == null || value.isEmpty) {
      return 'La priorité est requise';
    }
    
    const validPriorities = [
      AppConstants.lowPriority,
      AppConstants.mediumPriority,
      AppConstants.highPriority,
      AppConstants.criticalPriority,
    ];
    
    if (!validPriorities.contains(value.toUpperCase())) {
      return 'Priorité invalide';
    }
    
    return null;
  }

  // Claim type validation
  static String? validateClaimType(String? value) {
    if (value == null || value.isEmpty) {
      return 'Le type de réclamation est requis';
    }
    
    const validTypes = [
      AppConstants.accidentType,
      AppConstants.incidentType,
      AppConstants.riskBehaviorType,
      AppConstants.nearMissType,
    ];
    
    if (!validTypes.contains(value.toUpperCase())) {
      return 'Type de réclamation invalide';
    }
    
    return null;
  }

  // Location validation
  static String? validateLocation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'La localisation est requise';
    }
    
    if (value.length < 3) {
      return 'La localisation doit contenir au moins 3 caractères';
    }
    
    return null;
  }

  // Numeric validation
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName est requis';
    }
    
    if (double.tryParse(value) == null) {
      return '$fieldName doit être un nombre valide';
    }
    
    return null;
  }

  // Positive number validation
  static String? validatePositiveNumber(String? value, String fieldName) {
    final numericValidation = validateNumeric(value, fieldName);
    if (numericValidation != null) return numericValidation;
    
    final number = double.parse(value!);
    if (number <= 0) {
      return '$fieldName doit être un nombre positif';
    }
    
    return null;
  }
}
