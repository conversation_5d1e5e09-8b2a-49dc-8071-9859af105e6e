import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../firebase_options.dart';
import '../errors/exceptions.dart';

/// Service pour initialiser et configurer Firebase pour GCT Security
class FirebaseSetupService {
  static bool _isInitialized = false;
  
  /// Initialise Firebase avec la configuration du projet GCT
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialiser Firebase avec les options du projet
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      
      // Configurer Firestore
      await _configureFirestore();
      
      // Configurer Firebase Auth
      await _configureAuth();
      
      // Configurer Firebase Storage
      await _configureStorage();
      
      _isInitialized = true;
      print('✅ Firebase initialisé avec succès pour le projet securityapp-8b742');
      
    } catch (e) {
      print('❌ Erreur lors de l\'initialisation Firebase: $e');
      throw ServerException(message: 'Erreur d\'initialisation Firebase: $e');
    }
  }
  
  /// Configure Firestore avec les paramètres optimaux
  static Future<void> _configureFirestore() async {
    final firestore = FirebaseFirestore.instance;
    
    // Paramètres de performance
    firestore.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
    
    // Activer le réseau
    await firestore.enableNetwork();
    
    print('✅ Firestore configuré');
  }
  
  /// Configure Firebase Auth
  static Future<void> _configureAuth() async {
    final auth = FirebaseAuth.instance;
    
    // Configurer la persistance de l'authentification
    await auth.setPersistence(Persistence.LOCAL);
    
    print('✅ Firebase Auth configuré');
  }
  
  /// Configure Firebase Storage
  static Future<void> _configureStorage() async {
    final storage = FirebaseStorage.instance;
    
    // Configuration du storage (si nécessaire)
    print('✅ Firebase Storage configuré');
  }
  
  /// Crée les collections de base si elles n'existent pas
  static Future<void> createBaseCollections() async {
    if (!_isInitialized) {
      throw const ServerException(message: 'Firebase non initialisé');
    }
    
    final firestore = FirebaseFirestore.instance;
    
    try {
      // Créer les collections de base avec des documents d'exemple
      await _createFactoriesCollection(firestore);
      await _createUsersCollection(firestore);
      await _createTrainingsCollection(firestore);
      
      print('✅ Collections de base créées');
      
    } catch (e) {
      print('❌ Erreur lors de la création des collections: $e');
      throw ServerException(message: 'Erreur de création des collections: $e');
    }
  }
  
  /// Crée la collection des usines GCT
  static Future<void> _createFactoriesCollection(FirebaseFirestore firestore) async {
    final factoriesRef = firestore.collection('factories');
    
    // Vérifier si des usines existent déjà
    final snapshot = await factoriesRef.limit(1).get();
    if (snapshot.docs.isNotEmpty) {
      print('ℹ️ Collection factories existe déjà');
      return;
    }
    
    // Créer les usines GCT
    final factories = [
      {
        'id': 'gct-sfax-001',
        'name': 'Usine Sfax',
        'code': 'GCT-SFX',
        'description': 'Usine principale de production chimique à Sfax',
        'address': 'Zone Industrielle Sfax, Route de Tunis Km 4',
        'city': 'Sfax',
        'region': 'Sfax',
        'country': 'Tunisie',
        'postalCode': '3000',
        'latitude': 34.7406,
        'longitude': 10.7603,
        'phoneNumber': '+216 74 123 456',
        'email': '<EMAIL>',
        'managerId': null,
        'managerName': 'Ahmed Ben Ali',
        'isActive': true,
        'employeeCount': 150,
        'departments': ['Production', 'Maintenance', 'Qualité', 'Sécurité'],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'gct-tunis-001',
        'name': 'Centre R&D Tunis',
        'code': 'GCT-TUN',
        'description': 'Centre de recherche et développement à Tunis',
        'address': 'Technopole El Ghazala, Ariana',
        'city': 'Ariana',
        'region': 'Tunis',
        'country': 'Tunisie',
        'postalCode': '2083',
        'latitude': 36.8983,
        'longitude': 10.1894,
        'phoneNumber': '+216 71 123 456',
        'email': '<EMAIL>',
        'managerId': null,
        'managerName': 'Fatma Trabelsi',
        'isActive': true,
        'employeeCount': 80,
        'departments': ['R&D', 'Innovation', 'Tests', 'Sécurité'],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'gct-gabes-001',
        'name': 'Usine Gabès',
        'code': 'GCT-GAB',
        'description': 'Unité de traitement des phosphates à Gabès',
        'address': 'Zone Industrielle Gabès',
        'city': 'Gabès',
        'region': 'Gabès',
        'country': 'Tunisie',
        'postalCode': '6000',
        'latitude': 33.8815,
        'longitude': 10.0982,
        'phoneNumber': '+216 75 123 456',
        'email': '<EMAIL>',
        'managerId': null,
        'managerName': 'Mohamed Sassi',
        'isActive': true,
        'employeeCount': 200,
        'departments': ['Extraction', 'Traitement', 'Expédition', 'Sécurité'],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
    ];
    
    // Ajouter les usines
    final batch = firestore.batch();
    for (final factory in factories) {
      final docRef = factoriesRef.doc(factory['id'] as String);
      batch.set(docRef, factory);
    }
    await batch.commit();
    
    print('✅ ${factories.length} usines GCT créées');
  }
  
  /// Crée la collection des utilisateurs avec des profils de test
  static Future<void> _createUsersCollection(FirebaseFirestore firestore) async {
    final usersRef = firestore.collection('users');
    
    // Note: En production, les utilisateurs seront créés via Firebase Auth
    // Ici on crée juste la structure de la collection
    
    print('✅ Collection users configurée');
  }
  
  /// Crée la collection des formations
  static Future<void> _createTrainingsCollection(FirebaseFirestore firestore) async {
    final trainingsRef = firestore.collection('trainings');
    
    // Vérifier si des formations existent déjà
    final snapshot = await trainingsRef.limit(1).get();
    if (snapshot.docs.isNotEmpty) {
      print('ℹ️ Collection trainings existe déjà');
      return;
    }
    
    // Créer les formations de base
    final trainings = [
      {
        'id': 'safety-basic-001',
        'title': 'Formation Sécurité de Base',
        'description': 'Formation obligatoire sur les règles de sécurité de base dans l\'industrie chimique',
        'category': 'SAFETY_BASIC',
        'level': 'BASIC',
        'duration': 120, // minutes
        'isRequired': true,
        'validityPeriod': 365, // jours
        'targetRoles': ['EMPLOYEE', 'FACTORY_ADMIN'],
        'content': {
          'modules': [
            'Règles de sécurité générales',
            'Équipements de protection individuelle',
            'Procédures d\'urgence de base',
            'Signalisation de sécurité'
          ],
          'quiz': true,
          'practicalTest': false,
        },
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'chemical-handling-001',
        'title': 'Manipulation des Produits Chimiques',
        'description': 'Formation avancée sur la manipulation sécurisée des produits chimiques',
        'category': 'CHEMICAL_HANDLING',
        'level': 'INTERMEDIATE',
        'duration': 180,
        'isRequired': true,
        'validityPeriod': 730, // 2 ans
        'targetRoles': ['EMPLOYEE'],
        'content': {
          'modules': [
            'Classification des produits chimiques',
            'Techniques de manipulation sécurisée',
            'Stockage et transport',
            'Gestion des déversements'
          ],
          'quiz': true,
          'practicalTest': true,
        },
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'emergency-procedures-001',
        'title': 'Procédures d\'Urgence',
        'description': 'Formation sur les procédures d\'évacuation et de gestion des urgences',
        'category': 'EMERGENCY',
        'level': 'ADVANCED',
        'duration': 240,
        'isRequired': true,
        'validityPeriod': 365,
        'targetRoles': ['EMPLOYEE', 'FACTORY_ADMIN', 'SECURITY_ADMIN_GCT'],
        'content': {
          'modules': [
            'Plans d\'évacuation',
            'Gestion des incidents majeurs',
            'Communication d\'urgence',
            'Premiers secours'
          ],
          'quiz': true,
          'practicalTest': true,
        },
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
    ];
    
    // Ajouter les formations
    final batch = firestore.batch();
    for (final training in trainings) {
      final docRef = trainingsRef.doc(training['id'] as String);
      batch.set(docRef, training);
    }
    await batch.commit();
    
    print('✅ ${trainings.length} formations créées');
  }
  
  /// Vérifie l'état de la connexion Firebase
  static bool get isInitialized => _isInitialized;
  
  /// Obtient les instances Firebase
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
}
