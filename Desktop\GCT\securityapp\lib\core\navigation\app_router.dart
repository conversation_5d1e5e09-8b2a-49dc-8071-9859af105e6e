import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/bloc/auth_state.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/claims/presentation/pages/claims_list_page.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter createRouter(AuthBloc authBloc) {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/splash',
      redirect: (context, state) {
        final authState = authBloc.state;
        final isOnSplash = state.matchedLocation == '/splash';
        final isOnLogin = state.matchedLocation == '/login';
        final isOnDashboard = state.matchedLocation.startsWith('/dashboard');

        // Si on est sur le splash, laisser passer
        if (isOnSplash) return null;

        // Si l'utilisateur est authentifié
        if (authState is AuthAuthenticated) {
          // Si on est sur login, rediriger vers dashboard
          if (isOnLogin) return '/dashboard';
          // Sinon laisser passer
          return null;
        }

        // Si l'utilisateur n'est pas authentifié
        if (authState is AuthUnauthenticated || authState is AuthError) {
          // Si on n'est pas sur login, rediriger vers login
          if (!isOnLogin) return '/login';
        }

        // Dans tous les autres cas, laisser passer
        return null;
      },
      routes: [
        // Splash Screen
        GoRoute(
          path: '/splash',
          name: 'splash',
          builder: (context, state) => const SplashPage(),
        ),

        // Login
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginPage(),
        ),

        // Dashboard et routes principales
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          builder: (context, state) => const DashboardPage(),
          routes: [
            // Claims routes
            GoRoute(
              path: '/claims',
              name: 'claims',
              builder: (context, state) => const ClaimsListPage(),
              routes: [
                GoRoute(
                  path: '/create',
                  name: 'create-claim',
                  builder: (context, state) => const Placeholder(), // TODO: CreateClaimPage
                ),
                GoRoute(
                  path: '/:id',
                  name: 'claim-details',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return const Placeholder(); // TODO: ClaimDetailsPage(id: id)
                  },
                ),
              ],
            ),

            // Training routes
            GoRoute(
              path: '/trainings',
              name: 'trainings',
              builder: (context, state) => const Placeholder(), // TODO: TrainingsListPage
              routes: [
                GoRoute(
                  path: '/:id',
                  name: 'training-details',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return const Placeholder(); // TODO: TrainingDetailsPage(id: id)
                  },
                ),
              ],
            ),

            // Factories routes (Admin only)
            GoRoute(
              path: '/factories',
              name: 'factories',
              builder: (context, state) => const Placeholder(), // TODO: FactoriesListPage
              routes: [
                GoRoute(
                  path: '/create',
                  name: 'create-factory',
                  builder: (context, state) => const Placeholder(), // TODO: CreateFactoryPage
                ),
                GoRoute(
                  path: '/:id',
                  name: 'factory-details',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return const Placeholder(); // TODO: FactoryDetailsPage(id: id)
                  },
                ),
              ],
            ),

            // Profile routes
            GoRoute(
              path: '/profile',
              name: 'profile',
              builder: (context, state) => const Placeholder(), // TODO: ProfilePage
            ),

            // Settings routes
            GoRoute(
              path: '/settings',
              name: 'settings',
              builder: (context, state) => const Placeholder(), // TODO: SettingsPage
            ),
          ],
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Page non trouvée',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'La page "${state.matchedLocation}" n\'existe pas.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go('/dashboard'),
                child: const Text('Retour à l\'accueil'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
