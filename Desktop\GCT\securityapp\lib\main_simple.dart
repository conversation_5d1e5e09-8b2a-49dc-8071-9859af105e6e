import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/theme/app_theme.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/auth/domain/usecases/login_usecase.dart';
import 'features/auth/domain/usecases/logout_usecase.dart';
import 'features/auth/domain/usecases/get_current_user_usecase.dart';
import 'features/auth/domain/usecases/refresh_token_usecase.dart';
import 'features/auth/data/repositories/auth_repository_impl.dart';
import 'features/auth/data/datasources/auth_firebase_datasource.dart';
import 'features/auth/data/datasources/auth_local_datasource.dart';
import 'features/auth/presentation/bloc/auth_event.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize SharedPreferences
  final sharedPrefs = await SharedPreferences.getInstance();
  
  runApp(GCTSecurityApp(sharedPrefs: sharedPrefs));
}

class GCTSecurityApp extends StatelessWidget {
  final SharedPreferences sharedPrefs;
  
  const GCTSecurityApp({super.key, required this.sharedPrefs});

  @override
  Widget build(BuildContext context) {
    // Create dependencies
    final localDataSource = AuthLocalDataSourceImpl(sharedPrefs);
    final firebaseDataSource = AuthFirebaseDataSourceImpl();
    final repository = AuthRepositoryImpl(
      firebaseDataSource: firebaseDataSource,
      localDataSource: localDataSource,
    );

    final authBloc = AuthBloc(
      loginUseCase: LoginUseCase(repository),
      logoutUseCase: LogoutUseCase(repository),
      getCurrentUserUseCase: GetCurrentUserUseCase(repository),
      refreshTokenUseCase: RefreshTokenUseCase(repository),
    );

    return BlocProvider.value(
      value: authBloc,
      child: MaterialApp(
        title: 'GCT Security',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        home: const LoginPage(),
        routes: {
          '/login': (context) => const LoginPage(),
          '/dashboard': (context) => const DashboardPage(),
        },
      ),
    );
  }
}

// Page de tableau de bord temporaire
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GCT Security - Tableau de Bord'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              // Déconnexion
              context.read<AuthBloc>().add(const LogoutRequested());
              Navigator.of(context).pushReplacementNamed('/login');
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.security,
              size: 100,
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: 20),
            Text(
              'Bienvenue dans GCT Security',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Application de gestion de la sécurité',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 40),
            Card(
              margin: EdgeInsets.all(20),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      'Fonctionnalités disponibles :',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 15),
                    ListTile(
                      leading: Icon(Icons.report_problem, color: AppTheme.warningColor),
                      title: Text('Gestion des Réclamations'),
                      subtitle: Text('Signaler et suivre les incidents de sécurité'),
                    ),
                    ListTile(
                      leading: Icon(Icons.school, color: AppTheme.infoColor),
                      title: Text('Formations Obligatoires'),
                      subtitle: Text('Suivi des formations en sécurité'),
                    ),
                    ListTile(
                      leading: Icon(Icons.factory, color: AppTheme.successColor),
                      title: Text('Gestion Multi-Usines'),
                      subtitle: Text('Centralisation pour toutes les usines GCT'),
                    ),
                    ListTile(
                      leading: Icon(Icons.analytics, color: AppTheme.primaryColor),
                      title: Text('Tableau de Bord'),
                      subtitle: Text('Statistiques et rapports de sécurité'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
