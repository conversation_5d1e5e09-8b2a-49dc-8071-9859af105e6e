enum TrainingType {
  chemicalSafety,
  firstAid,
  equipmentHandling,
  emergencyProcedures,
  fireProtection,
  workAtHeight,
  confinedSpace,
  personalProtection,
}

enum TrainingStatus {
  pending,
  approved,
  rejected,
  expired,
  completed,
}

class TrainingModel {
  final String id;
  final String userId;
  final String userName;
  final String factoryId;
  final String factoryName;
  final TrainingType type;
  final TrainingStatus status;
  final DateTime requestedAt;
  final DateTime? approvedAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final String? approvedBy;
  final String? approverName;
  final String? certificateUrl;
  final String? approvalComment;
  final int validityMonths;
  final Map<String, dynamic>? metadata;

  const TrainingModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.factoryId,
    required this.factoryName,
    required this.type,
    required this.status,
    required this.requestedAt,
    this.approvedAt,
    this.completedAt,
    this.expiresAt,
    this.approvedBy,
    this.approverName,
    this.certificateUrl,
    this.approvalComment,
    this.validityMonths = 6, // Validité de 6 mois selon spécifications GCT
    this.metadata,
  });

  String get typeDisplayName {
    switch (type) {
      case TrainingType.chemicalSafety:
        return 'Sécurité Chimique';
      case TrainingType.firstAid:
        return 'Premiers Secours';
      case TrainingType.equipmentHandling:
        return 'Manipulation d\'Équipements';
      case TrainingType.emergencyProcedures:
        return 'Procédures d\'Urgence';
      case TrainingType.fireProtection:
        return 'Protection Incendie';
      case TrainingType.workAtHeight:
        return 'Travail en Hauteur';
      case TrainingType.confinedSpace:
        return 'Espaces Confinés';
      case TrainingType.personalProtection:
        return 'Équipements de Protection';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TrainingStatus.pending:
        return 'En attente';
      case TrainingStatus.approved:
        return 'Approuvée';
      case TrainingStatus.rejected:
        return 'Rejetée';
      case TrainingStatus.expired:
        return 'Expirée';
      case TrainingStatus.completed:
        return 'Terminée';
    }
  }

  bool get isValid {
    if (status != TrainingStatus.completed) return false;
    if (expiresAt == null) return true;
    return DateTime.now().isBefore(expiresAt!);
  }

  bool get isExpiringSoon {
    if (expiresAt == null) return false;
    final daysUntilExpiry = expiresAt!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  int get daysUntilExpiry {
    if (expiresAt == null) return -1;
    return expiresAt!.difference(DateTime.now()).inDays;
  }

  TrainingModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? factoryId,
    String? factoryName,
    TrainingType? type,
    TrainingStatus? status,
    DateTime? requestedAt,
    DateTime? approvedAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? approvedBy,
    String? approverName,
    String? certificateUrl,
    String? approvalComment,
    int? validityMonths,
    Map<String, dynamic>? metadata,
  }) {
    return TrainingModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      type: type ?? this.type,
      status: status ?? this.status,
      requestedAt: requestedAt ?? this.requestedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      completedAt: completedAt ?? this.completedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      approvedBy: approvedBy ?? this.approvedBy,
      approverName: approverName ?? this.approverName,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      approvalComment: approvalComment ?? this.approvalComment,
      validityMonths: validityMonths ?? this.validityMonths,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'requestedAt': requestedAt.toIso8601String(),
      'approvedAt': approvedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'approvedBy': approvedBy,
      'approverName': approverName,
      'certificateUrl': certificateUrl,
      'approvalComment': approvalComment,
      'validityMonths': validityMonths,
      'metadata': metadata,
    };
  }

  factory TrainingModel.fromJson(Map<String, dynamic> json) {
    return TrainingModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      factoryId: json['factoryId'] ?? '',
      factoryName: json['factoryName'] ?? '',
      type: TrainingType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TrainingType.chemicalSafety,
      ),
      status: TrainingStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TrainingStatus.pending,
      ),
      requestedAt: json['requestedAt'] != null ? DateTime.parse(json['requestedAt']) : DateTime.now(),
      approvedAt: json['approvedAt'] != null ? DateTime.parse(json['approvedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      approvedBy: json['approvedBy'],
      approverName: json['approverName'],
      certificateUrl: json['certificateUrl'],
      approvalComment: json['approvalComment'],
      validityMonths: json['validityMonths'] ?? 12,
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }
}
