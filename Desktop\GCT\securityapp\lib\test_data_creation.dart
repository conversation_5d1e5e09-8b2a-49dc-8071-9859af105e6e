import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'app/data/services/demo_data_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  print('Firebase initialisé, création des données...');

  try {
    // Supprimer les anciennes données
    await DemoDataService.clearDemoData();
    print('Anciennes données supprimées');

    // Créer les nouvelles données
    await DemoDataService.initializeDemoData();
    print('Nouvelles données créées avec succès');

  } catch (e) {
    print('Erreur lors de la création des données: $e');
  }

  print('Terminé');
}
