                        -HC:\Mobile\Flutter_SDK\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Mobile\Android_SDK\Android_SDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Mobile\Android_SDK\Android_SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\GCT\securityapp\build\app\intermediates\cxx\Debug\2s331i86\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\GCT\securityapp\build\app\intermediates\cxx\Debug\2s331i86\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\Desktop\GCT\securityapp\android\app\.cxx\Debug\2s331i86\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2