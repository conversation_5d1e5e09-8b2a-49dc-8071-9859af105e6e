import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../firebase_options.dart';

class FirebaseTest {
  static Future<void> testFirebaseConnection() async {
    try {
      print('🔥 Test de connexion Firebase...');
      
      // 1. Initialiser Firebase
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      print('✅ Firebase initialisé avec succès');
      
      // 2. Tester Firebase Auth
      final auth = FirebaseAuth.instance;
      print('✅ Firebase Auth connecté');
      print('   - Utilisateur actuel: ${auth.currentUser?.email ?? "Aucun"}');
      
      // 3. Tester Firestore
      final firestore = FirebaseFirestore.instance;
      await firestore.collection('test').doc('connection').set({
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'connected',
        'app': 'GCT Security',
      });
      print('✅ Firestore connecté et test d\'écriture réussi');
      
      // 4. Lire depuis Firestore
      final testDoc = await firestore.collection('test').doc('connection').get();
      if (testDoc.exists) {
        print('✅ Firestore lecture réussie: ${testDoc.data()}');
      }
      
      // 5. Vérifier les collections existantes
      await _checkExistingCollections(firestore);
      
      print('🎉 Tous les tests Firebase sont passés avec succès !');
      
    } catch (e) {
      print('❌ Erreur de connexion Firebase: $e');
      print('');
      print('🔧 Solutions possibles:');
      print('   1. Vérifiez que google-services.json est dans android/app/');
      print('   2. Vérifiez firebase_options.dart avec les bonnes clés');
      print('   3. Vérifiez que les services sont activés dans la console Firebase');
      print('   4. Vérifiez votre connexion internet');
    }
  }
  
  static Future<void> _checkExistingCollections(FirebaseFirestore firestore) async {
    try {
      print('📊 Vérification des collections existantes...');
      
      // Vérifier la collection users
      final usersSnapshot = await firestore.collection('users').limit(1).get();
      print('   - Collection "users": ${usersSnapshot.docs.length} documents trouvés');
      
      // Vérifier la collection claims
      final claimsSnapshot = await firestore.collection('claims').limit(1).get();
      print('   - Collection "claims": ${claimsSnapshot.docs.length} documents trouvés');
      
      // Vérifier la collection trainings
      final trainingsSnapshot = await firestore.collection('trainings').limit(1).get();
      print('   - Collection "trainings": ${trainingsSnapshot.docs.length} documents trouvés');
      
      // Vérifier la collection factories
      final factoriesSnapshot = await firestore.collection('factories').limit(1).get();
      print('   - Collection "factories": ${factoriesSnapshot.docs.length} documents trouvés');
      
    } catch (e) {
      print('⚠️ Erreur lors de la vérification des collections: $e');
    }
  }
  
  static Future<void> createTestUser() async {
    try {
      print('👤 Création d\'un utilisateur de test...');
      
      final auth = FirebaseAuth.instance;
      final firestore = FirebaseFirestore.instance;
      
      // Créer un utilisateur de test
      final userCredential = await auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'test123',
      );
      
      if (userCredential.user != null) {
        // Créer le profil dans Firestore
        await firestore.collection('users').doc(userCredential.user!.uid).set({
          'id': userCredential.user!.uid,
          'email': '<EMAIL>',
          'name': 'Utilisateur Test',
          'role': 'employee',
          'factoryId': 'factory_test',
          'factoryName': 'Usine Test',
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
        });
        
        print('✅ Utilisateur de test créé avec succès');
        print('   - Email: <EMAIL>');
        print('   - Mot de passe: test123');
        
        // Se déconnecter
        await auth.signOut();
      }
      
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        print('ℹ️ L\'utilisateur de test existe déjà');
      } else {
        print('❌ Erreur création utilisateur de test: $e');
      }
    }
  }
  
  static Future<void> initializeSampleData() async {
    try {
      print('📊 Initialisation des données d\'exemple...');
      
      final firestore = FirebaseFirestore.instance;
      
      // Créer des usines d'exemple
      final factories = [
        {
          'id': 'factory_sfax',
          'name': 'Usine Sfax',
          'code': 'GCT-SFX',
          'address': 'Zone Industrielle Sfax',
          'city': 'Sfax',
          'region': 'Sfax',
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
        },
        {
          'id': 'factory_tunis',
          'name': 'Usine Tunis',
          'code': 'GCT-TUN',
          'address': 'Zone Industrielle Ben Arous',
          'city': 'Ben Arous',
          'region': 'Tunis',
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
        },
      ];
      
      for (final factory in factories) {
        await firestore.collection('factories').doc(factory['id'] as String).set(factory);
      }
      
      print('✅ Données d\'exemple initialisées');
      
    } catch (e) {
      print('❌ Erreur initialisation données: $e');
    }
  }
  
  static Future<void> checkFirebaseRules() async {
    try {
      print('🔒 Vérification des règles de sécurité...');
      
      final firestore = FirebaseFirestore.instance;
      
      // Tenter d'accéder aux collections sans authentification
      try {
        await firestore.collection('users').limit(1).get();
        print('⚠️ ATTENTION: Les règles de sécurité permettent l\'accès non authentifié aux utilisateurs');
      } catch (e) {
        print('✅ Règles de sécurité actives pour la collection users');
      }
      
    } catch (e) {
      print('❌ Erreur vérification règles: $e');
    }
  }
  
  static Future<void> runAllTests() async {
    print('🧪 === TESTS FIREBASE GCT SECURITY ===');
    print('');
    
    await testFirebaseConnection();
    print('');
    
    await createTestUser();
    print('');
    
    await initializeSampleData();
    print('');
    
    await checkFirebaseRules();
    print('');
    
    print('🏁 Tests terminés !');
  }
}
