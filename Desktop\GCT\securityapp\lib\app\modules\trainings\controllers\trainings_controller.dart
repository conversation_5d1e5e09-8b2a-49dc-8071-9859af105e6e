import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/simple_training_model.dart';

class TrainingsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthController authController = Get.find<AuthController>();

  // Form controllers
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final justificationController = TextEditingController();
  
  // Form state
  final formKey = GlobalKey<FormState>();
  var isLoading = false.obs;
  var selectedCategory = 'Sécurité au travail'.obs;
  var selectedUrgency = 'Normale'.obs;
  var selectedDuration = '1 jour'.obs;
  
  // Category options
  final categories = [
    'Sécurité au travail',
    'Manipulation d\'équipements',
    'Procédures d\'urgence',
    'Hygiène industrielle',
    'Gestion des risques',
    'Formation réglementaire',
    'Compétences techniques',
    'Autre'
  ];

  // Urgency options
  final urgencyLevels = ['Faible', 'Normale', 'Élevée', 'Urgente'];
  
  // Duration options
  final durations = [
    '2 heures',
    '4 heures', 
    '1 jour',
    '2 jours',
    '3 jours',
    '1 semaine',
    'Autre'
  ];

  // Trainings list
  var trainings = <TrainingModel>[].obs;
  var filteredTrainings = <TrainingModel>[].obs;
  var searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Ne pas charger automatiquement les formations
    // loadTrainings() sera appelé manuellement si nécessaire
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    justificationController.dispose();
    super.onClose();
  }

  Future<void> loadTrainings() async {
    try {
      isLoading.value = true;
      
      final user = authController.currentUser;
      if (user == null) return;

      Query query = _firestore.collection('trainings');
      
      // Filter by user for employees
      if (user.role == 'employe') {
        query = query.where('employeeId', isEqualTo: user.id);
      } else if (user.role == 'admin_usine') {
        query = query.where('factory', isEqualTo: user.factory);
      }
      
      final snapshot = await query
          .orderBy('createdAt', descending: true)
          .get();

      trainings.value = snapshot.docs
          .map((doc) => TrainingModel.fromFirestore(doc))
          .toList();
      
      filteredTrainings.value = trainings;
      
    } catch (e) {
      print('Erreur lors du chargement des formations: $e');
      // Ne pas afficher de snackbar d'erreur automatiquement
      // pour éviter les erreurs lors de l'initialisation
      trainings.value = [];
      filteredTrainings.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> requestTraining() async {
    if (!formKey.currentState!.validate()) return;

    try {
      isLoading.value = true;

      final user = authController.currentUser;
      if (user == null) {
        Get.snackbar(
          'Erreur',
          'Utilisateur non connecté',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Validation supplémentaire
      if (titleController.text.trim().isEmpty ||
          descriptionController.text.trim().isEmpty ||
          justificationController.text.trim().isEmpty) {
        Get.snackbar(
          'Erreur',
          'Tous les champs obligatoires doivent être remplis',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      final training = TrainingModel(
        id: '', // Will be set by Firestore
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        category: selectedCategory.value,
        urgency: selectedUrgency.value,
        duration: selectedDuration.value,
        justification: justificationController.text.trim(),
        status: 'En attente',
        employeeId: user.id,
        employeeName: '${user.firstName} ${user.lastName}',
        employeeEmail: user.email,
        factory: user.factory ?? '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final docRef = await _firestore.collection('trainings').add(training.toFirestore());
      
      // Update the training with the generated ID
      await docRef.update({'id': docRef.id});

      Get.snackbar(
        'Succès',
        'Demande de formation créée avec succès',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Clear form
      clearForm();

      // Navigate back
      Get.back();

    } catch (e) {
      print('Erreur lors de la création de la demande: $e');
      String errorMessage = 'Impossible de créer la demande de formation';

      // Personnaliser le message d'erreur selon le type d'erreur
      if (e.toString().contains('permission-denied')) {
        errorMessage = 'Vous n\'avez pas les permissions nécessaires';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Problème de connexion réseau';
      } else if (e.toString().contains('failed-precondition')) {
        errorMessage = 'Configuration de base de données en cours...';
      }

      Get.snackbar(
        'Erreur',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
      );
    } finally {
      isLoading.value = false;
    }
  }

  void clearForm() {
    titleController.clear();
    descriptionController.clear();
    justificationController.clear();
    selectedCategory.value = 'Sécurité au travail';
    selectedUrgency.value = 'Normale';
    selectedDuration.value = '1 jour';
  }

  void searchTrainings(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredTrainings.value = trainings;
    } else {
      filteredTrainings.value = trainings.where((training) =>
          training.title.toLowerCase().contains(query.toLowerCase()) ||
          training.description.toLowerCase().contains(query.toLowerCase()) ||
          training.category.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }
  }

  Color getUrgencyColor(String urgency) {
    switch (urgency) {
      case 'Urgente':
        return Colors.red.shade700;
      case 'Élevée':
        return Colors.orange.shade600;
      case 'Normale':
        return Colors.blue.shade600;
      case 'Faible':
        return Colors.green.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  Color getStatusColor(String status) {
    switch (status) {
      case 'En attente':
        return Colors.orange.shade600;
      case 'Approuvée':
        return Colors.blue.shade600;
      case 'En cours':
        return Colors.purple.shade600;
      case 'Terminée':
        return Colors.green.shade600;
      case 'Refusée':
        return Colors.red.shade600;
      default:
        return Colors.grey.shade600;
    }
  }
}
