import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/theme/app_theme.dart';
import 'core/services/firebase_setup_service.dart';
import 'core/di/simple_injection.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/pages/splash_page.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/auth/presentation/bloc/auth_event.dart';
import 'features/auth/presentation/bloc/auth_state.dart';
import 'core/widgets/permission_widget.dart';
import 'core/services/permissions_service.dart';
import 'features/claims/presentation/pages/create_claim_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialiser Firebase
    print('🔥 Initialisation de Firebase...');
    await FirebaseSetupService.initialize();
    
    // Créer les collections de base
    print('📁 Création des collections de base...');
    await FirebaseSetupService.createBaseCollections();
    
    // Initialiser les dépendances
    print('🔧 Initialisation des dépendances...');
    await SimpleInjection.instance.init();
    
    print('✅ Application GCT Security prête !');
    
  } catch (e) {
    print('❌ Erreur lors de l\'initialisation: $e');
    // En cas d'erreur, on continue avec le mode démo
  }
  
  runApp(const GCTSecurityApp());
}

class GCTSecurityApp extends StatelessWidget {
  const GCTSecurityApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: SimpleInjection.instance.authBloc..add(const CheckAuthStatus()),
      child: MaterialApp(
        title: 'GCT Security',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        home: const SplashPage(),
        routes: {
          '/login': (context) => const LoginPage(),
          '/splash': (context) => const SplashPage(),
          '/dashboard': (context) => const DashboardPage(),
        },
      ),
    );
  }
}

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        String userName = 'Utilisateur';
        String userRole = 'Employé';
        String userEmail = '';
        
        if (state is AuthAuthenticated) {
          userName = state.user.fullName;
          userRole = _getRoleDisplayName(state.user.role);
          userEmail = state.user.email;
        }
        
        return Scaffold(
          appBar: AppBar(
            title: const Text('GCT Security'),
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            elevation: 2,
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () => _showNotifications(context),
              ),
              IconButton(
                icon: const Icon(Icons.person),
                onPressed: () => _showProfile(context),
              ),
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () => _logout(context),
              ),
            ],
          ),
          drawer: _buildNavigationDrawer(context, state.user),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Carte de bienvenue avec badge de rôle
                _buildWelcomeCard(userName, userRole, userEmail, state.user.role),
                const SizedBox(height: 24),
                
                // Statistiques rapides
                _buildQuickStats(),
                const SizedBox(height: 24),
                
                // Fonctionnalités principales
                const Text(
                  'Fonctionnalités',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                _buildFeatureGrid(context),
                const SizedBox(height: 24),
                
                // Activités récentes
                _buildRecentActivity(),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => _createNewClaim(context),
            backgroundColor: AppTheme.primaryColor,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        );
      },
    );
  }
  
  Widget _buildWelcomeCard(String userName, String userRole, String userEmail, String roleCode) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bienvenue, $userName',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    userRole,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (userEmail.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      userEmail,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Column(
              children: [
                RoleBadge(role: roleCode),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppTheme.successColor.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.circle,
                        size: 8,
                        color: AppTheme.successColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'En ligne',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.successColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.report_problem,
            title: 'Réclamations',
            value: '12',
            subtitle: 'En cours',
            color: AppTheme.warningColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.school,
            title: 'Formations',
            value: '3',
            subtitle: 'À compléter',
            color: AppTheme.infoColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.check_circle,
            title: 'Conformité',
            value: '95%',
            subtitle: 'Sécurité',
            color: AppTheme.successColor,
          ),
        ),
      ],
    );
  }
  
  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureGrid(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: [
        // Réclamations - Tous les utilisateurs peuvent créer
        _buildFeatureCard(
          icon: Icons.report_problem,
          title: 'Réclamations',
          subtitle: 'Signaler un incident',
          color: AppTheme.warningColor,
          onTap: () => Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const CreateClaimPage()),
          ),
        ),

        // Formations - Tous les utilisateurs
        _buildFeatureCard(
          icon: Icons.school,
          title: 'Formations',
          subtitle: 'Mes formations',
          color: AppTheme.infoColor,
          onTap: () => _showComingSoon(context, 'Module Formations'),
        ),

        // Usines - Selon les permissions
        PermissionWidget(
          permissions: const [AppPermissions.FACTORIES_VIEW_ALL, AppPermissions.FACTORIES_VIEW_OWN],
          child: _buildFeatureCard(
            icon: Icons.factory,
            title: 'Usines',
            subtitle: 'Gestion des sites',
            color: AppTheme.successColor,
            onTap: () => _showComingSoon(context, 'Gestion des Usines'),
          ),
        ),

        // Rapports - Selon les permissions
        PermissionWidget(
          permissions: const [AppPermissions.REPORTS_VIEW_ALL, AppPermissions.REPORTS_VIEW_FACTORY],
          child: _buildFeatureCard(
            icon: Icons.analytics,
            title: 'Rapports',
            subtitle: 'Statistiques',
            color: AppTheme.primaryColor,
            onTap: () => _showComingSoon(context, 'Rapports et Analytics'),
          ),
        ),

        // Gestion des utilisateurs - Admins seulement
        PermissionWidget(
          permissions: const [AppPermissions.USERS_VIEW_ALL, AppPermissions.USERS_VIEW_FACTORY],
          child: _buildFeatureCard(
            icon: Icons.people,
            title: 'Utilisateurs',
            subtitle: 'Gestion des équipes',
            color: Colors.purple,
            onTap: () => _showComingSoon(context, 'Gestion des Utilisateurs'),
          ),
        ),

        // Configuration - Super Admin et Security Admin seulement
        PermissionWidget(
          requiredRoles: const [AppRoles.SUPER_ADMIN, AppRoles.SECURITY_ADMIN_GCT],
          child: _buildFeatureCard(
            icon: Icons.settings,
            title: 'Configuration',
            subtitle: 'Paramètres système',
            color: Colors.grey,
            onTap: () => _showComingSoon(context, 'Configuration Système'),
          ),
        ),
      ].where((widget) => widget != null).cast<Widget>().toList(),
    );
  }
  
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 11,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Activité Récente',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildActivityItem(
                  icon: Icons.report,
                  title: 'Nouvelle réclamation créée',
                  subtitle: 'Fuite de produit chimique - Zone A',
                  time: 'Il y a 2 heures',
                  color: AppTheme.warningColor,
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.check_circle,
                  title: 'Formation complétée',
                  subtitle: 'Sécurité de base - Certificat obtenu',
                  time: 'Hier',
                  color: AppTheme.successColor,
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.notification_important,
                  title: 'Rappel de formation',
                  subtitle: 'Manipulation des produits chimiques',
                  time: 'Il y a 3 jours',
                  color: AppTheme.infoColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: TextStyle(
              fontSize: 11,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationDrawer(BuildContext context, User user) {
    return Drawer(
      child: Column(
        children: [
          // Header du drawer
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor,
                  AppTheme.primaryColor.withOpacity(0.8),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 35,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  user.fullName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                RoleBadge(
                  role: user.role,
                  fontSize: 10,
                ),
              ],
            ),
          ),

          // Menu items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Tableau de bord
                ListTile(
                  leading: const Icon(Icons.dashboard),
                  title: const Text('Tableau de Bord'),
                  onTap: () => Navigator.pop(context),
                ),

                // Réclamations
                ListTile(
                  leading: const Icon(Icons.report_problem),
                  title: const Text('Réclamations'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const CreateClaimPage()),
                    );
                  },
                ),

                // Formations
                ListTile(
                  leading: const Icon(Icons.school),
                  title: const Text('Formations'),
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoon(context, 'Module Formations');
                  },
                ),

                // Usines (selon permissions)
                PermissionWidget(
                  permissions: const [AppPermissions.FACTORIES_VIEW_ALL, AppPermissions.FACTORIES_VIEW_OWN],
                  child: ListTile(
                    leading: const Icon(Icons.factory),
                    title: const Text('Usines'),
                    onTap: () {
                      Navigator.pop(context);
                      _showComingSoon(context, 'Gestion des Usines');
                    },
                  ),
                ),

                // Utilisateurs (selon permissions)
                PermissionWidget(
                  permissions: const [AppPermissions.USERS_VIEW_ALL, AppPermissions.USERS_VIEW_FACTORY],
                  child: ListTile(
                    leading: const Icon(Icons.people),
                    title: const Text('Utilisateurs'),
                    onTap: () {
                      Navigator.pop(context);
                      _showComingSoon(context, 'Gestion des Utilisateurs');
                    },
                  ),
                ),

                // Rapports (selon permissions)
                PermissionWidget(
                  permissions: const [AppPermissions.REPORTS_VIEW_ALL, AppPermissions.REPORTS_VIEW_FACTORY],
                  child: ListTile(
                    leading: const Icon(Icons.analytics),
                    title: const Text('Rapports'),
                    onTap: () {
                      Navigator.pop(context);
                      _showComingSoon(context, 'Rapports et Analytics');
                    },
                  ),
                ),

                const Divider(),

                // Configuration (admins seulement)
                PermissionWidget(
                  requiredRoles: const [AppRoles.SUPER_ADMIN, AppRoles.SECURITY_ADMIN_GCT],
                  child: ListTile(
                    leading: const Icon(Icons.settings),
                    title: const Text('Configuration'),
                    onTap: () {
                      Navigator.pop(context);
                      _showComingSoon(context, 'Configuration Système');
                    },
                  ),
                ),

                // Aide et support
                ListTile(
                  leading: const Icon(Icons.help),
                  title: const Text('Aide & Support'),
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoon(context, 'Aide et Support');
                  },
                ),
              ],
            ),
          ),

          // Footer du drawer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'GCT Security v1.0.0',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _logout(context);
                    },
                    icon: const Icon(Icons.logout, size: 16),
                    label: const Text('Déconnexion'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'Super Administrateur';
      case 'SECURITY_ADMIN_GCT':
        return 'Admin Sécurité GCT';
      case 'FACTORY_ADMIN':
        return 'Admin d\'Usine';
      case 'EMPLOYEE':
        return 'Employé';
      default:
        return role;
    }
  }
  
  void _showNotifications(BuildContext context) {
    _showComingSoon(context, 'Notifications');
  }
  
  void _showProfile(BuildContext context) {
    _showComingSoon(context, 'Profil Utilisateur');
  }
  
  void _logout(BuildContext context) {
    context.read<AuthBloc>().add(const LogoutRequested());
  }
  
  void _createNewClaim(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateClaimPage()),
    );
  }
  
  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.construction, color: AppTheme.warningColor),
            const SizedBox(width: 8),
            Text(feature),
          ],
        ),
        content: const Text(
          'Cette fonctionnalité est en cours de développement.\n\n'
          'Elle sera disponible dans la prochaine version de l\'application GCT Security.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}


