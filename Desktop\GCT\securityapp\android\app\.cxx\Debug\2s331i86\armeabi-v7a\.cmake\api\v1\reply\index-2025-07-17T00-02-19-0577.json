{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/Mobile/Android_SDK/Android_SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-7202323dc0396f06721e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-2d0731872f741e62465e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b354fdc6a9bcf5c702ad.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-2d0731872f741e62465e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-b354fdc6a9bcf5c702ad.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-7202323dc0396f06721e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}