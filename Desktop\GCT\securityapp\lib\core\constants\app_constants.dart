class AppConstants {
  // App Information
  static const String appName = 'GCT Security';
  static const String appVersion = '1.0.0';
  static const String companyName = 'Groupe Chimique Tunisien';
  
  // API Configuration
  static const String baseUrl = 'https://api.gct-security.tn';
  static const String apiVersion = 'v1';
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String firstLaunchKey = 'first_launch';
  
  // User Roles
  static const String superAdminRole = 'SUPER_ADMIN';
  static const String securityAdminRole = 'SECURITY_ADMIN_GCT';
  static const String factoryAdminRole = 'FACTORY_ADMIN';
  static const String employeeRole = 'EMPLOYEE';
  
  // Claim Types
  static const String accidentType = 'ACCIDENT';
  static const String incidentType = 'INCIDENT';
  static const String riskBehaviorType = 'RISK_BEHAVIOR';
  static const String nearMissType = 'NEAR_MISS';
  
  // Claim Status
  static const String pendingStatus = 'PENDING';
  static const String inProgressStatus = 'IN_PROGRESS';
  static const String resolvedStatus = 'RESOLVED';
  static const String closedStatus = 'CLOSED';
  
  // Training Status
  static const String trainingNotStarted = 'NOT_STARTED';
  static const String trainingInProgress = 'IN_PROGRESS';
  static const String trainingCompleted = 'COMPLETED';
  static const String trainingExpired = 'EXPIRED';
  
  // Priority Levels
  static const String lowPriority = 'LOW';
  static const String mediumPriority = 'MEDIUM';
  static const String highPriority = 'HIGH';
  static const String criticalPriority = 'CRITICAL';
  
  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int maxDescriptionLength = 1000;
  static const int maxTitleLength = 100;
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String timeFormat = 'HH:mm';
  
  // Languages
  static const String frenchLanguage = 'fr';
  static const String arabicLanguage = 'ar';
  static const String englishLanguage = 'en';
  
  // Notification Types
  static const String claimNotification = 'CLAIM';
  static const String trainingNotification = 'TRAINING';
  static const String systemNotification = 'SYSTEM';
  
  // Map Configuration
  static const double defaultLatitude = 36.8065; // Tunis
  static const double defaultLongitude = 10.1815;
  static const double defaultZoom = 10.0;
}
