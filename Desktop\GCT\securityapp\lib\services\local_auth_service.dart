import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class LocalAuthService {
  // ==================== UTILISATEURS PRÉDÉFINIS ====================
  
  static const Map<String, Map<String, dynamic>> _predefinedUsers = {
    // Super Admin
    '<EMAIL>': {
      'password': 'admin123',
      'role': 'super_admin',
      'name': 'Administrateur Principal',
      'factory': 'Siège Social',
      'permissions': ['all'],
    },
    
    // Security Admin
    '<EMAIL>': {
      'password': 'security123',
      'role': 'security_admin',
      'name': 'Responsable Sécurité',
      'factory': 'Toutes les usines',
      'permissions': ['complaints', 'trainings', 'reports'],
    },
    
    // Factory Admin - Usine Gabès
    '<EMAIL>': {
      'password': 'gabes123',
      'role': 'factory_admin',
      'name': 'Admin Usine Gabès',
      'factory': '<PERSON><PERSON> Gabès',
      'permissions': ['complaints', 'trainings', 'users'],
    },
    
    // Factory Admin - Usine Sfax
    '<EMAIL>': {
      'password': 'sfax123',
      'role': 'factory_admin',
      'name': 'Admin Usine Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings', 'users'],
    },
    
    // Employee - Usine Gabès
    '<EMAIL>': {
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Gabès',
      'factory': 'Usine Gabès',
      'permissions': ['complaints', 'trainings'],
    },
    
    // Employee - Usine Sfax
    '<EMAIL>': {
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings'],
    },
    
    // Test accounts
    '<EMAIL>': {
      'password': '123456',
      'role': 'employee',
      'name': 'Utilisateur Test',
      'factory': 'Test Factory',
      'permissions': ['complaints'],
    },
  };

  // ==================== MÉTHODES D'AUTHENTIFICATION ====================
  
  /// Connexion utilisateur
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      // Vérifier si l'utilisateur existe
      if (!_predefinedUsers.containsKey(email.toLowerCase())) {
        return {
          'success': false,
          'message': 'Adresse email non trouvée',
        };
      }

      final user = _predefinedUsers[email.toLowerCase()]!;
      
      // Vérifier le mot de passe
      if (user['password'] != password) {
        return {
          'success': false,
          'message': 'Mot de passe incorrect',
        };
      }

      // Créer un token simple (en production, utilisez JWT)
      final token = _generateSimpleToken(email);
      
      // Sauvegarder la session
      await _saveUserSession({
        'email': email.toLowerCase(),
        'token': token,
        'role': user['role'],
        'name': user['name'],
        'factory': user['factory'],
        'permissions': user['permissions'],
        'loginTime': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'message': 'Connexion réussie',
        'user': {
          'email': email.toLowerCase(),
          'token': token,
          'role': user['role'],
          'name': user['name'],
          'factory': user['factory'],
          'permissions': user['permissions'],
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }

  /// Déconnexion
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_session');
  }

  /// Vérifier si l'utilisateur est connecté
  static Future<bool> isLoggedIn() async {
    final session = await getCurrentUserSession();
    return session != null;
  }

  /// Récupérer la session utilisateur actuelle
  static Future<Map<String, dynamic>?> getCurrentUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionString = prefs.getString('user_session');
      
      if (sessionString != null) {
        return jsonDecode(sessionString);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Vérifier les permissions
  static Future<bool> hasPermission(String permission) async {
    final session = await getCurrentUserSession();
    if (session == null) return false;
    
    final permissions = List<String>.from(session['permissions'] ?? []);
    return permissions.contains('all') || permissions.contains(permission);
  }

  /// Récupérer le rôle de l'utilisateur
  static Future<String?> getUserRole() async {
    final session = await getCurrentUserSession();
    return session?['role'];
  }

  // ==================== MÉTHODES PRIVÉES ====================
  
  /// Générer un token simple
  static String _generateSimpleToken(String email) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final data = '$email:$timestamp';
    return base64Encode(utf8.encode(data));
  }

  /// Sauvegarder la session utilisateur
  static Future<void> _saveUserSession(Map<String, dynamic> session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_session', jsonEncode(session));
  }

  // ==================== MÉTHODES UTILITAIRES ====================
  
  /// Lister tous les utilisateurs disponibles (pour debug)
  static List<Map<String, dynamic>> getAllUsers() {
    return _predefinedUsers.entries.map((entry) {
      return {
        'email': entry.key,
        'role': entry.value['role'],
        'name': entry.value['name'],
        'factory': entry.value['factory'],
        // Ne pas exposer le mot de passe
      };
    }).toList();
  }

  /// Changer le mot de passe (simulation)
  static Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final session = await getCurrentUserSession();
    if (session == null) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    final email = session['email'];
    final user = _predefinedUsers[email];
    
    if (user == null || user['password'] != currentPassword) {
      return {
        'success': false,
        'message': 'Mot de passe actuel incorrect',
      };
    }

    // En production, vous modifieriez la base de données
    // Ici, c'est juste une simulation
    return {
      'success': true,
      'message': 'Mot de passe changé avec succès (simulation)',
    };
  }

  /// Valider le format email
  static bool isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  /// Valider la force du mot de passe
  static Map<String, dynamic> validatePassword(String password) {
    if (password.length < 6) {
      return {
        'valid': false,
        'message': 'Le mot de passe doit contenir au moins 6 caractères',
      };
    }

    return {
      'valid': true,
      'message': 'Mot de passe valide',
    };
  }
}
