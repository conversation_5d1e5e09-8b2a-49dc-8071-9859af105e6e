import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class LocalAuthService {
  // ==================== UTILISATEURS PRÉDÉFINIS ====================
  
  static const Map<String, Map<String, dynamic>> _predefinedUsers = {
    // Super Admin
    '<EMAIL>': {
      'password': 'admin123',
      'role': 'super_admin',
      'name': 'Administrateur Principal',
      'factory': 'Siège Social',
      'permissions': ['all'],
    },
    
    // Security Admin
    '<EMAIL>': {
      'password': 'security123',
      'role': 'security_admin',
      'name': 'Responsable Sécurité',
      'factory': 'Toutes les usines',
      'permissions': ['complaints', 'trainings', 'reports'],
    },
    
    // Factory Admin - Usine Gabès
    '<EMAIL>': {
      'password': 'gabes123',
      'role': 'factory_admin',
      'name': 'Admin Usine Gabès',
      'factory': 'Usine <PERSON>abès',
      'permissions': ['complaints', 'trainings', 'users'],
    },
    
    // Factory Admin - Usine Sfax
    '<EMAIL>': {
      'password': 'sfax123',
      'role': 'factory_admin',
      'name': 'Admin Usine Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings', 'users'],
    },
    
    // Employee - Usine Gabès
    '<EMAIL>': {
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Gabès',
      'factory': 'Usine Gabès',
      'permissions': ['complaints', 'trainings'],
    },
    
    // Employee - Usine Sfax
    '<EMAIL>': {
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings'],
    },

    // Sara Mansour - Employée Gabès
    '<EMAIL>': {
      'password': 'Sara2024!',
      'role': 'employee',
      'name': 'Sara Mansour',
      'factory': 'Usine Gabès',
      'permissions': ['complaints', 'trainings'],
    },
    
    // Test accounts
    '<EMAIL>': {
      'password': '123456',
      'role': 'employee',
      'name': 'Utilisateur Test',
      'factory': 'Test Factory',
      'permissions': ['complaints'],
    },
  };

  // ==================== MÉTHODES D'AUTHENTIFICATION ====================
  
  /// Connexion utilisateur
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('=== TENTATIVE DE CONNEXION ===');
      print('Email: $email');

      // 1. D'abord, essayer de trouver l'utilisateur dans Firestore
      print('Tentative d\'authentification directe via Firestore...');
      final firestoreResult = await _loginWithFirestore(email, password);
      if (firestoreResult['success']) {
        print('Utilisateur trouvé dans Firestore: ${firestoreResult['user']['displayName']} (${firestoreResult['user']['role']})');
        return firestoreResult;
      }

      // 2. Si pas trouvé dans Firestore, essayer les comptes prédéfinis
      print('Tentative d\'authentification avec comptes prédéfinis...');

      // Vérifier si l'utilisateur existe dans les comptes prédéfinis
      if (!_predefinedUsers.containsKey(email.toLowerCase())) {
        return {
          'success': false,
          'message': 'Adresse email non trouvée',
        };
      }

      final user = _predefinedUsers[email.toLowerCase()]!;

      // Vérifier le mot de passe
      if (user['password'] != password) {
        return {
          'success': false,
          'message': 'Mot de passe incorrect',
        };
      }

      // Créer un token simple (en production, utilisez JWT)
      final token = _generateSimpleToken(email);

      // Sauvegarder la session
      await _saveUserSession({
        'email': email.toLowerCase(),
        'token': token,
        'role': user['role'],
        'name': user['name'],
        'factory': user['factory'],
        'permissions': user['permissions'],
        'loginTime': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'message': 'Connexion réussie',
        'user': {
          'email': email.toLowerCase(),
          'token': token,
          'role': user['role'],
          'name': user['name'],
          'factory': user['factory'],
          'permissions': user['permissions'],
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }

  /// Authentification via Firestore
  static Future<Map<String, dynamic>> _loginWithFirestore(String email, String password) async {
    try {
      print('🔍 Recherche utilisateur dans Firestore pour: $email');

      // Import dynamique pour éviter les erreurs de compilation
      final firestore = await _getFirestoreInstance();
      if (firestore == null) {
        print('❌ Firestore non disponible');
        return {'success': false, 'message': 'Firestore non disponible'};
      }

      print('✅ Firestore disponible, recherche en cours...');

      // Rechercher l'utilisateur par email
      final querySnapshot = await firestore
          .collection('auth_users')
          .where('email', isEqualTo: email.toLowerCase())
          .limit(1)
          .get();

      print('📊 Résultats de la recherche: ${querySnapshot.docs.length} documents trouvés');

      if (querySnapshot.docs.isEmpty) {
        // Essayons de lister tous les utilisateurs pour debug
        print('🔍 Aucun utilisateur trouvé, listage de tous les utilisateurs...');
        final allUsers = await firestore.collection('auth_users').get();
        print('📋 Total utilisateurs dans Firestore: ${allUsers.docs.length}');
        for (var doc in allUsers.docs) {
          final data = doc.data();
          print('   - ${data['email']} (${data['displayName']})');
        }
        return {'success': false, 'message': 'Utilisateur non trouvé dans Firestore'};
      }

      final userDoc = querySnapshot.docs.first;
      final userData = userDoc.data() as Map<String, dynamic>;

      // Vérifier le mot de passe (en production, utilisez un hash)
      if (userData['password'] != password) {
        return {'success': false, 'message': 'Mot de passe incorrect'};
      }

      // Créer un token
      final token = _generateSimpleToken(email);

      // Convertir le rôle Firestore vers le format attendu
      String role = userData['role'] ?? 'employee';
      List<String> permissions = [];

      switch (role) {
        case 'employe':
        case 'employee':
          role = 'employee';
          permissions = ['complaints', 'trainings'];
          break;
        case 'factory_admin':
          permissions = ['complaints', 'trainings', 'users'];
          break;
        case 'security_admin':
          permissions = ['complaints', 'trainings', 'reports'];
          break;
        case 'super_admin':
          permissions = ['all'];
          break;
      }

      // Sauvegarder la session
      final sessionData = {
        'email': email.toLowerCase(),
        'token': token,
        'role': role,
        'name': userData['displayName'] ?? userData['name'] ?? 'Utilisateur',
        'factory': userData['factory'] ?? 'Non assigné',
        'permissions': permissions,
        'loginTime': DateTime.now().toIso8601String(),
        'firestoreId': userDoc.id,
      };

      await _saveUserSession(sessionData);

      return {
        'success': true,
        'message': 'Connexion réussie via Firestore',
        'user': sessionData,
      };
    } catch (e) {
      print('Erreur Firestore: $e');
      return {'success': false, 'message': 'Erreur Firestore: $e'};
    }
  }

  /// Obtenir l'instance Firestore
  static Future<FirebaseFirestore?> _getFirestoreInstance() async {
    try {
      return FirebaseFirestore.instance;
    } catch (e) {
      print('Firebase non disponible: $e');
      return null;
    }
  }

  /// Déconnexion
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_session');
  }

  /// Vérifier si l'utilisateur est connecté
  static Future<bool> isLoggedIn() async {
    final session = await getCurrentUserSession();
    return session != null;
  }

  /// Récupérer la session utilisateur actuelle
  static Future<Map<String, dynamic>?> getCurrentUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionString = prefs.getString('user_session');
      
      if (sessionString != null) {
        return jsonDecode(sessionString);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Vérifier les permissions
  static Future<bool> hasPermission(String permission) async {
    final session = await getCurrentUserSession();
    if (session == null) return false;
    
    final permissions = List<String>.from(session['permissions'] ?? []);
    return permissions.contains('all') || permissions.contains(permission);
  }

  /// Récupérer le rôle de l'utilisateur
  static Future<String?> getUserRole() async {
    final session = await getCurrentUserSession();
    return session?['role'];
  }

  // ==================== MÉTHODES PRIVÉES ====================
  
  /// Générer un token simple
  static String _generateSimpleToken(String email) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final data = '$email:$timestamp';
    return base64Encode(utf8.encode(data));
  }

  /// Sauvegarder la session utilisateur
  static Future<void> _saveUserSession(Map<String, dynamic> session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_session', jsonEncode(session));
  }

  // ==================== MÉTHODES UTILITAIRES ====================
  
  /// Lister tous les utilisateurs disponibles (pour debug)
  static List<Map<String, dynamic>> getAllUsers() {
    return _predefinedUsers.entries.map((entry) {
      return {
        'email': entry.key,
        'role': entry.value['role'],
        'name': entry.value['name'],
        'factory': entry.value['factory'],
        // Ne pas exposer le mot de passe
      };
    }).toList();
  }

  /// Changer le mot de passe (simulation)
  static Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final session = await getCurrentUserSession();
    if (session == null) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    final email = session['email'];
    final user = _predefinedUsers[email];
    
    if (user == null || user['password'] != currentPassword) {
      return {
        'success': false,
        'message': 'Mot de passe actuel incorrect',
      };
    }

    // En production, vous modifieriez la base de données
    // Ici, c'est juste une simulation
    return {
      'success': true,
      'message': 'Mot de passe changé avec succès (simulation)',
    };
  }

  /// Valider le format email
  static bool isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  /// Valider la force du mot de passe
  static Map<String, dynamic> validatePassword(String password) {
    if (password.length < 6) {
      return {
        'valid': false,
        'message': 'Le mot de passe doit contenir au moins 6 caractères',
      };
    }

    return {
      'valid': true,
      'message': 'Mot de passe valide',
    };
  }
}
