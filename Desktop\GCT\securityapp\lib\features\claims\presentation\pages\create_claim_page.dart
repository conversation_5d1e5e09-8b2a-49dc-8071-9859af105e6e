import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/permission_widget.dart';
import '../../../../core/services/permissions_service.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/presentation/bloc/auth_state.dart';
import '../../domain/constants/claim_constants.dart';
import '../widgets/claim_form_widgets.dart';

class CreateClaimPage extends StatelessWidget {
  const CreateClaimPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: AppPermissions.CLAIMS_CREATE,
      fallback: const AccessDeniedWidget(
        message: 'Vous n\'avez pas l\'autorisation de créer des réclamations',
        icon: Icons.report_problem,
      ),
      showFallbackOnDenied: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Nouvelle Réclamation'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        body: const _CreateClaimForm(),
      ),
    );
  }
}

class _CreateClaimForm extends StatefulWidget {
  const _CreateClaimForm();

  @override
  State<_CreateClaimForm> createState() => _CreateClaimFormState();
}

class _CreateClaimFormState extends State<_CreateClaimForm>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;
  
  // Contrôleurs de texte
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _involvedPersonsController = TextEditingController();
  final _equipmentController = TextEditingController();
  final _estimatedCostController = TextEditingController();
  
  // Variables de formulaire
  String _selectedType = ClaimConstants.TYPE_INCIDENT;
  String _selectedPriority = ClaimConstants.PRIORITY_MEDIUM;
  String _selectedSeverity = ClaimConstants.SEVERITY_MINOR;
  String _selectedDepartment = ClaimConstants.DEPT_PRODUCTION;
  DateTime _incidentDate = DateTime.now();
  bool _isConfidential = false;
  bool _requiresInvestigation = false;
  List<String> _attachments = [];
  List<String> _involvedPersons = [];
  List<String> _equipmentInvolved = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _involvedPersonsController.dispose();
    _equipmentController.dispose();
    _estimatedCostController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return const Center(child: CircularProgressIndicator());
        }

        return Form(
          key: _formKey,
          child: Column(
            children: [
              // En-tête avec informations utilisateur
              _buildUserInfoHeader(state.user),
              
              // Tabs
              Container(
                color: Colors.grey[100],
                child: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.primaryColor,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: AppTheme.primaryColor,
                  tabs: const [
                    Tab(
                      icon: Icon(Icons.info),
                      text: 'Informations',
                    ),
                    Tab(
                      icon: Icon(Icons.details),
                      text: 'Détails',
                    ),
                    Tab(
                      icon: Icon(Icons.attach_file),
                      text: 'Pièces jointes',
                    ),
                  ],
                ),
              ),
              
              // Contenu des tabs
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildBasicInfoTab(),
                    _buildDetailsTab(),
                    _buildAttachmentsTab(),
                  ],
                ),
              ),
              
              // Boutons d'action
              _buildActionButtons(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserInfoHeader(user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              user.fullName.substring(0, 1).toUpperCase(),
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Signalé par: ${user.fullName}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '${user.email} • ${user.factoryName ?? 'Usine non définie'}',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          RoleBadge(role: user.role, fontSize: 10),
        ],
      ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Type d'incident
          ClaimFormSection(
            title: 'Type d\'incident',
            icon: Icons.category,
            child: ClaimTypeSelector(
              selectedType: _selectedType,
              onChanged: (value) => setState(() => _selectedType = value),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Titre
          ClaimFormSection(
            title: 'Titre de la réclamation',
            icon: Icons.title,
            child: TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                hintText: 'Résumé bref de l\'incident...',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le titre est obligatoire';
                }
                if (value.trim().length < 10) {
                  return 'Le titre doit contenir au moins 10 caractères';
                }
                return null;
              },
              maxLength: 100,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Description
          ClaimFormSection(
            title: 'Description détaillée',
            icon: Icons.description,
            child: TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                hintText: 'Décrivez l\'incident en détail...',
                border: OutlineInputBorder(),
              ),
              maxLines: 5,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'La description est obligatoire';
                }
                if (value.trim().length < 20) {
                  return 'La description doit contenir au moins 20 caractères';
                }
                return null;
              },
              maxLength: 1000,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Priorité et Sévérité
          Row(
            children: [
              Expanded(
                child: ClaimFormSection(
                  title: 'Priorité',
                  icon: Icons.priority_high,
                  child: ClaimPrioritySelector(
                    selectedPriority: _selectedPriority,
                    onChanged: (value) => setState(() => _selectedPriority = value),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ClaimFormSection(
                  title: 'Sévérité',
                  icon: Icons.warning,
                  child: ClaimSeveritySelector(
                    selectedSeverity: _selectedSeverity,
                    onChanged: (value) => setState(() => _selectedSeverity = value),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Date et heure de l'incident
          ClaimFormSection(
            title: 'Date et heure de l\'incident',
            icon: Icons.access_time,
            child: ClaimDateTimePicker(
              selectedDate: _incidentDate,
              onChanged: (date) => setState(() => _incidentDate = date),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Département
          ClaimFormSection(
            title: 'Département/Zone',
            icon: Icons.business,
            child: ClaimDepartmentSelector(
              selectedDepartment: _selectedDepartment,
              onChanged: (value) => setState(() => _selectedDepartment = value),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Localisation
          ClaimFormSection(
            title: 'Localisation précise',
            icon: Icons.location_on,
            child: TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                hintText: 'Ex: Atelier 2, Ligne de production A, Poste 3...',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'La localisation est obligatoire';
                }
                return null;
              },
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Personnes impliquées
          ClaimFormSection(
            title: 'Personnes impliquées ou témoins',
            icon: Icons.people,
            child: ClaimPersonsSelector(
              persons: _involvedPersons,
              onChanged: (persons) => setState(() => _involvedPersons = persons),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Équipements impliqués
          ClaimFormSection(
            title: 'Équipements impliqués',
            icon: Icons.precision_manufacturing,
            child: ClaimEquipmentSelector(
              equipment: _equipmentInvolved,
              onChanged: (equipment) => setState(() => _equipmentInvolved = equipment),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Coût estimé
          ClaimFormSection(
            title: 'Coût estimé des dommages (TND)',
            icon: Icons.monetization_on,
            child: TextFormField(
              controller: _estimatedCostController,
              decoration: const InputDecoration(
                hintText: '0',
                border: OutlineInputBorder(),
                suffixText: 'TND',
              ),
              keyboardType: TextInputType.number,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Options
          ClaimFormSection(
            title: 'Options',
            icon: Icons.settings,
            child: Column(
              children: [
                CheckboxListTile(
                  title: const Text('Réclamation confidentielle'),
                  subtitle: const Text('Visible uniquement par les administrateurs'),
                  value: _isConfidential,
                  onChanged: (value) => setState(() => _isConfidential = value ?? false),
                ),
                CheckboxListTile(
                  title: const Text('Nécessite une enquête approfondie'),
                  subtitle: const Text('Assignera automatiquement un enquêteur'),
                  value: _requiresInvestigation,
                  onChanged: (value) => setState(() => _requiresInvestigation = value ?? false),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClaimFormSection(
            title: 'Photos et documents',
            icon: Icons.attach_file,
            child: ClaimAttachmentUploader(
              attachments: _attachments,
              onChanged: (attachments) => setState(() => _attachments = attachments),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Conseils pour les pièces jointes
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.infoColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.lightbulb, color: AppTheme.infoColor),
                    const SizedBox(width: 8),
                    const Text(
                      'Conseils pour les pièces jointes',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  '• Prenez des photos claires de la zone d\'incident\n'
                  '• Documentez les dommages matériels\n'
                  '• Incluez les équipements de protection utilisés\n'
                  '• Ajoutez tout document pertinent (procédures, rapports...)',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _submitClaim,
              icon: const Icon(Icons.send),
              label: const Text('Soumettre la réclamation'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitClaim() {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Implémenter la soumission de la réclamation
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Réclamation soumise avec succès !'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez corriger les erreurs dans le formulaire'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
