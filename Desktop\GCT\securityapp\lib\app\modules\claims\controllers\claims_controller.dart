import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/simple_claim_model.dart';

class ClaimsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthController authController = Get.find<AuthController>();

  // Form controllers
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final locationController = TextEditingController();
  
  // Form state
  final formKey = GlobalKey<FormState>();
  var isLoading = false.obs;
  var selectedPriority = 'Moyenne'.obs;
  var selectedCategory = 'Sécurité'.obs;
  
  // Priority options
  final priorities = ['Faible', 'Moyenne', 'Élevée', 'Critique'];
  
  // Category options
  final categories = [
    'Sécurité',
    'Équipement',
    'Environnement',
    'Procédures',
    'Formation',
    'Autre'
  ];

  // Claims list
  var claims = <ClaimModel>[].obs;
  var filteredClaims = <ClaimModel>[].obs;
  var searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Ne pas charger automatiquement les réclamations
    // loadClaims() sera appelé manuellement si nécessaire
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    locationController.dispose();
    super.onClose();
  }

  Future<void> loadClaims() async {
    try {
      isLoading.value = true;
      
      final user = authController.currentUser;
      if (user == null) return;

      Query query = _firestore.collection('claims');
      
      // Filter by user for employees
      if (user.role == 'employe') {
        query = query.where('employeeId', isEqualTo: user.id);
      } else if (user.role == 'admin_usine') {
        query = query.where('factory', isEqualTo: user.factory);
      }
      
      final snapshot = await query
          .orderBy('createdAt', descending: true)
          .get();

      claims.value = snapshot.docs
          .map((doc) => ClaimModel.fromFirestore(doc))
          .toList();
      
      filteredClaims.value = claims;
      
    } catch (e) {
      print('Erreur lors du chargement des réclamations: $e');
      // Ne pas afficher de snackbar d'erreur automatiquement
      // pour éviter les erreurs lors de l'initialisation
      claims.value = [];
      filteredClaims.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createClaim() async {
    if (!formKey.currentState!.validate()) return;

    try {
      isLoading.value = true;

      final user = authController.currentUser;
      if (user == null) {
        Get.snackbar(
          'Erreur',
          'Utilisateur non connecté',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Validation supplémentaire
      if (titleController.text.trim().isEmpty ||
          descriptionController.text.trim().isEmpty ||
          locationController.text.trim().isEmpty) {
        Get.snackbar(
          'Erreur',
          'Tous les champs obligatoires doivent être remplis',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      final claim = ClaimModel(
        id: '', // Will be set by Firestore
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        location: locationController.text.trim(),
        priority: selectedPriority.value,
        category: selectedCategory.value,
        status: 'En attente',
        employeeId: user.id,
        employeeName: '${user.firstName} ${user.lastName}',
        employeeEmail: user.email,
        factory: user.factory ?? '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final docRef = await _firestore.collection('claims').add(claim.toFirestore());
      
      // Update the claim with the generated ID
      await docRef.update({'id': docRef.id});

      Get.snackbar(
        'Succès',
        'Réclamation créée avec succès',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Clear form
      clearForm();

      // Recharger les données si on est sur la page des réclamations
      if (Get.currentRoute.contains('claims')) {
        await loadClaims();
      }

      // Navigate back
      Get.back();

    } catch (e) {
      print('Erreur lors de la création de la réclamation: $e');
      String errorMessage = 'Impossible de créer la réclamation';

      // Personnaliser le message d'erreur selon le type d'erreur
      if (e.toString().contains('permission-denied')) {
        errorMessage = 'Vous n\'avez pas les permissions nécessaires';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Problème de connexion réseau';
      } else if (e.toString().contains('failed-precondition')) {
        errorMessage = 'Configuration de base de données en cours...';
      }

      Get.snackbar(
        'Erreur',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
      );
    } finally {
      isLoading.value = false;
    }
  }

  void clearForm() {
    titleController.clear();
    descriptionController.clear();
    locationController.clear();
    selectedPriority.value = 'Moyenne';
    selectedCategory.value = 'Sécurité';
  }

  void searchClaims(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredClaims.value = claims;
    } else {
      filteredClaims.value = claims.where((claim) =>
          claim.title.toLowerCase().contains(query.toLowerCase()) ||
          claim.description.toLowerCase().contains(query.toLowerCase()) ||
          claim.category.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }
  }

  Color getPriorityColor(String priority) {
    switch (priority) {
      case 'Critique':
        return Colors.red.shade700;
      case 'Élevée':
        return Colors.orange.shade600;
      case 'Moyenne':
        return Colors.blue.shade600;
      case 'Faible':
        return Colors.green.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  Color getStatusColor(String status) {
    switch (status) {
      case 'En attente':
        return Colors.orange.shade600;
      case 'En cours':
        return Colors.blue.shade600;
      case 'Résolue':
        return Colors.green.shade600;
      case 'Fermée':
        return Colors.grey.shade600;
      default:
        return Colors.grey.shade600;
    }
  }
}
