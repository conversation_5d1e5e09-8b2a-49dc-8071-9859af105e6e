import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../models/claim_model.dart';
import '../models/training_model.dart';
import '../models/factory_model.dart';
import '../services/firebase_auth_service.dart';
import '../services/firebase_database_service.dart';

class FirebaseDemoData {
  static final FirebaseAuthService _authService = FirebaseAuthService();
  static final FirebaseDatabaseService _dbService = FirebaseDatabaseService();
  static final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // Initialiser toutes les données de démonstration
  static Future<void> initializeDemoData() async {
    try {
      print('🔥 Initialisation des données de démonstration Firebase...');
      
      await _createDemoUsers();
      await _createDemoFactories();
      await _createDemoClaims();
      await _createDemoTrainings();
      await _initializeStatistics();
      
      print('✅ Données de démonstration initialisées avec succès !');
    } catch (e) {
      print('❌ Erreur lors de l\'initialisation: $e');
    }
  }

  // Créer les utilisateurs de démonstration
  static Future<void> _createDemoUsers() async {
    print('👥 Création des utilisateurs de démonstration...');
    
    final demoUsers = [
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Super Administrateur',
        'role': UserRole.superAdmin,
        'factoryId': null,
        'factoryName': null,
      },
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Admin Sécurité GCT',
        'role': UserRole.securityAdmin,
        'factoryId': null,
        'factoryName': null,
      },
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Admin Usine Sfax',
        'role': UserRole.factoryAdmin,
        'factoryId': 'factory_sfax',
        'factoryName': 'Usine Sfax',
      },
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Admin Usine Tunis',
        'role': UserRole.factoryAdmin,
        'factoryId': 'factory_tunis',
        'factoryName': 'Usine Tunis',
      },
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Ahmed Ben Ali',
        'role': UserRole.employee,
        'factoryId': 'factory_sfax',
        'factoryName': 'Usine Sfax',
      },
      {
        'email': '<EMAIL>',
        'password': 'demo123',
        'name': 'Fatma Trabelsi',
        'role': UserRole.employee,
        'factoryId': 'factory_tunis',
        'factoryName': 'Usine Tunis',
      },
    ];

    for (final userData in demoUsers) {
      try {
        await _authService.registerWithEmailAndPassword(
          email: userData['email'] as String,
          password: userData['password'] as String,
          name: userData['name'] as String,
          role: userData['role'] as UserRole,
          factoryId: userData['factoryId'] as String?,
        );
        print('✅ Utilisateur créé: ${userData['email']}');
      } catch (e) {
        print('⚠️ Utilisateur existe déjà: ${userData['email']}');
      }
    }
  }

  // Créer les usines de démonstration
  static Future<void> _createDemoFactories() async {
    print('🏭 Création des usines de démonstration...');
    
    final factories = FactoryModel.getSampleFactories();
    
    for (final factory in factories) {
      try {
        await _database.child('factories').child(factory.id).set(factory.toJson());
        print('✅ Usine créée: ${factory.name}');
      } catch (e) {
        print('❌ Erreur création usine ${factory.name}: $e');
      }
    }
  }

  // Créer des réclamations de démonstration
  static Future<void> _createDemoClaims() async {
    print('📋 Création des réclamations de démonstration...');
    
    final demoClaims = [
      ClaimModel(
        id: 'claim_001',
        title: 'Fuite chimique détectée',
        description: 'Fuite de produit chimique dans la zone de production A',
        type: ClaimType.incident,
        status: ClaimStatus.pending,
        priority: ClaimPriority.critical,
        reportedBy: 'employee1_id',
        reporterName: 'Ahmed Ben Ali',
        factoryId: 'factory_sfax',
        factoryName: 'Usine Sfax',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        location: 'Zone Production A',
      ),
      ClaimModel(
        id: 'claim_002',
        title: 'Équipement défaillant',
        description: 'Système de ventilation en panne dans l\'atelier',
        type: ClaimType.nonCompliance,
        status: ClaimStatus.inProgress,
        priority: ClaimPriority.high,
        reportedBy: 'employee2_id',
        reporterName: 'Fatma Trabelsi',
        factoryId: 'factory_tunis',
        factoryName: 'Usine Tunis',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Atelier B',
      ),
      ClaimModel(
        id: 'claim_003',
        title: 'Presque-accident évité',
        description: 'Employé a failli glisser sur sol mouillé',
        type: ClaimType.nearMiss,
        status: ClaimStatus.resolved,
        priority: ClaimPriority.medium,
        reportedBy: 'employee1_id',
        reporterName: 'Ahmed Ben Ali',
        factoryId: 'factory_sfax',
        factoryName: 'Usine Sfax',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        location: 'Couloir principal',
      ),
    ];

    for (final claim in demoClaims) {
      try {
        await _database.child('claims').child(claim.id).set(claim.toJson());
        print('✅ Réclamation créée: ${claim.title}');
      } catch (e) {
        print('❌ Erreur création réclamation: $e');
      }
    }
  }

  // Créer des formations de démonstration
  static Future<void> _createDemoTrainings() async {
    print('🎓 Création des formations de démonstration...');
    
    final demoTrainings = [
      TrainingModel(
        id: 'training_001',
        userId: 'employee1_id',
        userName: 'Ahmed Ben Ali',
        factoryId: 'factory_sfax',
        factoryName: 'Usine Sfax',
        type: TrainingType.chemicalSafety,
        status: TrainingStatus.pending,
        requestedAt: DateTime.now().subtract(const Duration(days: 2)),
        validityMonths: 24,
      ),
      TrainingModel(
        id: 'training_002',
        userId: 'employee2_id',
        userName: 'Fatma Trabelsi',
        factoryId: 'factory_tunis',
        factoryName: 'Usine Tunis',
        type: TrainingType.firstAid,
        status: TrainingStatus.approved,
        requestedAt: DateTime.now().subtract(const Duration(days: 5)),
        approvedAt: DateTime.now().subtract(const Duration(days: 1)),
        validityMonths: 12,
      ),
      TrainingModel(
        id: 'training_003',
        userId: 'employee1_id',
        userName: 'Ahmed Ben Ali',
        factoryId: 'factory_sfax',
        factoryName: 'Usine Sfax',
        type: TrainingType.equipmentHandling,
        status: TrainingStatus.completed,
        requestedAt: DateTime.now().subtract(const Duration(days: 30)),
        approvedAt: DateTime.now().subtract(const Duration(days: 25)),
        completedAt: DateTime.now().subtract(const Duration(days: 20)),
        expiresAt: DateTime.now().add(const Duration(days: 335)),
        validityMonths: 12,
      ),
    ];

    for (final training in demoTrainings) {
      try {
        await _database.child('trainings').child(training.id).set(training.toJson());
        print('✅ Formation créée: ${training.typeDisplayName}');
      } catch (e) {
        print('❌ Erreur création formation: $e');
      }
    }
  }

  // Initialiser les statistiques
  static Future<void> _initializeStatistics() async {
    print('📊 Initialisation des statistiques...');
    
    final statistics = {
      'claims': {
        'total': 156,
        'pending': 23,
        'inProgress': 45,
        'resolved': 78,
        'critical': 12,
        'byFactory': {
          'factory_sfax': 45,
          'factory_tunis': 38,
          'factory_sousse': 32,
          'factory_gabes': 28,
        },
        'byType': {
          'accident': 34,
          'incident': 67,
          'nearMiss': 28,
          'nonCompliance': 19,
          'suggestion': 8,
        },
      },
      'trainings': {
        'total': 89,
        'pending': 12,
        'approved': 23,
        'completed': 45,
        'expired': 9,
        'byFactory': {
          'factory_sfax': 25,
          'factory_tunis': 22,
          'factory_sousse': 20,
          'factory_gabes': 18,
        },
      },
      'users': {
        'total': 89,
        'active': 85,
        'byRole': {
          'superAdmin': 1,
          'securityAdmin': 3,
          'factoryAdmin': 8,
          'employee': 77,
        },
      },
      'lastUpdated': DateTime.now().toIso8601String(),
    };

    try {
      await _database.child('statistics').set(statistics);
      print('✅ Statistiques initialisées');
    } catch (e) {
      print('❌ Erreur initialisation statistiques: $e');
    }
  }

  // Nettoyer toutes les données (pour les tests)
  static Future<void> clearAllData() async {
    print('🧹 Nettoyage des données...');
    
    try {
      await _database.child('claims').remove();
      await _database.child('trainings').remove();
      await _database.child('factories').remove();
      await _database.child('statistics').remove();
      
      print('✅ Données nettoyées');
    } catch (e) {
      print('❌ Erreur nettoyage: $e');
    }
  }

  // Vérifier si les données existent
  static Future<bool> checkDataExists() async {
    try {
      final snapshot = await _database.child('statistics').get();
      return snapshot.exists;
    } catch (e) {
      return false;
    }
  }
}
