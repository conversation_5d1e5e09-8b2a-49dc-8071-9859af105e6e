import '../models/user_model.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  UserModel? _currentUser;
  UserModel? get currentUser => _currentUser;

  // Utilisateurs de démonstration
  final List<UserModel> _demoUsers = [
    UserModel(
      id: '1',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: UserRole.superAdmin,
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      lastLogin: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    UserModel(
      id: '2',
      email: '<EMAIL>',
      name: '<PERSON><PERSON>',
      role: UserRole.securityAdmin,
      createdAt: DateTime.now().subtract(const Duration(days: 300)),
      lastLogin: DateTime.now().subtract(const Duration(hours: 1)),
    ),
    UserModel(
      id: '3',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: UserRole.factoryAdmin,
      factoryId: 'sfax_001',
      factoryName: 'Usine Sfax',
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      lastLogin: DateTime.now().subtract(const Duration(minutes: 30)),
    ),
    UserModel(
      id: '4',
      email: '<EMAIL>',
      name: 'Leila Mansouri',
      role: UserRole.factoryAdmin,
      factoryId: 'tunis_001',
      factoryName: 'Usine Tunis',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      lastLogin: DateTime.now().subtract(const Duration(hours: 4)),
    ),
    UserModel(
      id: '5',
      email: '<EMAIL>',
      name: 'Karim Bouazizi',
      role: UserRole.employee,
      factoryId: 'sfax_001',
      factoryName: 'Usine Sfax',
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
      lastLogin: DateTime.now().subtract(const Duration(minutes: 15)),
    ),
    UserModel(
      id: '6',
      email: '<EMAIL>',
      name: 'Sonia Khelifi',
      role: UserRole.employee,
      factoryId: 'tunis_001',
      factoryName: 'Usine Tunis',
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      lastLogin: DateTime.now().subtract(const Duration(hours: 6)),
    ),
  ];

  Future<UserModel?> login(String email, String password) async {
    // Simulation d'une authentification
    await Future.delayed(const Duration(milliseconds: 500));

    // Recherche de l'utilisateur par email
    final user = _demoUsers.firstWhere(
      (user) => user.email.toLowerCase() == email.toLowerCase(),
      orElse: () => _demoUsers.first, // Par défaut, retourne le premier utilisateur
    );

    // Mise à jour de la dernière connexion
    _currentUser = user.copyWith(lastLogin: DateTime.now());
    
    return _currentUser;
  }

  Future<void> logout() async {
    _currentUser = null;
  }

  bool get isLoggedIn => _currentUser != null;

  List<UserModel> getAllUsers() {
    return List.from(_demoUsers);
  }

  List<UserModel> getUsersByFactory(String factoryId) {
    return _demoUsers.where((user) => user.factoryId == factoryId).toList();
  }

  List<UserModel> getUsersByRole(UserRole role) {
    return _demoUsers.where((user) => user.role == role).toList();
  }
}
