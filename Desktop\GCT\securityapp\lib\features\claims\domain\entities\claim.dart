import 'package:equatable/equatable.dart';
import 'claim_comment.dart';

class Claim extends Equatable {
  final String id;
  final String title;
  final String description;
  final String type; // INCIDENT, ACCIDENT, NEAR_MISS, UNSAFE_BEHAVIOR, SUGGESTION
  final String status; // PENDING, IN_PROGRESS, RESOLVED, CLOSED, REJECTED
  final String priority; // LOW, MEDIUM, HIGH, CRITICAL
  final String severity; // MINOR, MODERATE, MAJOR, CATASTROPHIC
  final String reporterId;
  final String reporterName;
  final String reporterEmail;
  final String reporterPhone;
  final String? assignedToId;
  final String? assignedToName;
  final String factoryId;
  final String factoryName;
  final String department; // Zone/département où s'est produit l'incident
  final String location; // Description précise du lieu
  final double? latitude;
  final double? longitude;
  final DateTime incidentDate;
  final DateTime reportedAt;
  final DateTime? acknowledgedAt; // Quand la réclamation a été prise en compte
  final DateTime? resolvedAt;
  final DateTime? closedAt;
  final List<String> attachments; // URLs des photos/vidéos/documents
  final List<ClaimComment> comments;
  final List<String> involvedPersons; // Personnes impliquées ou témoins
  final List<String> equipmentInvolved; // Équipements impliqués
  final String? rootCause; // Cause racine identifiée
  final String? correctiveActions; // Actions correctives prises
  final String? preventiveActions; // Actions préventives recommandées
  final bool isConfidential; // Réclamation confidentielle
  final bool requiresInvestigation; // Nécessite une enquête approfondie
  final int estimatedCost; // Coût estimé des dommages (en millimes)
  final String? investigatorId; // ID de l'enquêteur assigné
  final String? investigatorName;
  final DateTime? investigationDeadline;
  final Map<String, dynamic>? customFields; // Champs personnalisés par usine
  final Map<String, dynamic>? metadata;

  const Claim({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    required this.severity,
    required this.reporterId,
    required this.reporterName,
    required this.reporterEmail,
    required this.reporterPhone,
    this.assignedToId,
    this.assignedToName,
    required this.factoryId,
    required this.factoryName,
    required this.department,
    required this.location,
    this.latitude,
    this.longitude,
    required this.incidentDate,
    required this.reportedAt,
    this.acknowledgedAt,
    this.resolvedAt,
    this.closedAt,
    this.attachments = const [],
    this.comments = const [],
    this.involvedPersons = const [],
    this.equipmentInvolved = const [],
    this.rootCause,
    this.correctiveActions,
    this.preventiveActions,
    this.isConfidential = false,
    this.requiresInvestigation = false,
    this.estimatedCost = 0,
    this.investigatorId,
    this.investigatorName,
    this.investigationDeadline,
    this.customFields,
    this.metadata,
  });

  bool get isPending => status == 'PENDING';
  bool get isInProgress => status == 'IN_PROGRESS';
  bool get isResolved => status == 'RESOLVED';
  bool get isClosed => status == 'CLOSED';

  bool get isLowPriority => priority == 'LOW';
  bool get isMediumPriority => priority == 'MEDIUM';
  bool get isHighPriority => priority == 'HIGH';
  bool get isCriticalPriority => priority == 'CRITICAL';

  bool get isAccident => type == 'ACCIDENT';
  bool get isIncident => type == 'INCIDENT';
  bool get isRiskBehavior => type == 'RISK_BEHAVIOR';
  bool get isNearMiss => type == 'NEAR_MISS';

  bool get hasLocation => latitude != null && longitude != null;
  bool get hasAttachments => attachments.isNotEmpty;
  bool get hasComments => comments.isNotEmpty;

  Duration? get resolutionTime {
    if (resolvedAt == null) return null;
    return resolvedAt!.difference(reportedAt);
  }

  Claim copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? status,
    String? priority,
    String? reporterId,
    String? reporterName,
    String? assignedToId,
    String? assignedToName,
    String? factoryId,
    String? factoryName,
    String? location,
    double? latitude,
    double? longitude,
    DateTime? incidentDate,
    DateTime? reportedAt,
    DateTime? resolvedAt,
    List<String>? attachments,
    List<ClaimComment>? comments,
    Map<String, dynamic>? metadata,
  }) {
    return Claim(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      reporterId: reporterId ?? this.reporterId,
      reporterName: reporterName ?? this.reporterName,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      incidentDate: incidentDate ?? this.incidentDate,
      reportedAt: reportedAt ?? this.reportedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        status,
        priority,
        severity,
        reporterId,
        reporterName,
        reporterEmail,
        reporterPhone,
        assignedToId,
        assignedToName,
        factoryId,
        factoryName,
        department,
        location,
        latitude,
        longitude,
        incidentDate,
        reportedAt,
        acknowledgedAt,
        resolvedAt,
        closedAt,
        attachments,
        comments,
        involvedPersons,
        equipmentInvolved,
        rootCause,
        correctiveActions,
        preventiveActions,
        isConfidential,
        requiresInvestigation,
        estimatedCost,
        investigatorId,
        investigatorName,
        investigationDeadline,
        customFields,
        metadata,
      ];

  /// Crée une copie de la réclamation avec des modifications
  Claim copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? status,
    String? priority,
    String? severity,
    String? reporterId,
    String? reporterName,
    String? reporterEmail,
    String? reporterPhone,
    String? assignedToId,
    String? assignedToName,
    String? factoryId,
    String? factoryName,
    String? department,
    String? location,
    double? latitude,
    double? longitude,
    DateTime? incidentDate,
    DateTime? reportedAt,
    DateTime? acknowledgedAt,
    DateTime? resolvedAt,
    DateTime? closedAt,
    List<String>? attachments,
    List<ClaimComment>? comments,
    List<String>? involvedPersons,
    List<String>? equipmentInvolved,
    String? rootCause,
    String? correctiveActions,
    String? preventiveActions,
    bool? isConfidential,
    bool? requiresInvestigation,
    int? estimatedCost,
    String? investigatorId,
    String? investigatorName,
    DateTime? investigationDeadline,
    Map<String, dynamic>? customFields,
    Map<String, dynamic>? metadata,
  }) {
    return Claim(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      severity: severity ?? this.severity,
      reporterId: reporterId ?? this.reporterId,
      reporterName: reporterName ?? this.reporterName,
      reporterEmail: reporterEmail ?? this.reporterEmail,
      reporterPhone: reporterPhone ?? this.reporterPhone,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      department: department ?? this.department,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      incidentDate: incidentDate ?? this.incidentDate,
      reportedAt: reportedAt ?? this.reportedAt,
      acknowledgedAt: acknowledgedAt ?? this.acknowledgedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      closedAt: closedAt ?? this.closedAt,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      involvedPersons: involvedPersons ?? this.involvedPersons,
      equipmentInvolved: equipmentInvolved ?? this.equipmentInvolved,
      rootCause: rootCause ?? this.rootCause,
      correctiveActions: correctiveActions ?? this.correctiveActions,
      preventiveActions: preventiveActions ?? this.preventiveActions,
      isConfidential: isConfidential ?? this.isConfidential,
      requiresInvestigation: requiresInvestigation ?? this.requiresInvestigation,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      investigatorId: investigatorId ?? this.investigatorId,
      investigatorName: investigatorName ?? this.investigatorName,
      investigationDeadline: investigationDeadline ?? this.investigationDeadline,
      customFields: customFields ?? this.customFields,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Vérifie si la réclamation est en retard
  bool get isOverdue {
    if (status == 'CLOSED' || status == 'RESOLVED') return false;

    final now = DateTime.now();
    final responseLimit = _getResponseTimeLimit();

    return now.difference(reportedAt).inHours > responseLimit;
  }

  /// Vérifie si la réclamation nécessite une escalade
  bool get needsEscalation {
    return priority == 'CRITICAL' ||
           priority == 'EMERGENCY' ||
           severity == 'CATASTROPHIC' ||
           severity == 'MAJOR' ||
           isOverdue;
  }

  /// Obtient le délai de réponse en heures selon la priorité
  int _getResponseTimeLimit() {
    switch (priority) {
      case 'EMERGENCY':
        return 1;
      case 'CRITICAL':
        return 4;
      case 'HIGH':
        return 24;
      case 'MEDIUM':
        return 72;
      case 'LOW':
        return 168;
      default:
        return 72;
    }
  }

  /// Obtient le délai de résolution en heures selon la priorité
  int get resolutionTimeLimit {
    switch (priority) {
      case 'EMERGENCY':
        return 4;
      case 'CRITICAL':
        return 24;
      case 'HIGH':
        return 72;
      case 'MEDIUM':
        return 168;
      case 'LOW':
        return 336;
      default:
        return 168;
    }
  }

  /// Calcule le temps écoulé depuis le signalement
  Duration get timeElapsed => DateTime.now().difference(reportedAt);

  /// Calcule le temps restant pour la résolution
  Duration get timeRemaining {
    final deadline = reportedAt.add(Duration(hours: resolutionTimeLimit));
    final now = DateTime.now();

    if (deadline.isBefore(now)) {
      return Duration.zero;
    }

    return deadline.difference(now);
  }

  /// Vérifie si la réclamation est fermée
  bool get isClosed => status == 'CLOSED';

  /// Vérifie si la réclamation est résolue
  bool get isResolved => status == 'RESOLVED';

  /// Vérifie si la réclamation est en cours
  bool get isInProgress => status == 'IN_PROGRESS' || status == 'UNDER_INVESTIGATION';

  /// Vérifie si la réclamation est en attente
  bool get isPending => status == 'PENDING';

  /// Obtient le nombre de commentaires publics
  int get publicCommentsCount => comments.where((c) => !c.isInternal).length;

  /// Obtient le nombre de commentaires internes
  int get internalCommentsCount => comments.where((c) => c.isInternal).length;

  /// Obtient le dernier commentaire
  ClaimComment? get lastComment {
    if (comments.isEmpty) return null;
    return comments.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
  }

  /// Vérifie si l'utilisateur peut voir cette réclamation
  bool canBeViewedBy(String userId, String userRole) {
    // Le rapporteur peut toujours voir sa réclamation
    if (reporterId == userId) return true;

    // L'assigné peut voir la réclamation
    if (assignedToId == userId) return true;

    // L'enquêteur peut voir la réclamation
    if (investigatorId == userId) return true;

    // Les admins peuvent voir toutes les réclamations
    if (userRole == 'SUPER_ADMIN' || userRole == 'SECURITY_ADMIN_GCT') return true;

    // Les admins d'usine peuvent voir les réclamations de leur usine
    if (userRole == 'FACTORY_ADMIN') return true; // TODO: vérifier l'usine

    return false;
  }

  /// Vérifie si l'utilisateur peut modifier cette réclamation
  bool canBeEditedBy(String userId, String userRole) {
    // Seuls les admins et l'assigné peuvent modifier
    if (userRole == 'SUPER_ADMIN' || userRole == 'SECURITY_ADMIN_GCT') return true;
    if (assignedToId == userId) return true;
    if (investigatorId == userId) return true;

    return false;
  }
}

class ClaimComment extends Equatable {
  final String id;
  final String claimId;
  final String authorId;
  final String authorName;
  final String content;
  final DateTime createdAt;
  final List<String> attachments;

  const ClaimComment({
    required this.id,
    required this.claimId,
    required this.authorId,
    required this.authorName,
    required this.content,
    required this.createdAt,
    this.attachments = const [],
  });

  bool get hasAttachments => attachments.isNotEmpty;

  ClaimComment copyWith({
    String? id,
    String? claimId,
    String? authorId,
    String? authorName,
    String? content,
    DateTime? createdAt,
    List<String>? attachments,
  }) {
    return ClaimComment(
      id: id ?? this.id,
      claimId: claimId ?? this.claimId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        claimId,
        authorId,
        authorName,
        content,
        createdAt,
        attachments,
      ];
}
