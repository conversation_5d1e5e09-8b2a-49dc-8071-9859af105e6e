{"buildFiles": ["C:\\Mobile\\Flutter_SDK\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Mobile\\Android_SDK\\Android_SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\GCT\\securityapp\\android\\app\\.cxx\\Debug\\2s331i86\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Mobile\\Android_SDK\\Android_SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\GCT\\securityapp\\android\\app\\.cxx\\Debug\\2s331i86\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Mobile\\Android_SDK\\Android_SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}