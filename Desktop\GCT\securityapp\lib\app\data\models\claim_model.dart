import 'package:cloud_firestore/cloud_firestore.dart';

enum ClaimType {
  accident,
  incident,
  nearMiss,
  nonCompliance,
  suggestion,
  hazard,
}

enum ClaimStatus {
  pending,
  inProgress,
  resolved,
  rejected,
  closed,
}

enum ClaimPriority {
  low,
  medium,
  high,
  critical,
}

class ClaimModel {
  final String id;
  final String title;
  final String description;
  final ClaimType type;
  final ClaimStatus status;
  final ClaimPriority priority;
  final String userId;
  final String userEmail;
  final String userName;
  final String factory;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? resolvedAt;
  final String? assignedTo;
  final String? resolution;
  final List<String> attachments;
  final Map<String, dynamic>? location;

  ClaimModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.status = ClaimStatus.pending,
    this.priority = ClaimPriority.medium,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.factory,
    required this.createdAt,
    this.updatedAt,
    this.resolvedAt,
    this.assignedTo,
    this.resolution,
    this.attachments = const [],
    this.location,
  });

  String get typeDisplayName {
    switch (type) {
      case ClaimType.accident:
        return 'Accident';
      case ClaimType.incident:
        return 'Incident';
      case ClaimType.nearMiss:
        return 'Presque accident';
      case ClaimType.nonCompliance:
        return 'Non-conformité';
      case ClaimType.suggestion:
        return 'Suggestion';
      case ClaimType.hazard:
        return 'Danger identifié';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case ClaimStatus.pending:
        return 'En attente';
      case ClaimStatus.inProgress:
        return 'En cours';
      case ClaimStatus.resolved:
        return 'Résolu';
      case ClaimStatus.rejected:
        return 'Rejeté';
      case ClaimStatus.closed:
        return 'Fermé';
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case ClaimPriority.low:
        return 'Faible';
      case ClaimPriority.medium:
        return 'Moyenne';
      case ClaimPriority.high:
        return 'Élevée';
      case ClaimPriority.critical:
        return 'Critique';
    }
  }

  bool get isResolved => status == ClaimStatus.resolved;
  bool get isPending => status == ClaimStatus.pending;
  bool get isInProgress => status == ClaimStatus.inProgress;

  // Conversion vers Map pour Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'priority': priority.name,
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'factory': factory,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'resolvedAt': resolvedAt,
      'assignedTo': assignedTo,
      'resolution': resolution,
      'attachments': attachments,
      'location': location,
    };
  }

  // Création depuis Map Firestore
  factory ClaimModel.fromMap(Map<String, dynamic> map) {
    return ClaimModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: ClaimType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ClaimType.incident,
      ),
      status: ClaimStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ClaimStatus.pending,
      ),
      priority: ClaimPriority.values.firstWhere(
        (e) => e.name == map['priority'],
        orElse: () => ClaimPriority.medium,
      ),
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      userName: map['userName'] ?? '',
      factory: map['factory'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      resolvedAt: (map['resolvedAt'] as Timestamp?)?.toDate(),
      assignedTo: map['assignedTo'],
      resolution: map['resolution'],
      attachments: List<String>.from(map['attachments'] ?? []),
      location: map['location'],
    );
  }

  // Création depuis DocumentSnapshot
  factory ClaimModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ClaimModel.fromMap({...data, 'id': doc.id});
  }

  // Copie avec modifications
  ClaimModel copyWith({
    String? id,
    String? title,
    String? description,
    ClaimType? type,
    ClaimStatus? status,
    ClaimPriority? priority,
    String? userId,
    String? userEmail,
    String? userName,
    String? factory,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? resolvedAt,
    String? assignedTo,
    String? resolution,
    List<String>? attachments,
    Map<String, dynamic>? location,
  }) {
    return ClaimModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      factory: factory ?? this.factory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      resolution: resolution ?? this.resolution,
      attachments: attachments ?? this.attachments,
      location: location ?? this.location,
    );
  }

  @override
  String toString() {
    return 'ClaimModel(id: $id, title: $title, type: $type, status: $status, factory: $factory)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClaimModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
