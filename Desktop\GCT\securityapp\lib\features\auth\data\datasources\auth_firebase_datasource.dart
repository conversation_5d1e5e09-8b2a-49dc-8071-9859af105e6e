import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';
import 'auth_demo_datasource.dart';

abstract class AuthFirebaseDataSource {
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<void> signOut();

  Future<UserModel?> getCurrentUser();

  Future<UserModel> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? factoryId,
    String? phoneNumber,
  });

  Future<void> updateUserProfile(UserModel user);

  Future<void> sendPasswordResetEmail(String email);

  Stream<User?> get authStateChanges;
}

class AuthFirebaseDataSourceImpl implements AuthFirebaseDataSource {
  final FirebaseAuth _auth = FirebaseService.auth;
  final FirebaseFirestore _firestore = FirebaseService.firestore;

  // Use demo mode in debug builds
  static const bool _useDemoMode = kDebugMode;

  @override
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (_useDemoMode) {
      return await AuthDemoDataSource.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    }

    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw const AuthenticationException(message: 'Échec de la connexion');
      }

      // Get user data from Firestore
      final userDoc = await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(credential.user!.uid)
          .get();

      if (!userDoc.exists) {
        throw const AuthenticationException(message: 'Données utilisateur non trouvées');
      }

      final userData = userDoc.data()!;
      userData['id'] = credential.user!.uid;

      // Update last login
      await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(credential.user!.uid)
          .update({
        'lastLoginAt': FieldValue.serverTimestamp(),
      });

      return UserModel.fromJson(userData);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<void> signOut() async {
    if (_useDemoMode) {
      return await AuthDemoDataSource.signOut();
    }

    try {
      await _auth.signOut();
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    if (_useDemoMode) {
      return await AuthDemoDataSource.getCurrentUser();
    }

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final userDoc = await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return null;

      final userData = userDoc.data()!;
      userData['id'] = currentUser.uid;

      return UserModel.fromJson(userData);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<UserModel> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? factoryId,
    String? phoneNumber,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw const AuthenticationException(message: 'Échec de la création du compte');
      }

      // Create user document in Firestore
      final userData = {
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': phoneNumber,
        'role': role,
        'factoryId': factoryId,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(credential.user!.uid)
          .set(userData);

      // Get factory name if factoryId is provided
      String? factoryName;
      if (factoryId != null) {
        final factoryDoc = await _firestore
            .collection(FirebaseService.factoriesCollection)
            .doc(factoryId)
            .get();
        if (factoryDoc.exists) {
          factoryName = factoryDoc.data()?['name'];
        }
      }

      final userModel = UserModel(
        id: credential.user!.uid,
        email: email,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        role: role,
        factoryId: factoryId,
        factoryName: factoryName,
        isActive: true,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      return userModel;
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<void> updateUserProfile(UserModel user) async {
    try {
      final userData = user.toJson();
      userData.remove('id'); // Remove ID from update data
      userData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(user.id)
          .update(userData);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  @override
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Helper method to check if user exists in Firestore
  Future<bool> userExistsInFirestore(String uid) async {
    try {
      final doc = await _firestore
          .collection(FirebaseService.usersCollection)
          .doc(uid)
          .get();
      return doc.exists;
    } catch (e) {
      return false;
    }
  }

  // Helper method to get user by email
  Future<UserModel?> getUserByEmail(String email) async {
    try {
      final querySnapshot = await _firestore
          .collection(FirebaseService.usersCollection)
          .where('email', isEqualTo: email)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) return null;

      final userData = querySnapshot.docs.first.data();
      userData['id'] = querySnapshot.docs.first.id;

      return UserModel.fromJson(userData);
    } catch (e) {
      throw FirebaseService.handleFirebaseError(e);
    }
  }

  // Helper method to update FCM token
  Future<void> updateFCMToken(String userId, String? token) async {
    try {
      if (token != null) {
        await _firestore
            .collection(FirebaseService.usersCollection)
            .doc(userId)
            .update({
          'fcmToken': token,
          'fcmTokenUpdatedAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      // Ignore FCM token update errors
    }
  }
}
