import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

// Configuration Firebase pour le web
const firebaseConfig = {
  'apiKey': "AIzaSyDOCAbC123dEf456GhI789jKl01MnO2PqR",
  'authDomain': "securityapp-8b742.firebaseapp.com",
  'projectId': "securityapp-8b742",
  'storageBucket': "securityapp-8b742.appspot.com",
  'messagingSenderId': "325296538989",
  'appId': "1:325296538989:web:a7ba3a5eeedc340115c664"
};

void main() async {
  print('Initialisation de Firebase...');
  
  try {
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: "AIzaSyDOCAbC123dEf456GhI789jKl01MnO2PqR",
        authDomain: "securityapp-8b742.firebaseapp.com",
        projectId: "securityapp-8b742",
        storageBucket: "securityapp-8b742.appspot.com",
        messagingSenderId: "325296538989",
        appId: "1:325296538989:web:a7ba3a5eeedc340115c664",
      ),
    );
    
    print('Firebase initialisé avec succès');
    
    final firestore = FirebaseFirestore.instance;
    
    // Créer quelques données de test simples
    print('Création de données de test...');
    
    // Créer une usine
    await firestore.collection('factories').doc('test_factory').set({
      'id': 'test_factory',
      'name': 'Usine de Test',
      'code': 'TEST',
      'address': 'Adresse de test',
      'city': 'Tunis',
      'region': 'Tunis',
      'country': 'Tunisie',
      'isActive': true,
      'createdAt': Timestamp.fromDate(DateTime.now()),
    });
    
    // Créer un utilisateur
    await firestore.collection('users').doc('test_user').set({
      'id': 'test_user',
      'email': '<EMAIL>',
      'firstName': 'Test',
      'lastName': 'User',
      'role': 'employe',
      'factory': 'Usine de Test',
      'isActive': true,
      'createdAt': Timestamp.fromDate(DateTime.now()),
    });
    
    // Créer une réclamation
    await firestore.collection('claims').doc('test_claim').set({
      'id': 'test_claim',
      'title': 'Réclamation de test',
      'description': 'Description de test',
      'type': 'hazard',
      'status': 'pending',
      'priority': 'medium',
      'userId': 'test_user',
      'userEmail': '<EMAIL>',
      'userName': 'Test User',
      'factory': 'Usine de Test',
      'createdAt': Timestamp.fromDate(DateTime.now()),
      'attachments': [],
    });
    
    // Créer une formation
    await firestore.collection('trainings').doc('test_training').set({
      'id': 'test_training',
      'title': 'Formation de test',
      'description': 'Description de formation',
      'type': 'safety',
      'status': 'pending',
      'userId': 'test_user',
      'userEmail': '<EMAIL>',
      'userName': 'Test User',
      'factory': 'Usine de Test',
      'requestedAt': Timestamp.fromDate(DateTime.now()),
      'durationHours': 8,
      'isMandatory': true,
      'prerequisites': [],
    });
    
    print('Données de test créées avec succès');
    
    // Vérifier les données
    print('\nVérification des données...');
    
    final usersSnapshot = await firestore.collection('users').get();
    print('Utilisateurs: ${usersSnapshot.docs.length}');
    
    final claimsSnapshot = await firestore.collection('claims').get();
    print('Réclamations: ${claimsSnapshot.docs.length}');
    
    final trainingsSnapshot = await firestore.collection('trainings').get();
    print('Formations: ${trainingsSnapshot.docs.length}');
    
    final factoriesSnapshot = await firestore.collection('factories').get();
    print('Usines: ${factoriesSnapshot.docs.length}');
    
    print('\nTest terminé avec succès!');
    
  } catch (e) {
    print('Erreur: $e');
  }
  
  exit(0);
}
