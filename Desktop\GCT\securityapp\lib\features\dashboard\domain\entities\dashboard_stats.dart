import 'package:equatable/equatable.dart';

class DashboardStats extends Equatable {
  final OverallStats overall;
  final ClaimsStats claims;
  final TrainingStats training;
  final SafetyStats safety;
  final List<TrendData> trends;
  final DateTime lastUpdated;

  const DashboardStats({
    required this.overall,
    required this.claims,
    required this.training,
    required this.safety,
    this.trends = const [],
    required this.lastUpdated,
  });

  bool get hasTrends => trends.isNotEmpty;

  DashboardStats copyWith({
    OverallStats? overall,
    ClaimsStats? claims,
    TrainingStats? training,
    SafetyStats? safety,
    List<TrendData>? trends,
    DateTime? lastUpdated,
  }) {
    return DashboardStats(
      overall: overall ?? this.overall,
      claims: claims ?? this.claims,
      training: training ?? this.training,
      safety: safety ?? this.safety,
      trends: trends ?? this.trends,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  List<Object?> get props => [
        overall,
        claims,
        training,
        safety,
        trends,
        lastUpdated,
      ];
}

class OverallStats extends Equatable {
  final int totalFactories;
  final int totalEmployees;
  final int activeUsers;
  final double overallSafetyScore;
  final int daysSinceLastIncident;
  final DateTime? lastIncidentDate;

  const OverallStats({
    required this.totalFactories,
    required this.totalEmployees,
    required this.activeUsers,
    required this.overallSafetyScore,
    required this.daysSinceLastIncident,
    this.lastIncidentDate,
  });

  bool get hasRecentIncident => daysSinceLastIncident < 7;
  bool get isHighSafety => overallSafetyScore >= 80;
  bool get isLowSafety => overallSafetyScore < 60;

  OverallStats copyWith({
    int? totalFactories,
    int? totalEmployees,
    int? activeUsers,
    double? overallSafetyScore,
    int? daysSinceLastIncident,
    DateTime? lastIncidentDate,
  }) {
    return OverallStats(
      totalFactories: totalFactories ?? this.totalFactories,
      totalEmployees: totalEmployees ?? this.totalEmployees,
      activeUsers: activeUsers ?? this.activeUsers,
      overallSafetyScore: overallSafetyScore ?? this.overallSafetyScore,
      daysSinceLastIncident: daysSinceLastIncident ?? this.daysSinceLastIncident,
      lastIncidentDate: lastIncidentDate ?? this.lastIncidentDate,
    );
  }

  @override
  List<Object?> get props => [
        totalFactories,
        totalEmployees,
        activeUsers,
        overallSafetyScore,
        daysSinceLastIncident,
        lastIncidentDate,
      ];
}

class ClaimsStats extends Equatable {
  final int totalClaims;
  final int pendingClaims;
  final int inProgressClaims;
  final int resolvedClaims;
  final int closedClaims;
  final Map<String, int> claimsByType;
  final Map<String, int> claimsByPriority;
  final Map<String, int> claimsByFactory;
  final double averageResolutionTime; // in hours
  final int claimsThisMonth;
  final int claimsLastMonth;

  const ClaimsStats({
    required this.totalClaims,
    required this.pendingClaims,
    required this.inProgressClaims,
    required this.resolvedClaims,
    required this.closedClaims,
    this.claimsByType = const {},
    this.claimsByPriority = const {},
    this.claimsByFactory = const {},
    required this.averageResolutionTime,
    required this.claimsThisMonth,
    required this.claimsLastMonth,
  });

  double get resolutionRate {
    if (totalClaims == 0) return 0.0;
    return (resolvedClaims / totalClaims) * 100;
  }

  double get monthlyGrowthRate {
    if (claimsLastMonth == 0) return 0.0;
    return ((claimsThisMonth - claimsLastMonth) / claimsLastMonth) * 100;
  }

  bool get isIncreasing => claimsThisMonth > claimsLastMonth;
  bool get isDecreasing => claimsThisMonth < claimsLastMonth;

  ClaimsStats copyWith({
    int? totalClaims,
    int? pendingClaims,
    int? inProgressClaims,
    int? resolvedClaims,
    int? closedClaims,
    Map<String, int>? claimsByType,
    Map<String, int>? claimsByPriority,
    Map<String, int>? claimsByFactory,
    double? averageResolutionTime,
    int? claimsThisMonth,
    int? claimsLastMonth,
  }) {
    return ClaimsStats(
      totalClaims: totalClaims ?? this.totalClaims,
      pendingClaims: pendingClaims ?? this.pendingClaims,
      inProgressClaims: inProgressClaims ?? this.inProgressClaims,
      resolvedClaims: resolvedClaims ?? this.resolvedClaims,
      closedClaims: closedClaims ?? this.closedClaims,
      claimsByType: claimsByType ?? this.claimsByType,
      claimsByPriority: claimsByPriority ?? this.claimsByPriority,
      claimsByFactory: claimsByFactory ?? this.claimsByFactory,
      averageResolutionTime: averageResolutionTime ?? this.averageResolutionTime,
      claimsThisMonth: claimsThisMonth ?? this.claimsThisMonth,
      claimsLastMonth: claimsLastMonth ?? this.claimsLastMonth,
    );
  }

  @override
  List<Object?> get props => [
        totalClaims,
        pendingClaims,
        inProgressClaims,
        resolvedClaims,
        closedClaims,
        claimsByType,
        claimsByPriority,
        claimsByFactory,
        averageResolutionTime,
        claimsThisMonth,
        claimsLastMonth,
      ];
}

class TrainingStats extends Equatable {
  final int totalTrainings;
  final int completedTrainings;
  final int inProgressTrainings;
  final int expiredTrainings;
  final int overdueTrainings;
  final Map<String, int> trainingsByCategory;
  final Map<String, int> trainingsByFactory;
  final double averageCompletionRate;
  final int trainingsThisMonth;
  final int trainingsLastMonth;

  const TrainingStats({
    required this.totalTrainings,
    required this.completedTrainings,
    required this.inProgressTrainings,
    required this.expiredTrainings,
    required this.overdueTrainings,
    this.trainingsByCategory = const {},
    this.trainingsByFactory = const {},
    required this.averageCompletionRate,
    required this.trainingsThisMonth,
    required this.trainingsLastMonth,
  });

  double get completionRate {
    if (totalTrainings == 0) return 0.0;
    return (completedTrainings / totalTrainings) * 100;
  }

  double get monthlyGrowthRate {
    if (trainingsLastMonth == 0) return 0.0;
    return ((trainingsThisMonth - trainingsLastMonth) / trainingsLastMonth) * 100;
  }

  bool get hasOverdueTrainings => overdueTrainings > 0;
  bool get hasExpiredTrainings => expiredTrainings > 0;

  TrainingStats copyWith({
    int? totalTrainings,
    int? completedTrainings,
    int? inProgressTrainings,
    int? expiredTrainings,
    int? overdueTrainings,
    Map<String, int>? trainingsByCategory,
    Map<String, int>? trainingsByFactory,
    double? averageCompletionRate,
    int? trainingsThisMonth,
    int? trainingsLastMonth,
  }) {
    return TrainingStats(
      totalTrainings: totalTrainings ?? this.totalTrainings,
      completedTrainings: completedTrainings ?? this.completedTrainings,
      inProgressTrainings: inProgressTrainings ?? this.inProgressTrainings,
      expiredTrainings: expiredTrainings ?? this.expiredTrainings,
      overdueTrainings: overdueTrainings ?? this.overdueTrainings,
      trainingsByCategory: trainingsByCategory ?? this.trainingsByCategory,
      trainingsByFactory: trainingsByFactory ?? this.trainingsByFactory,
      averageCompletionRate: averageCompletionRate ?? this.averageCompletionRate,
      trainingsThisMonth: trainingsThisMonth ?? this.trainingsThisMonth,
      trainingsLastMonth: trainingsLastMonth ?? this.trainingsLastMonth,
    );
  }

  @override
  List<Object?> get props => [
        totalTrainings,
        completedTrainings,
        inProgressTrainings,
        expiredTrainings,
        overdueTrainings,
        trainingsByCategory,
        trainingsByFactory,
        averageCompletionRate,
        trainingsThisMonth,
        trainingsLastMonth,
      ];
}

class SafetyStats extends Equatable {
  final double overallScore;
  final int totalIncidents;
  final int criticalIncidents;
  final int daysSinceLastIncident;
  final Map<String, double> scoresByFactory;
  final Map<String, int> incidentsByType;
  final List<SafetyAlert> alerts;

  const SafetyStats({
    required this.overallScore,
    required this.totalIncidents,
    required this.criticalIncidents,
    required this.daysSinceLastIncident,
    this.scoresByFactory = const {},
    this.incidentsByType = const {},
    this.alerts = const [],
  });

  bool get hasAlerts => alerts.isNotEmpty;
  bool get hasCriticalIncidents => criticalIncidents > 0;
  bool get isHighRisk => overallScore < 60;
  bool get isLowRisk => overallScore >= 80;

  SafetyStats copyWith({
    double? overallScore,
    int? totalIncidents,
    int? criticalIncidents,
    int? daysSinceLastIncident,
    Map<String, double>? scoresByFactory,
    Map<String, int>? incidentsByType,
    List<SafetyAlert>? alerts,
  }) {
    return SafetyStats(
      overallScore: overallScore ?? this.overallScore,
      totalIncidents: totalIncidents ?? this.totalIncidents,
      criticalIncidents: criticalIncidents ?? this.criticalIncidents,
      daysSinceLastIncident: daysSinceLastIncident ?? this.daysSinceLastIncident,
      scoresByFactory: scoresByFactory ?? this.scoresByFactory,
      incidentsByType: incidentsByType ?? this.incidentsByType,
      alerts: alerts ?? this.alerts,
    );
  }

  @override
  List<Object?> get props => [
        overallScore,
        totalIncidents,
        criticalIncidents,
        daysSinceLastIncident,
        scoresByFactory,
        incidentsByType,
        alerts,
      ];
}

class SafetyAlert extends Equatable {
  final String id;
  final String type;
  final String message;
  final String severity; // LOW, MEDIUM, HIGH, CRITICAL
  final String? factoryId;
  final String? factoryName;
  final DateTime createdAt;
  final bool isRead;

  const SafetyAlert({
    required this.id,
    required this.type,
    required this.message,
    required this.severity,
    this.factoryId,
    this.factoryName,
    required this.createdAt,
    required this.isRead,
  });

  bool get isCritical => severity == 'CRITICAL';
  bool get isHigh => severity == 'HIGH';
  bool get isMedium => severity == 'MEDIUM';
  bool get isLow => severity == 'LOW';

  SafetyAlert copyWith({
    String? id,
    String? type,
    String? message,
    String? severity,
    String? factoryId,
    String? factoryName,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return SafetyAlert(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      severity: severity ?? this.severity,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        message,
        severity,
        factoryId,
        factoryName,
        createdAt,
        isRead,
      ];
}

class TrendData extends Equatable {
  final String label;
  final double value;
  final DateTime date;
  final String type;

  const TrendData({
    required this.label,
    required this.value,
    required this.date,
    required this.type,
  });

  TrendData copyWith({
    String? label,
    double? value,
    DateTime? date,
    String? type,
  }) {
    return TrendData(
      label: label ?? this.label,
      value: value ?? this.value,
      date: date ?? this.date,
      type: type ?? this.type,
    );
  }

  @override
  List<Object?> get props => [label, value, date, type];
}
