import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/date_utils.dart';
import '../../domain/entities/claim.dart';

class ClaimCard extends StatelessWidget {
  final Claim claim;
  final VoidCallback? onTap;
  final VoidCallback? onStatusChange;

  const ClaimCard({
    super.key,
    required this.claim,
    this.onTap,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and priority
              Row(
                children: [
                  Expanded(
                    child: Text(
                      claim.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildPriorityChip(),
                ],
              ),
              const SizedBox(height: 8),
              
              // Description
              Text(
                claim.description,
                style: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              
              // Status and type row
              Row(
                children: [
                  _buildStatusChip(),
                  const SizedBox(width: 8),
                  _buildTypeChip(),
                  const Spacer(),
                  if (claim.hasAttachments)
                    Icon(
                      Icons.attach_file,
                      size: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Footer with factory, reporter and date
              Row(
                children: [
                  Icon(
                    Icons.factory,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      claim.factoryName,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Par ${claim.reporterName}',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AppDateUtils.getRelativeTime(claim.reportedAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  if (claim.hasLocation)
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: AppTheme.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          claim.location,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color color;
    String label;
    
    switch (claim.status) {
      case 'PENDING':
        color = AppTheme.warningColor;
        label = 'En attente';
        break;
      case 'IN_PROGRESS':
        color = AppTheme.infoColor;
        label = 'En cours';
        break;
      case 'RESOLVED':
        color = AppTheme.successColor;
        label = 'Résolu';
        break;
      case 'CLOSED':
        color = AppTheme.textSecondaryColor;
        label = 'Fermé';
        break;
      default:
        color = AppTheme.textSecondaryColor;
        label = claim.status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildTypeChip() {
    String label;
    IconData icon;
    
    switch (claim.type) {
      case 'ACCIDENT':
        label = 'Accident';
        icon = Icons.local_hospital;
        break;
      case 'INCIDENT':
        label = 'Incident';
        icon = Icons.warning;
        break;
      case 'RISK_BEHAVIOR':
        label = 'Comportement à risque';
        icon = Icons.person_off;
        break;
      case 'NEAR_MISS':
        label = 'Presque accident';
        icon = Icons.near_me;
        break;
      default:
        label = claim.type;
        icon = Icons.report;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip() {
    Color color = AppTheme.getPriorityColor(claim.priority);
    String label;
    
    switch (claim.priority) {
      case 'LOW':
        label = 'Faible';
        break;
      case 'MEDIUM':
        label = 'Moyen';
        break;
      case 'HIGH':
        label = 'Élevé';
        break;
      case 'CRITICAL':
        label = 'Critique';
        break;
      default:
        label = claim.priority;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }
}
