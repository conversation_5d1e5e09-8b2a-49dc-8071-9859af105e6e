import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseUserService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== COMPTES PRÉDÉFINIS ====================
  
  static const List<Map<String, dynamic>> _predefinedUsers = [
    {
      'email': '<EMAIL>',
      'password': 'admin123',
      'role': 'super_admin',
      'name': 'Administrateur Principal',
      'factory': 'Siège Social',
      'permissions': ['all'],
      'department': 'Direction Générale',
    },
    {
      'email': '<EMAIL>',
      'password': 'security123',
      'role': 'security_admin',
      'name': 'Responsable Sécurité',
      'factory': 'Toutes les usines',
      'permissions': ['complaints', 'trainings', 'reports'],
      'department': 'Sécurité',
    },
    {
      'email': '<EMAIL>',
      'password': 'gabes123',
      'role': 'factory_admin',
      'name': 'Admin Usine Gabès',
      'factory': 'Usine Gabès',
      'permissions': ['complaints', 'trainings', 'users'],
      'department': 'Administration',
    },
    {
      'email': '<EMAIL>',
      'password': 'sfax123',
      'role': 'factory_admin',
      'name': 'Admin Usine Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings', 'users'],
      'department': 'Administration',
    },
    {
      'email': '<EMAIL>',
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Gabès',
      'factory': 'Usine Gabès',
      'permissions': ['complaints', 'trainings'],
      'department': 'Production',
    },
    {
      'email': '<EMAIL>',
      'password': 'emp123',
      'role': 'employee',
      'name': 'Employé Sfax',
      'factory': 'Usine Sfax',
      'permissions': ['complaints', 'trainings'],
      'department': 'Production',
    },
  ];

  // ==================== INITIALISATION DES COMPTES ====================
  
  /// Créer tous les comptes prédéfinis dans Firebase
  static Future<Map<String, dynamic>> initializePredefinedUsers() async {
    List<String> createdUsers = [];
    List<String> existingUsers = [];
    List<String> errors = [];

    for (var userData in _predefinedUsers) {
      try {
        // Vérifier si l'utilisateur existe déjà
        final methods = await _auth.fetchSignInMethodsForEmail(userData['email']);
        
        if (methods.isEmpty) {
          // Créer l'utilisateur
          final userCredential = await _auth.createUserWithEmailAndPassword(
            email: userData['email'],
            password: userData['password'],
          );

          // Ajouter les informations utilisateur dans Firestore
          await _firestore.collection('users').doc(userCredential.user!.uid).set({
            'email': userData['email'],
            'role': userData['role'],
            'name': userData['name'],
            'factory': userData['factory'],
            'permissions': userData['permissions'],
            'department': userData['department'],
            'createdAt': FieldValue.serverTimestamp(),
            'isActive': true,
            'lastLogin': null,
          });

          createdUsers.add(userData['email']);
        } else {
          existingUsers.add(userData['email']);
        }
      } catch (e) {
        errors.add('${userData['email']}: $e');
      }
    }

    return {
      'success': errors.isEmpty,
      'created': createdUsers,
      'existing': existingUsers,
      'errors': errors,
      'total': _predefinedUsers.length,
    };
  }

  // ==================== AUTHENTIFICATION ====================
  
  /// Connexion utilisateur
  static Future<Map<String, dynamic>> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Récupérer les informations utilisateur depuis Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .get();

      if (!userDoc.exists) {
        return {
          'success': false,
          'message': 'Profil utilisateur non trouvé',
        };
      }

      final userData = userDoc.data()!;

      // Mettre à jour la dernière connexion
      await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .update({'lastLogin': FieldValue.serverTimestamp()});

      return {
        'success': true,
        'message': 'Connexion réussie',
        'user': {
          'uid': userCredential.user!.uid,
          'email': userData['email'],
          'name': userData['name'],
          'role': userData['role'],
          'factory': userData['factory'],
          'permissions': userData['permissions'],
          'department': userData['department'],
        },
      };
    } on FirebaseAuthException catch (e) {
      String message;
      switch (e.code) {
        case 'user-not-found':
          message = 'Aucun utilisateur trouvé avec cet email';
          break;
        case 'wrong-password':
          message = 'Mot de passe incorrect';
          break;
        case 'invalid-email':
          message = 'Format email invalide';
          break;
        case 'user-disabled':
          message = 'Ce compte a été désactivé';
          break;
        default:
          message = 'Erreur de connexion: ${e.message}';
      }
      return {
        'success': false,
        'message': message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur inattendue: $e',
      };
    }
  }

  /// Déconnexion
  static Future<void> signOut() async {
    await _auth.signOut();
  }

  /// Utilisateur actuel
  static User? get currentUser => _auth.currentUser;

  /// Stream de l'état d'authentification
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // ==================== GESTION DES UTILISATEURS ====================
  
  /// Récupérer les informations d'un utilisateur
  static Future<Map<String, dynamic>?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      return doc.exists ? doc.data() : null;
    } catch (e) {
      return null;
    }
  }

  /// Lister tous les utilisateurs (pour admin)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Vérifier les permissions
  static Future<bool> hasPermission(String uid, String permission) async {
    try {
      final userData = await getUserData(uid);
      if (userData == null) return false;
      
      final permissions = List<String>.from(userData['permissions'] ?? []);
      return permissions.contains('all') || permissions.contains(permission);
    } catch (e) {
      return false;
    }
  }

  /// Obtenir les statistiques des utilisateurs
  static Future<Map<String, int>> getUserStats() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      final users = snapshot.docs.map((doc) => doc.data()).toList();
      
      return {
        'total': users.length,
        'super_admin': users.where((u) => u['role'] == 'super_admin').length,
        'security_admin': users.where((u) => u['role'] == 'security_admin').length,
        'factory_admin': users.where((u) => u['role'] == 'factory_admin').length,
        'employee': users.where((u) => u['role'] == 'employee').length,
        'active': users.where((u) => u['isActive'] == true).length,
      };
    } catch (e) {
      return {};
    }
  }
}
