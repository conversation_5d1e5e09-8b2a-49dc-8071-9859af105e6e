enum UserRole {
  superAdmin,
  securityAdmin,
  factoryAdmin,
  employee,
}

class UserModel {
  final String id;
  final String email;
  final String name;
  final UserRole role;
  final String? factoryId;
  final String? factoryName;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    this.factoryId,
    this.factoryName,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
  });

  String get roleDisplayName {
    switch (role) {
      case UserRole.superAdmin:
        return 'Super Administrateur';
      case UserRole.securityAdmin:
        return 'Admin Sécurité GCT';
      case UserRole.factoryAdmin:
        return 'Admin d\'Usine';
      case UserRole.employee:
        return 'Employé';
    }
  }

  bool get canManageAllFactories => role == UserRole.superAdmin || role == UserRole.securityAdmin;
  bool get canManageUsers => role == UserRole.superAdmin;
  bool get canApproveTrainings => role == UserRole.securityAdmin || role == UserRole.factoryAdmin;
  bool get canViewAllClaims => role == UserRole.superAdmin || role == UserRole.securityAdmin;

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      role: UserRole.values.firstWhere(
        (e) => e.toString().split('.').last == json['role'],
        orElse: () => UserRole.employee,
      ),
      factoryId: json['factoryId'],
      factoryName: json['factoryName'],
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : DateTime.now(),
      lastLogin: json['lastLogin'] != null ? DateTime.parse(json['lastLogin']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role.toString().split('.').last,
      'factoryId': factoryId,
      'factoryName': factoryName,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    UserRole? role,
    String? factoryId,
    String? factoryName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      factoryId: factoryId ?? this.factoryId,
      factoryName: factoryName ?? this.factoryName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }


}
